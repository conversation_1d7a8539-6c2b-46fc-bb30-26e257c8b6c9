"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[329],{2854:(e,t,n)=>{n.d(t,{UC:()=>eg,Y9:()=>ey,q7:()=>ev,bL:()=>em,l9:()=>eh});var r,o=n(2115),l=n.t(o,2),i=n(5155);function a(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let l=o.createContext(r),a=n.length;n=[...n,r];let u=t=>{let{scope:n,children:r,...u}=t,c=n?.[e]?.[a]||l,s=o.useMemo(()=>u,Object.values(u));return(0,i.jsx)(c.Provider,{value:s,children:r})};return u.displayName=t+"Provider",[u,function(n,i){let u=i?.[e]?.[a]||l,c=o.useContext(u);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}function u(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function c(e,t){var n=u(e,t,"get");return n.get?n.get.call(e):n.value}function s(e,t,n){var r=u(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var d=n(6101),f=n(9708),p=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=v(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function v(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap;function y(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var h=globalThis?.document?o.useLayoutEffect:()=>{},g=l[" useInsertionEffect ".trim().toString()]||h;function w({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[l,i,a]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),l=o.useRef(n),i=o.useRef(t);return g(()=>{i.current=t},[t]),o.useEffect(()=>{l.current!==n&&(i.current?.(n),l.current=n)},[n,l]),[n,r,i]}({defaultProp:t,onChange:n}),u=void 0!==e,c=u?e:l;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==u){let t=u?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=u},[u,r])}return[c,o.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else i(t)},[u,e,i,a])]}Symbol("RADIX:SYNC_STATE");var b=n(3655),x=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,l]=o.useState(),i=o.useRef(null),a=o.useRef(e),u=o.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=N(i.current);u.current="mounted"===c?e:"none"},[c]),h(()=>{let t=i.current,n=a.current;if(n!==e){let r=u.current,o=N(t);e?s("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),a.current=e}},[e,s]),h(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=N(i.current).includes(e.animationName);if(e.target===r&&o&&(s("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},l=e=>{e.target===r&&(u.current=N(i.current))};return r.addEventListener("animationstart",l),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",l),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}s("ANIMATION_END")},[r,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:o.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(t),l="function"==typeof n?n({present:r.isPresent}):o.Children.only(n),i=(0,d.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof n||r.isPresent?o.cloneElement(l,{ref:i}):null};function N(e){return(null==e?void 0:e.animationName)||"none"}x.displayName="Presence";var R=l[" useId ".trim().toString()]||(()=>void 0),C=0;function A(e){let[t,n]=o.useState(R());return h(()=>{e||n(e=>e??String(C++))},[e]),e||(t?`radix-${t}`:"")}var j="Collapsible",[k,M]=a(j),[_,E]=k(j),I=o.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:r,defaultOpen:l,disabled:a,onOpenChange:u,...c}=e,[s,d]=w({prop:r,defaultProp:null!=l&&l,onChange:u,caller:j});return(0,i.jsx)(_,{scope:n,disabled:a,contentId:A(),open:s,onOpenToggle:o.useCallback(()=>d(e=>!e),[d]),children:(0,i.jsx)(b.sG.div,{"data-state":L(s),"data-disabled":a?"":void 0,...c,ref:t})})});I.displayName=j;var O="CollapsibleTrigger",T=o.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,o=E(O,n);return(0,i.jsx)(b.sG.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":L(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...r,ref:t,onClick:y(e.onClick,o.onOpenToggle)})});T.displayName=O;var S="CollapsibleContent",D=o.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=E(S,e.__scopeCollapsible);return(0,i.jsx)(x,{present:n||o.open,children:e=>{let{present:n}=e;return(0,i.jsx)(P,{...r,ref:t,present:n})}})});D.displayName=S;var P=o.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:r,children:l,...a}=e,u=E(S,n),[c,s]=o.useState(r),f=o.useRef(null),p=(0,d.s)(t,f),m=o.useRef(0),v=m.current,y=o.useRef(0),g=y.current,w=u.open||c,x=o.useRef(w),N=o.useRef(void 0);return o.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),h(()=>{let e=f.current;if(e){N.current=N.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();m.current=t.height,y.current=t.width,x.current||(e.style.transitionDuration=N.current.transitionDuration,e.style.animationName=N.current.animationName),s(r)}},[u.open,r]),(0,i.jsx)(b.sG.div,{"data-state":L(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!w,...a,ref:p,style:{"--radix-collapsible-content-height":v?"".concat(v,"px"):void 0,"--radix-collapsible-content-width":g?"".concat(g,"px"):void 0,...e.style},children:w&&l})});function L(e){return e?"open":"closed"}var U=o.createContext(void 0),$="Accordion",W=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[F,G,q]=function(e){let t=e+"CollectionProvider",[n,r]=a(t),[l,u]=n(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:n}=e,r=o.useRef(null),a=o.useRef(new Map).current;return(0,i.jsx)(l,{scope:t,itemMap:a,collectionRef:r,children:n})};c.displayName=t;let s=e+"CollectionSlot",p=(0,f.TL)(s),m=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=u(s,n),l=(0,d.s)(t,o.collectionRef);return(0,i.jsx)(p,{ref:l,children:r})});m.displayName=s;let v=e+"CollectionItemSlot",y="data-radix-collection-item",h=(0,f.TL)(v),g=o.forwardRef((e,t)=>{let{scope:n,children:r,...l}=e,a=o.useRef(null),c=(0,d.s)(t,a),s=u(v,n);return o.useEffect(()=>(s.itemMap.set(a,{ref:a,...l}),()=>void s.itemMap.delete(a))),(0,i.jsx)(h,{...{[y]:""},ref:c,children:r})});return g.displayName=v,[{Provider:c,Slot:m,ItemSlot:g},function(t){let n=u(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(y,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}($),[V,z]=a($,[q,M]),H=M(),B=o.forwardRef((e,t)=>{let{type:n,...r}=e;return(0,i.jsx)(F.Provider,{scope:e.__scopeAccordion,children:"multiple"===n?(0,i.jsx)(Q,{...r,ref:t}):(0,i.jsx)(J,{...r,ref:t})})});B.displayName=$;var[K,X]=V($),[Y,Z]=V($,{collapsible:!1}),J=o.forwardRef((e,t)=>{let{value:n,defaultValue:r,onValueChange:l=()=>{},collapsible:a=!1,...u}=e,[c,s]=w({prop:n,defaultProp:null!=r?r:"",onChange:l,caller:$});return(0,i.jsx)(K,{scope:e.__scopeAccordion,value:o.useMemo(()=>c?[c]:[],[c]),onItemOpen:s,onItemClose:o.useCallback(()=>a&&s(""),[a,s]),children:(0,i.jsx)(Y,{scope:e.__scopeAccordion,collapsible:a,children:(0,i.jsx)(en,{...u,ref:t})})})}),Q=o.forwardRef((e,t)=>{let{value:n,defaultValue:r,onValueChange:l=()=>{},...a}=e,[u,c]=w({prop:n,defaultProp:null!=r?r:[],onChange:l,caller:$}),s=o.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[c]),d=o.useCallback(e=>c(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[c]);return(0,i.jsx)(K,{scope:e.__scopeAccordion,value:u,onItemOpen:s,onItemClose:d,children:(0,i.jsx)(Y,{scope:e.__scopeAccordion,collapsible:!0,children:(0,i.jsx)(en,{...a,ref:t})})})}),[ee,et]=V($),en=o.forwardRef((e,t)=>{let{__scopeAccordion:n,disabled:r,dir:l,orientation:a="vertical",...u}=e,c=o.useRef(null),s=(0,d.s)(c,t),f=G(n),p="ltr"===function(e){let t=o.useContext(U);return e||t||"ltr"}(l),m=y(e.onKeyDown,e=>{var t;if(!W.includes(e.key))return;let n=e.target,r=f().filter(e=>{var t;return!(null==(t=e.ref.current)?void 0:t.disabled)}),o=r.findIndex(e=>e.ref.current===n),l=r.length;if(-1===o)return;e.preventDefault();let i=o,u=l-1,c=()=>{(i=o+1)>u&&(i=0)},s=()=>{(i=o-1)<0&&(i=u)};switch(e.key){case"Home":i=0;break;case"End":i=u;break;case"ArrowRight":"horizontal"===a&&(p?c():s());break;case"ArrowDown":"vertical"===a&&c();break;case"ArrowLeft":"horizontal"===a&&(p?s():c());break;case"ArrowUp":"vertical"===a&&s()}null==(t=r[i%l].ref.current)||t.focus()});return(0,i.jsx)(ee,{scope:n,disabled:r,direction:l,orientation:a,children:(0,i.jsx)(F.Slot,{scope:n,children:(0,i.jsx)(b.sG.div,{...u,"data-orientation":a,ref:s,onKeyDown:r?void 0:m})})})}),er="AccordionItem",[eo,el]=V(er),ei=o.forwardRef((e,t)=>{let{__scopeAccordion:n,value:r,...o}=e,l=et(er,n),a=X(er,n),u=H(n),c=A(),s=r&&a.value.includes(r)||!1,d=l.disabled||e.disabled;return(0,i.jsx)(eo,{scope:n,open:s,disabled:d,triggerId:c,children:(0,i.jsx)(I,{"data-orientation":l.orientation,"data-state":ep(s),...u,...o,ref:t,disabled:d,open:s,onOpenChange:e=>{e?a.onItemOpen(r):a.onItemClose(r)}})})});ei.displayName=er;var ea="AccordionHeader",eu=o.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=et($,n),l=el(ea,n);return(0,i.jsx)(b.sG.h3,{"data-orientation":o.orientation,"data-state":ep(l.open),"data-disabled":l.disabled?"":void 0,...r,ref:t})});eu.displayName=ea;var ec="AccordionTrigger",es=o.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=et($,n),l=el(ec,n),a=Z(ec,n),u=H(n);return(0,i.jsx)(F.ItemSlot,{scope:n,children:(0,i.jsx)(T,{"aria-disabled":l.open&&!a.collapsible||void 0,"data-orientation":o.orientation,id:l.triggerId,...u,...r,ref:t})})});es.displayName=ec;var ed="AccordionContent",ef=o.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,o=et($,n),l=el(ed,n),a=H(n);return(0,i.jsx)(D,{role:"region","aria-labelledby":l.triggerId,"data-orientation":o.orientation,...a,...r,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function ep(e){return e?"open":"closed"}ef.displayName=ed;var em=B,ev=ei,ey=eu,eh=es,eg=ef},3655:(e,t,n)=>{n.d(t,{sG:()=>i});var r=n(2115);n(7650);var o=n(9708),l=n(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),i=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?n:t,{...i,ref:r})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{})},4631:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},6101:(e,t,n)=>{n.d(t,{s:()=>i,t:()=>l});var r=n(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function i(...e){return r.useCallback(l(...e),e)}},6474:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},9708:(e,t,n)=>{n.d(t,{DX:()=>a,TL:()=>i});var r=n(2115),o=n(6101),l=n(5155);function i(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...l}=e;if(r.isValidElement(n)){var i;let e,a,u=(i=n,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let n={...t};for(let r in t){let o=e[r],l=t[r];/^on[A-Z]/.test(r)?o&&l?n[r]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(n[r]=o):"style"===r?n[r]={...o,...l}:"className"===r&&(n[r]=[o,l].filter(Boolean).join(" "))}return{...e,...n}}(l,n.props);return n.type!==r.Fragment&&(c.ref=t?(0,o.t)(t,u):u),r.cloneElement(n,c)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,a=r.Children.toArray(o),u=a.find(c);if(u){let e=u.props.children,o=a.map(t=>t!==u?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,l.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}var a=i("Slot"),u=Symbol("radix.slottable");function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===u}}}]);