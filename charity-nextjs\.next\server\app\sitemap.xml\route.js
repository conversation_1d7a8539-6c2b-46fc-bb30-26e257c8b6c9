(()=>{var e={};e.id=475,e.ids=[475],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79205:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>y});var a={};r.r(a),r.d(a,{GET:()=>c});var i=r(96559),o=r(48088),n=r(37719),l=r(32190);let s=process.env.BACKEND_URL||"http://localhost:5000",u=process.env.NEXT_PUBLIC_SITE_URL||"http://localhost:3000";async function c(){try{let[e,t]=await Promise.allSettled([fetch(`${s}/api/news?limit=1000`),fetch(`${s}/api/gallery/albums`)]),r=[],a=[];"fulfilled"===e.status&&e.value.ok&&(r=(await e.value.json()).news||[]),"fulfilled"===t.status&&t.value.ok&&(a=(await t.value.json()).albums||[]);let i=[{url:u,lastModified:new Date().toISOString(),changeFrequency:"daily",priority:1},{url:`${u}/about`,lastModified:new Date().toISOString(),changeFrequency:"monthly",priority:.8},{url:`${u}/contact`,lastModified:new Date().toISOString(),changeFrequency:"monthly",priority:.7},{url:`${u}/faq`,lastModified:new Date().toISOString(),changeFrequency:"monthly",priority:.6},{url:`${u}/news`,lastModified:new Date().toISOString(),changeFrequency:"daily",priority:.9},{url:`${u}/gallery`,lastModified:new Date().toISOString(),changeFrequency:"weekly",priority:.8}],o=r.map(e=>({url:`${u}/news/${e.slug}`,lastModified:e.publishDate||new Date().toISOString(),changeFrequency:"monthly",priority:.7})),n=a.map(e=>({url:`${u}/gallery/${e.slug}`,lastModified:new Date().toISOString(),changeFrequency:"monthly",priority:.6})),c=[...i,...o,...n],p=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${c.map(e=>`  <url>
    <loc>${e.url}</loc>
    <lastmod>${e.lastModified}</lastmod>
    <changefreq>${e.changeFrequency}</changefreq>
    <priority>${e.priority}</priority>
  </url>`).join("\n")}
</urlset>`;return new l.NextResponse(p,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600, stale-while-revalidate=86400"}})}catch(t){let e=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${u}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${u}/about</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${u}/contact</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>
  <url>
    <loc>${u}/news</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>
  <url>
    <loc>${u}/gallery</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>
</urlset>`;return new l.NextResponse(e,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600"}})}}let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"route",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\sitemap.xml\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:d,workUnitAsyncStorage:y,serverHooks:m}=p;function h(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:y})}},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,580],()=>r(79205));module.exports=a})();