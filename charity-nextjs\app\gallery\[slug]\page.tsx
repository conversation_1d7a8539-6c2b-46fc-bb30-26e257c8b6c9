import { Metadata } from 'next';
import { GalleryDetailPageContent } from './GalleryDetailPageContent';

type Props = {
  params: { slug: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  // Use slug for potential future metadata customization
  console.log('Generating metadata for album:', params.slug);

  return {
    title: `Gallery Album - Charity Welcome Hub`,
    description: 'View photos from our charity events and community activities.',
    keywords: ['gallery', 'album', 'photos', 'charity events', 'community'],
    openGraph: {
      title: `Gallery Album - Charity Welcome Hub`,
      description: 'View photos from our charity events and community activities.',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `Gallery Album - Charity Welcome Hub`,
      description: 'View photos from our charity events and community activities.',
    },
  };
}

export default function GalleryDetailPage({ params }: Props) {
  return <GalleryDetailPageContent slug={params.slug} />;
}
