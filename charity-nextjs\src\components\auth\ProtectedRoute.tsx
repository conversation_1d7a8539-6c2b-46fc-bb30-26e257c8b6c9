'use client';

import React, { useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: "editor" | "super-admin";
}

export function ProtectedRoute({ 
  children, 
  requiredRole 
}: ProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    // Only run checks after loading is complete
    if (isLoading) return;

    // Redirect to login if not authenticated
    if (!isAuthenticated) {
      toast.error("Authentication required", {
        description: "Please login to access this area",
      });
      router.push(`/login?redirect=${encodeURIComponent(pathname)}`);
      return;
    }

    // Check for required role
    if (requiredRole && user?.role !== requiredRole && user?.role !== "super-admin") {
      toast.error("Access denied", {
        description: `You need ${requiredRole} permissions to access this area`,
      });
      router.push("/admin");
      return;
    }
  }, [isAuthenticated, isLoading, user, requiredRole, router, pathname]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Verifying your credentials...</span>
      </div>
    );
  }

  // Don't render children until authentication is verified
  if (!isAuthenticated) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Redirecting to login...</span>
      </div>
    );
  }

  // Check for required role
  if (requiredRole && user?.role !== requiredRole && user?.role !== "super-admin") {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Checking permissions...</span>
      </div>
    );
  }

  // User is authenticated and has the required role
  return <>{children}</>;
}
