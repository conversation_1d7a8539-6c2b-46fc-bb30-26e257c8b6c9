import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { QueryProvider } from "@/components/providers/QueryProvider";
import { OrganizationStructuredData, WebsiteStructuredData } from "@/components/seo/StructuredData";
import { WebVitals } from "@/components/performance/WebVitals";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: {
    default: "Charity Welcome Hub - Supporting Communities Through Action",
    template: "%s | Charity Welcome Hub",
  },
  description: "Join our mission to support communities through charitable work, transparency, and meaningful impact. Discover our programs, news, and ways to get involved.",
  keywords: [
    "charity", "community", "support", "donations", "volunteer",
    "nonprofit", "humanitarian", "social impact", "community service",
    "charitable organization", "helping others", "social good"
  ],
  authors: [{ name: "Charity Welcome Hub" }],
  creator: "Charity Welcome Hub",
  publisher: "Charity Welcome Hub",
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "Charity Welcome Hub - Supporting Communities Through Action",
    description: "Join our mission to support communities through charitable work, transparency, and meaningful impact.",
    type: "website",
    locale: "en_US",
    url: '/',
    siteName: "Charity Welcome Hub",
  },
  twitter: {
    card: "summary_large_image",
    title: "Charity Welcome Hub - Supporting Communities Through Action",
    description: "Join our mission to support communities through charitable work, transparency, and meaningful impact.",
    creator: "@CharityWelcomeHub",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        <OrganizationStructuredData
          name="Charity Welcome Hub"
          description="Supporting communities through charitable work, transparency, and meaningful impact"
          url={process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}
          contactPoint={{
            telephone: "******-0123",
            contactType: "customer service",
            email: "<EMAIL>",
          }}
        />
        <WebsiteStructuredData
          name="Charity Welcome Hub"
          description="Supporting communities through charitable work, transparency, and meaningful impact"
          url={process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}
        />
      </head>
      <body className="min-h-screen bg-background font-sans antialiased">
        <WebVitals />
        <QueryProvider>
          <div className="flex flex-col min-h-screen">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
        </QueryProvider>
      </body>
    </html>
  );
}
