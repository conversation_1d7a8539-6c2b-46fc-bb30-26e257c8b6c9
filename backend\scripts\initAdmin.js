require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../models/User');
const config = require('../config/config');

/**
 * Initialize admin user script
 * Creates a super-admin user if none exists
 */
async function initAdmin() {
  try {
    console.log('🚀 Starting admin initialization...');
    
    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(config.mongoURI, config.mongoOptions);
    console.log('✅ Connected to MongoDB');

    // Check if any super-admin exists
    const existingAdmin = await User.findOne({ role: 'super-admin' });
    
    if (existingAdmin) {
      console.log('⚠️  Super-admin user already exists:', existingAdmin.username);
      console.log('🔐 If you need to reset the password, please delete the user first or use the update functionality');
      return;
    }

    // Get admin credentials from command line arguments or use defaults
    const username = process.argv[2] || 'admin';
    const password = process.argv[3] || 'admin123';

    console.log('👤 Creating super-admin user...');
    console.log('📝 Username:', username);
    console.log('🔑 Password:', password.replace(/./g, '*')); // Hide password in logs

    // Create admin user
    const adminUser = new User({
      username: username,
      passwordHash: password, // This will be hashed by the pre-save hook
      role: 'super-admin'
    });

    await adminUser.save();
    
    console.log('✅ Super-admin user created successfully!');
    console.log('🎉 You can now login with:');
    console.log('   Username:', username);
    console.log('   Password:', password);
    console.log('');
    console.log('🔒 Please change the password after first login for security!');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
    
    if (error.code === 11000) {
      console.error('💡 Username already exists. Please choose a different username.');
    }
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log('📡 Database connection closed');
    process.exit(0);
  }
}

// Run the script
if (require.main === module) {
  console.log('');
  console.log('='.repeat(50));
  console.log('🏥 CHARITY INFO - ADMIN INITIALIZATION');
  console.log('='.repeat(50));
  console.log('');
  
  initAdmin();
}

module.exports = initAdmin;
