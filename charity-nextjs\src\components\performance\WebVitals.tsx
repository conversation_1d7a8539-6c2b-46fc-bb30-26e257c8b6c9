'use client';

import { useEffect } from 'react';
import { onCLS, onINP, onFCP, onLCP, onTTFB } from 'web-vitals';

function sendToAnalytics(metric: { name: string; value: number; id: string }) {
  // In production, you would send this to your analytics service
  // For now, we'll just log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Web Vital:', metric);
  }

  // Example: Send to Google Analytics
  // gtag('event', metric.name, {
  //   event_category: 'Web Vitals',
  //   event_label: metric.id,
  //   value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
  //   non_interaction: true,
  // });

  // Example: Send to custom analytics endpoint
  // fetch('/api/analytics/web-vitals', {
  //   method: 'POST',
  //   headers: { 'Content-Type': 'application/json' },
  //   body: JSON.stringify(metric),
  // });
}

export function WebVitals() {
  useEffect(() => {
    // Measure Core Web Vitals
    onCLS(sendToAnalytics);
    onINP(sendToAnalytics); // INP replaced FID
    onFCP(sendToAnalytics);
    onLCP(sendToAnalytics);
    onTTFB(sendToAnalytics);
  }, []);

  return null; // This component doesn't render anything
}

// Hook for measuring custom metrics
export function usePerformanceMetric(name: string, value: number) {
  useEffect(() => {
    const metric = {
      name,
      value,
      id: `${name}-${Date.now()}`,
      delta: value,
      entries: [],
    };
    sendToAnalytics(metric);
  }, [name, value]);
}

// Component for measuring page load performance
export function PageLoadMetrics({ pageName }: { pageName: string }) {
  useEffect(() => {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      const metric = {
        name: 'page_load_time',
        value: loadTime,
        id: `${pageName}-${Date.now()}`,
        delta: loadTime,
        entries: [],
        page: pageName,
      };
      
      sendToAnalytics(metric);
    };
  }, [pageName]);

  return null;
}
