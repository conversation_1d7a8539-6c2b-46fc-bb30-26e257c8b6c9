(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{238:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},285:(e,t,s)=>{"use strict";s.d(t,{$:()=>d,r:()=>c});var a=s(5155),l=s(2115),r=s(9708),i=s(2085),n=s(9434);let c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200",{variants:{variant:{default:"bg-charity-primary text-white hover:bg-charity-secondary",destructive:"bg-charity-destructive text-white hover:bg-charity-destructive/90",outline:"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground",secondary:"bg-charity-light text-charity-dark hover:bg-charity-light/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-charity-primary underline-offset-4 hover:underline",accent:"bg-charity-accent text-charity-dark hover:bg-charity-accent/80",success:"bg-charity-success text-white hover:bg-charity-success/90",warning:"bg-charity-warning text-white hover:bg-charity-warning/90",info:"bg-charity-info text-white hover:bg-charity-info/90",donate:"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 py-1.5 text-xs",lg:"h-11 rounded-md px-8 py-2.5",xl:"h-12 rounded-md px-10 py-3 text-base",icon:"h-10 w-10 rounded-full",wide:"h-10 px-8 py-2 w-full"}},defaultVariants:{variant:"default",size:"default"}}),d=l.forwardRef((e,t)=>{let{className:s,variant:l,size:i,asChild:d=!1,...o}=e,h=d?r.DX:"button";return(0,a.jsx)(h,{className:(0,n.cn)(c({variant:l,size:i,className:s})),ref:t,...o})});d.displayName="Button"},1976:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2084:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var a=s(5155),l=s(2115),r=s(285),i=s(1976),n=s(238);let c=(0,s(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);var d=s(2138),o=s(9074),h=s(7213),m=s(6874),x=s.n(m),u=s(6695),g=s(3319);function f(){let[e]=(0,l.useState)({}),[t]=(0,l.useState)({}),s={news:[{_id:"1",title:"Community Food Drive Success",slug:"community-food-drive-success",content:"Our recent food drive collected over 1000 items for local families in need.",publishedAt:new Date().toISOString(),attachments:[]},{_id:"2",title:"New Education Program Launch",slug:"new-education-program-launch",content:"We are excited to announce our new after-school tutoring program.",publishedAt:new Date().toISOString(),attachments:[]},{_id:"3",title:"Volunteer Appreciation Event",slug:"volunteer-appreciation-event",content:"Join us in celebrating our amazing volunteers who make our work possible.",publishedAt:new Date().toISOString(),attachments:[]}]},m={albums:[{_id:"1",title:"Community Events",slug:"community-events",description:"Photos from our recent community events"},{_id:"2",title:"Volunteer Activities",slug:"volunteer-activities",description:"Our volunteers in action"},{_id:"3",title:"Success Stories",slug:"success-stories",description:"Stories of impact and change"}]};return(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsx)("section",{className:"bg-gradient-to-r from-teal-600 to-teal-800 text-white py-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,a.jsx)("h1",{className:"text-5xl font-bold mb-6",children:"Making a Difference Together"}),(0,a.jsx)("p",{className:"text-xl mb-8 max-w-2xl mx-auto",children:"Join us in our mission to support communities and create positive change through charitable work and transparency."}),(0,a.jsxs)("div",{className:"flex gap-4 justify-center",children:[(0,a.jsx)(r.$,{size:"lg",variant:"secondary",children:"Learn More"}),(0,a.jsx)(r.$,{size:"lg",variant:"outline",className:"text-white border-white hover:bg-white hover:text-teal-800",children:"Get Involved"})]})]})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-center mb-12 text-teal-800",children:"Our Collective Impact"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"text-center p-6 rounded-lg border border-teal-100 shadow-sm hover:shadow-md transition-shadow",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 rounded-full bg-teal-100 text-teal-600 mb-4",children:(0,a.jsx)(i.A,{className:"h-8 w-8"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-teal-700 mb-2",children:"10,000+"}),(0,a.jsx)("p",{className:"text-teal-600",children:"Lives Changed"})]}),(0,a.jsxs)("div",{className:"text-center p-6 rounded-lg border border-teal-100 shadow-sm hover:shadow-md transition-shadow",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 rounded-full bg-teal-100 text-teal-600 mb-4",children:(0,a.jsx)(n.A,{className:"h-8 w-8"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-teal-700 mb-2",children:"$2.5M"}),(0,a.jsx)("p",{className:"text-teal-600",children:"Donations Facilitated"})]}),(0,a.jsxs)("div",{className:"text-center p-6 rounded-lg border border-teal-100 shadow-sm hover:shadow-md transition-shadow",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center w-16 h-16 rounded-full bg-teal-100 text-teal-600 mb-4",children:(0,a.jsx)(c,{className:"h-8 w-8"})}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-teal-700 mb-2",children:"500+"}),(0,a.jsx)("p",{className:"text-teal-600",children:"Community Partners"})]})]})]})}),(0,a.jsx)("section",{className:"py-16 bg-white",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-teal-800",children:"Latest News"}),(0,a.jsx)(r.$,{asChild:!0,variant:"ghost",className:"text-teal-600 hover:text-teal-700",children:(0,a.jsxs)(x(),{href:"/news",className:"flex items-center",children:["View All ",(0,a.jsx)(d.A,{className:"ml-2 h-4 w-4"})]})})]}),(null==s?void 0:s.news)&&0!==s.news.length?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:s.news.map(t=>(0,a.jsxs)(u.Zp,{className:"overflow-hidden hover:shadow-md transition-shadow flex flex-col h-full",children:[(0,a.jsx)("div",{className:"aspect-video bg-gray-100 overflow-hidden",children:e[t._id]?(0,a.jsx)("img",{src:e[t._id],alt:t.title,className:"w-full h-full object-cover hover:scale-105 transition-transform duration-300"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-teal-50",children:(0,a.jsx)(o.A,{className:"h-12 w-12 text-teal-300"})})}),(0,a.jsxs)(u.Wu,{className:"p-4 flex-grow",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg mb-2 text-teal-800 line-clamp-2",children:t.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mb-4 line-clamp-3",children:[t.content.replace(/<[^>]*>/g,"").substring(0,150),"..."]})]}),(0,a.jsxs)(u.wL,{className:"px-4 pb-4 pt-0 flex flex-col gap-2 w-full mt-auto",children:[(0,a.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:(0,g.GP)(new Date(t.publishedAt),"MMM dd, yyyy")}),(0,a.jsx)(r.$,{asChild:!0,size:"sm",className:"w-full",children:(0,a.jsx)(x(),{href:"/news/".concat(t.slug),children:"Read More"})})]})]},t._id))}):(0,a.jsx)("div",{className:"text-center py-8 bg-gray-50 rounded-lg",children:(0,a.jsx)("p",{className:"text-gray-600",children:"No news articles available."})})]})}),(0,a.jsx)("section",{className:"py-16 bg-teal-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold text-teal-800",children:"Photo Gallery"}),(0,a.jsx)(r.$,{asChild:!0,variant:"ghost",className:"text-teal-600 hover:text-teal-700",children:(0,a.jsxs)(x(),{href:"/gallery",className:"flex items-center",children:["View All ",(0,a.jsx)(d.A,{className:"ml-2 h-4 w-4"})]})})]}),(null==m?void 0:m.albums)&&0!==m.albums.length?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:m.albums.slice(0,3).map(e=>(0,a.jsxs)(u.Zp,{className:"overflow-hidden hover:shadow-md transition-shadow",children:[(0,a.jsx)("div",{className:"aspect-video bg-gray-100 overflow-hidden",children:t[e._id]?(0,a.jsx)("img",{src:t[e._id],alt:e.title,className:"w-full h-full object-cover hover:scale-105 transition-transform duration-300"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center bg-teal-50",children:(0,a.jsx)(h.A,{className:"h-12 w-12 text-teal-300"})})}),(0,a.jsxs)(u.Wu,{className:"p-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg mb-2 text-teal-800",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:e.description}),(0,a.jsx)(r.$,{asChild:!0,size:"sm",variant:"outline",className:"w-full",children:(0,a.jsx)(x(),{href:"/gallery/".concat(e.slug),children:"View Album"})})]})]},e._id))}):(0,a.jsx)("div",{className:"text-center py-8 bg-gray-50 rounded-lg",children:(0,a.jsx)("p",{className:"text-gray-600",children:"No gallery albums available."})})]})}),(0,a.jsx)("section",{className:"py-8 bg-primary/5",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsx)(u.Zp,{className:"bg-white border-primary/20",children:(0,a.jsxs)(u.Wu,{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-primary mb-4",children:"\uD83D\uDE80 Next.js Migration Progress"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-green-600",children:"✅"}),(0,a.jsx)("span",{className:"text-sm",children:"Project Setup"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-green-600",children:"✅"}),(0,a.jsx)("span",{className:"text-sm",children:"Components"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-green-600",children:"✅"}),(0,a.jsx)("span",{className:"text-sm",children:"Layout System"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-yellow-600",children:"\uD83D\uDD04"}),(0,a.jsx)("span",{className:"text-sm",children:"Pages Migration"})]})]})]})})})})]})}},2138:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>n,wL:()=>h});var a=s(5155),l=s(2115),r=s(9434);let i=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",s),...l})});i.displayName="Card";let n=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex flex-col space-y-1.5 p-6",s),...l})});n.displayName="CardHeader";let c=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("h3",{ref:t,className:(0,r.cn)("text-2xl font-semibold leading-none tracking-tight",s),...l})});c.displayName="CardTitle";let d=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("p",{ref:t,className:(0,r.cn)("text-sm text-muted-foreground",s),...l})});d.displayName="CardDescription";let o=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("p-6 pt-0",s),...l})});o.displayName="CardContent";let h=l.forwardRef((e,t)=>{let{className:s,...l}=e;return(0,a.jsx)("div",{ref:t,className:(0,r.cn)("flex items-center p-6 pt-0",s),...l})});h.displayName="CardFooter"},7133:(e,t,s)=>{Promise.resolve().then(s.bind(s,2084))},7213:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r,oH:()=>i,vV:()=>n});var a=s(2596),l=s(9688);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,l.QP)((0,a.$)(t))}function i(e){return e.replace(/\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g,(e,t)=>{let s=t.split(",").map(e=>e.trim()).filter(Boolean);if(0===s.length)return e;let a=s.map(e=>'<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">'.concat(e,"</span>")).join("");return'<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">'.concat(a,"</div></div>")})}let n=(e,t)=>{}}},e=>{var t=t=>e(e.s=t);e.O(0,[598,874,38,441,684,358],()=>t(7133)),_N_E=e.O()}]);