(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[220],{1008:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},1099:(e,a,t)=>{Promise.resolve().then(t.bind(t,2934))},1264:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},2934:(e,a,t)=>{"use strict";t.d(a,{AboutPageContent:()=>j});var s=t(5155);t(2115);var r=t(6874),i=t.n(r),n=t(6695),l=t(2960),c=t(5731),o=t(9434);function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,o.cn)("animate-pulse rounded-md bg-muted",a),...t})}var m=t(9946);let h=(0,m.A)("circle-user",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]]);var u=t(1264),x=t(9420);let p=(0,m.A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),y=(0,m.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),g=(0,m.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);var v=t(1008);function f(){let{data:e,isLoading:a,error:t}=(0,l.I)({queryKey:["team-members-public"],queryFn:()=>c.YV.getAll({active:!0}),staleTime:3e5});if(a)return(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[void 0,void 0,void 0].map((e,a)=>(0,s.jsxs)(n.Zp,{className:"overflow-hidden",children:[(0,s.jsx)("div",{className:"aspect-square relative",children:(0,s.jsx)(d,{className:"h-full w-full absolute"})}),(0,s.jsxs)(n.Wu,{className:"p-4",children:[(0,s.jsx)(d,{className:"h-6 w-3/4 mb-2"}),(0,s.jsx)(d,{className:"h-4 w-1/2 mb-4"}),(0,s.jsx)(d,{className:"h-20 w-full"})]})]},a))});if(t)return(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,s.jsx)("p",{children:"Unable to load team members. Please try again later."})});let r=(null==e?void 0:e.teamMembers)||[];return 0===r.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,s.jsx)("p",{children:"No team members to display."})}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(e=>(0,s.jsx)(w,{member:e},e._id))})}function w(e){var a,t,r;let{member:i}=e;return(0,s.jsxs)(n.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[(0,s.jsx)("div",{className:"aspect-square relative bg-gray-100",children:i.photo?(0,s.jsx)("img",{src:"".concat(v.A.baseURL).concat(i.photo),alt:i.name,className:"w-full h-full object-cover"}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,s.jsx)(h,{className:"h-24 w-24 text-gray-300"})})}),(0,s.jsxs)(n.Wu,{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:i.name}),(0,s.jsx)("p",{className:"text-sm font-medium text-teal-600 mb-3",children:i.position}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-4",children:i.bio}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-3 mt-4",children:[i.email&&(0,s.jsx)("a",{href:"mailto:".concat(i.email),className:"text-gray-500 hover:text-teal-600 transition-colors","aria-label":"Email ".concat(i.name),children:(0,s.jsx)(u.A,{className:"h-5 w-5"})}),i.phone&&(0,s.jsx)("a",{href:"tel:".concat(i.phone),className:"text-gray-500 hover:text-teal-600 transition-colors","aria-label":"Call ".concat(i.name),children:(0,s.jsx)(x.A,{className:"h-5 w-5"})}),(null==(a=i.socialLinks)?void 0:a.linkedin)&&(0,s.jsx)("a",{href:i.socialLinks.linkedin,target:"_blank",rel:"noopener noreferrer",className:"text-gray-500 hover:text-blue-600 transition-colors","aria-label":"".concat(i.name,"'s LinkedIn profile"),children:(0,s.jsx)(p,{className:"h-5 w-5"})}),(null==(t=i.socialLinks)?void 0:t.twitter)&&(0,s.jsx)("a",{href:i.socialLinks.twitter,target:"_blank",rel:"noopener noreferrer",className:"text-gray-500 hover:text-blue-400 transition-colors","aria-label":"".concat(i.name,"'s Twitter profile"),children:(0,s.jsx)(y,{className:"h-5 w-5"})}),(null==(r=i.socialLinks)?void 0:r.facebook)&&(0,s.jsx)("a",{href:i.socialLinks.facebook,target:"_blank",rel:"noopener noreferrer",className:"text-gray-500 hover:text-blue-800 transition-colors","aria-label":"".concat(i.name,"'s Facebook profile"),children:(0,s.jsx)(g,{className:"h-5 w-5"})})]})]})]})}function j(){let{data:e,isLoading:a}=(0,l.I)({queryKey:["aboutContent"],queryFn:c.EO.getContent,staleTime:3e5}),t=(null==e?void 0:e.aboutContent)||{mission:"Our mission is to provide support and resources to those in need within our community.",vision:"We believe in creating a welcoming environment where everyone can find the help they need, regardless of their background or circumstances.",foundedYear:"2010",volunteersCount:"50",peopleHelpedCount:"10,000",communitiesCount:"5"};return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold mb-6",children:["About ","Charity Welcome Hub"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[(0,s.jsx)(n.Zp,{className:"col-span-1 md:col-span-2",children:(0,s.jsxs)(n.Wu,{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-charity-primary",children:"Our Mission"}),a?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d,{className:"h-4 w-full"}),(0,s.jsx)(d,{className:"h-4 w-full"}),(0,s.jsx)(d,{className:"h-4 w-3/4"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:t.mission}),(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:t.vision})]})]})}),(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-charity-primary",children:"Quick Facts"}),(0,s.jsxs)("ul",{className:"space-y-3",children:[(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("polyline",{points:"20 6 9 17 4 12"})})}),(0,s.jsxs)("span",{children:["Founded in ",t.foundedYear||"2010"]})]}),(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("polyline",{points:"20 6 9 17 4 12"})})}),(0,s.jsxs)("span",{children:["Over ",t.volunteersCount||"50"," dedicated volunteers"]})]}),(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("polyline",{points:"20 6 9 17 4 12"})})}),(0,s.jsxs)("span",{children:["Helped over ",t.peopleHelpedCount||"10,000"," individuals"]})]}),(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("polyline",{points:"20 6 9 17 4 12"})})}),(0,s.jsxs)("span",{children:["Active in ",t.communitiesCount||"5"," local communities"]})]})]})]})})]}),(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-6 text-charity-primary",children:"Our Values"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"bg-charity-primary/10 p-4 rounded-full mb-4",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-charity-primary",children:[(0,s.jsx)("path",{d:"M18 8a6 6 0 0 0-6-6 6 6 0 0 0-6 6c0 7 6 13 6 13s6-6 6-13z"}),(0,s.jsx)("circle",{cx:"12",cy:"8",r:"2"})]})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Compassion"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We approach every individual with empathy and understanding, recognizing their unique circumstances and needs."})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"bg-charity-primary/10 p-4 rounded-full mb-4",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-charity-primary",children:[(0,s.jsx)("path",{d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"}),(0,s.jsx)("path",{d:"m7 9 3 3-3 3"}),(0,s.jsx)("path",{d:"M14 9h3v6h-3"})]})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Integrity"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We maintain the highest standards of honesty and transparency in all our operations and relationships."})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"bg-charity-primary/10 p-4 rounded-full mb-4",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-charity-primary",children:[(0,s.jsx)("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),(0,s.jsx)("circle",{cx:"9",cy:"7",r:"4"}),(0,s.jsx)("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),(0,s.jsx)("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Community"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We believe in the power of community and work together to create positive change and lasting impact."})]})})})]})]}),(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-6 text-charity-primary",children:"Get Involved"}),(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"p-6",children:[(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:"There are many ways to support our mission and make a difference in our community. Whether you're interested in volunteering your time, making a donation, or partnering with us, we welcome your involvement."}),(0,s.jsx)("div",{className:"flex flex-wrap gap-4 justify-center md:justify-start",children:(0,s.jsx)(i(),{href:"/contact",className:"bg-white border border-charity-primary text-charity-primary hover:bg-charity-primary/10 font-medium py-2 px-6 rounded-md transition-colors duration-200",children:"Contact Us"})})]})})]}),(0,s.jsx)("div",{className:"py-16 bg-gray-50 -mx-4",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Our Team"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Meet the dedicated individuals who make our mission possible."})]}),(0,s.jsx)(f,{})]})})]})}},5731:(e,a,t)=>{"use strict";t.d(a,{AY:()=>m,EO:()=>n,TP:()=>c,YV:()=>l,jE:()=>h,l4:()=>d,lM:()=>o});var s=t(3464),r=t(1008);let i=s.A.create({baseURL:r.A.baseURL,timeout:r.A.timeout,withCredentials:r.A.withCredentials});i.interceptors.request.use(async e=>{{let a=localStorage.getItem("authToken");a&&(e.headers.Authorization="Bearer ".concat(a))}return e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>{var a;return(null==(a=e.response)?void 0:a.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(e)});let n={getContent:async()=>(await i.get("/about")).data,updateContent:async e=>(await i.post("/about",e)).data},l={getAll:async e=>({teamMembers:(await i.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await i.get("/team/".concat(e))).data}),create:async e=>(await i.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,a)=>(await i.put("/team/".concat(e),a,{headers:{"Content-Type":"multipart/form-data"}})).data,delete:async e=>(await i.delete("/team/".concat(e))).data},c={submit:async e=>(await i.post("/contact",e)).data,getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,t=arguments.length>2?arguments[2]:void 0,s={page:e,limit:a,...t?{status:t}:{}};return(await i.get("/contact",{params:s})).data},getById:async e=>(await i.get("/contact/".concat(e))).data,updateStatus:async(e,a)=>(await i.put("/contact/".concat(e,"/status"),{status:a})).data,delete:async e=>(await i.delete("/contact/".concat(e))).data},o={getAll:async e=>({locations:(await i.get("/locations",{params:e})).data}),getById:async e=>({location:(await i.get("/locations/".concat(e))).data}),create:async e=>{let a={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await i.post("/locations",a)).data},update:async(e,a)=>{let t={...a,isMainOffice:void 0!==a.isMainOffice?String(a.isMainOffice):void 0,active:void 0!==a.active?String(a.active):void 0};return(await i.put("/locations/".concat(e),t)).data},delete:async e=>(await i.delete("/locations/".concat(e))).data},d={getAll:async()=>(await i.get("/faqs")).data,getAllAdmin:async()=>(await i.get("/admin/faqs")).data,getById:async e=>(await i.get("/admin/faqs/".concat(e))).data,create:async e=>(await i.post("/admin/faqs",e)).data,update:async function(e,a){let t,s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(s)t={isActive:void 0===a.isActive||!!a.isActive};else{var r,n,l;t={question:null==(r=a.question)?void 0:r.trim(),answer:null==(n=a.answer)?void 0:n.trim(),category:(null==(l=a.category)?void 0:l.trim())||"General",order:"number"==typeof a.order?a.order:0,isActive:void 0===a.isActive||!!a.isActive}}try{return(await i.put("/admin/faqs/".concat(e),t)).data}catch(e){throw e}},delete:async e=>(await i.delete("/admin/faqs/".concat(e))).data},m={getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await i.get("/news?page=".concat(e,"&limit=").concat(a,"&includeAttachments=true"))).data},getBySlug:async e=>(await i.get("/news/".concat(e,"?includeAttachments=true"))).data.news,getById:async e=>(await i.get("/admin/news/".concat(e))).data.news,create:async e=>(await i.post("/admin/news",e)).data.news,update:async(e,a)=>(await i.put("/admin/news/".concat(e),a)).data.news,delete:async e=>(await i.delete("/admin/news/".concat(e))).data},h={getAllAlbums:async()=>(await i.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await i.get("/gallery/albums/".concat(e))).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await i.get("/admin/gallery/albums/".concat(e))).data}catch(e){throw e}},createAlbum:async e=>(await i.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,a)=>(await i.put("/admin/gallery/albums/".concat(e),a)).data.album,deleteAlbum:async e=>{await i.delete("/admin/gallery/albums/".concat(e))},uploadImage:async(e,a)=>{var t;let s=null==(t=document.querySelector('meta[name="csrf-token"]'))?void 0:t.getAttribute("content");return s&&a.append("_csrf",s),(await i.post("/admin/gallery/albums/".concat(e,"/images"),a,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await i.delete("/admin/gallery/images/".concat(e))}}},6695:(e,a,t)=>{"use strict";t.d(a,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>n,aR:()=>l,wL:()=>m});var s=t(5155),r=t(2115),i=t(9434);let n=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",t),...r})});n.displayName="Card";let l=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...r})});l.displayName="CardHeader";let c=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let o=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})});o.displayName="CardDescription";let d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("p-6 pt-0",t),...r})});d.displayName="CardContent";let m=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",t),...r})});m.displayName="CardFooter"},9420:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9434:(e,a,t)=>{"use strict";t.d(a,{cn:()=>i,oH:()=>n,vV:()=>l});var s=t(2596),r=t(9688);function i(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}function n(e){return e.replace(/\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g,(e,a)=>{let t=a.split(",").map(e=>e.trim()).filter(Boolean);if(0===t.length)return e;let s=t.map(e=>'<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">'.concat(e,"</span>")).join("");return'<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">'.concat(s,"</div></div>")})}let l=(e,a)=>{}}},e=>{var a=a=>e(e.s=a);e.O(0,[598,967,951,874,441,684,358],()=>a(1099)),_N_E=e.O()}]);