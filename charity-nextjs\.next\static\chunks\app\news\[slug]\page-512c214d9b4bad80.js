(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[120],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>d,r:()=>c});var s=a(5155),r=a(2115),n=a(9708),i=a(2085),l=a(9434);let c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200",{variants:{variant:{default:"bg-charity-primary text-white hover:bg-charity-secondary",destructive:"bg-charity-destructive text-white hover:bg-charity-destructive/90",outline:"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground",secondary:"bg-charity-light text-charity-dark hover:bg-charity-light/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-charity-primary underline-offset-4 hover:underline",accent:"bg-charity-accent text-charity-dark hover:bg-charity-accent/80",success:"bg-charity-success text-white hover:bg-charity-success/90",warning:"bg-charity-warning text-white hover:bg-charity-warning/90",info:"bg-charity-info text-white hover:bg-charity-info/90",donate:"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 py-1.5 text-xs",lg:"h-11 rounded-md px-8 py-2.5",xl:"h-12 rounded-md px-10 py-3 text-base",icon:"h-10 w-10 rounded-full",wide:"h-10 px-8 py-2 w-full"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:a,variant:r,size:i,asChild:d=!1,...o}=e,m=d?n.DX:"button";return(0,s.jsx)(m,{className:(0,l.cn)(c({variant:r,size:i,className:a})),ref:t,...o})});d.displayName="Button"},1008:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},1883:(e,t,a)=>{"use strict";a.d(t,{NewsDetailPageContent:()=>R});var s=a(5155),r=a(2115),n=a(6874),i=a.n(n),l=a(6766),c=a(2960),d=a(9946);let o=(0,d.A)("file-image",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]]),m=(0,d.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),h=(0,d.A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]);var u=a(4631),p=a(9074);let x=(0,d.A)("paperclip",[["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 2.829 2.829l8.414-8.586a4 4 0 1 0-5.657-5.657l-8.379 8.551a6 6 0 1 0 8.485 8.485l8.379-8.551",key:"1miecu"}]]);var y=a(4416),g=a(2355),v=a(3052),f=a(3319),w=a(6695),b=a(285),j=a(2085),N=a(9434);let k=(0,j.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function A(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{className:(0,N.cn)(k({variant:a}),t),...r})}var M=a(5731);let C=(0,d.A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),T=(0,d.A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);function q(e){var t,a;let{title:r="An error occurred",description:n,error:i,onRetry:l,resetErrorBoundary:c}=e,d="string"==typeof i?i:(null==i?void 0:i.message)?i.message:(null==i||null==(a=i.response)||null==(t=a.data)?void 0:t.message)?i.response.data.message:"An unexpected error occurred";return(0,s.jsxs)(w.Zp,{className:"border-red-200 bg-red-50",children:[(0,s.jsxs)(w.aR,{children:[(0,s.jsxs)(w.ZB,{className:"text-red-800 flex items-center",children:[(0,s.jsx)(C,{className:"h-5 w-5 mr-2"}),r]}),(0,s.jsx)(w.BT,{className:"text-red-700",children:n||d})]}),(l||c)&&(0,s.jsx)(w.Wu,{children:(0,s.jsxs)(b.$,{variant:"outline",size:"sm",onClick:()=>{l&&l(),c&&c()},className:"border-red-300 text-red-700 hover:bg-red-100",children:[(0,s.jsx)(T,{className:"mr-2 h-4 w-4"}),"Try Again"]})})]})}var B=a(1008);function _(e){return"string"==typeof e?e:e.username||"Unknown Author"}function R(e){let{slug:t}=e,[a,n]=(0,r.useState)([]),[d,j]=(0,r.useState)({}),[k,C]=(0,r.useState)({}),[T,R]=(0,r.useState)(null),[z,S]=(0,r.useState)(null),[L,I]=(0,r.useState)(-1),{data:Z,isLoading:E,error:W,refetch:D}=(0,c.I)({queryKey:["news",t],queryFn:()=>M.AY.getBySlug(t||""),enabled:!!t});(0,r.useEffect)(()=>{if(null==Z?void 0:Z.attachments){let e=Z.attachments.map(e=>({...e,url:"".concat(B.A.baseURL,"/api/news/attachments/").concat(e._id,"/content"),downloadUrl:"".concat(B.A.baseURL,"/api/news/attachments/").concat(e._id,"/download")}));n(e);let t=e.find(e=>e.mimeType&&e.mimeType.startsWith("image/"));t&&R(t._id)}},[Z]);let H=(0,r.useCallback)(async(e,t)=>{if(!d[e]&&!k[e]){C(t=>({...t,[e]:!0}));try{let a=await fetch("".concat(B.A.baseURL,"/api/news/attachments/").concat(e,"/content"));if(a.ok){let s=await a.text();j(a=>({...a,[e]:{content:s,filename:t}}))}}catch(e){}finally{C(t=>({...t,[e]:!1}))}}},[d,k]),U=(e,t)=>{S(e),I(t)},O=()=>{S(null),I(-1)},F=e=>{let t=a.filter(e=>{var t;return null==(t=e.mimeType)?void 0:t.startsWith("image/")});if(0===t.length)return;let s=L;I(s="prev"===e?L>0?L-1:t.length-1:L<t.length-1?L+1:0),S(t[s].url)};(0,r.useEffect)(()=>{let e=e=>{z&&("Escape"===e.key?O():"ArrowLeft"===e.key?F("prev"):"ArrowRight"===e.key&&F("next"))};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[z,L,a]);let P=a.filter(e=>{var t;return null==(t=e.mimeType)?void 0:t.startsWith("image/")}),V=a.filter(e=>{var t;return!(null==(t=e.mimeType)?void 0:t.startsWith("image/"))});return E?(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,s.jsx)(u.A,{className:"h-8 w-8 animate-spin text-primary"})})}):W||!Z?(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)(q,{title:"Failed to load news article",error:W,onRetry:D}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(i(),{href:"/news",children:(0,s.jsx)(b.$,{variant:"outline",children:"Back to News"})})})]}):(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)(i(),{href:"/news",className:"text-primary hover:underline mb-4 inline-block",children:"← Back to News"}),(0,s.jsxs)(w.Zp,{className:"mt-4 overflow-hidden",children:[T&&(0,s.jsxs)("div",{className:"w-full h-[350px] md:h-[450px] bg-gray-100 relative overflow-hidden",children:[(0,s.jsx)(l.default,{src:"".concat(B.A.baseURL,"/news/attachments/").concat(T,"/content"),alt:Z.title,fill:!0,className:"object-cover transition-transform duration-700 hover:scale-105",priority:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw",onError:e=>{e.currentTarget.src="/placeholder.svg"}}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"}),(0,s.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6 text-white",children:[(0,s.jsx)("h1",{className:"text-3xl md:text-4xl font-bold mb-2 drop-shadow-md",children:Z.title}),(0,s.jsxs)("div",{className:"flex items-center text-white/80 text-sm md:text-base",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-1"}),(0,s.jsx)("span",{children:Z.publishDate&&(0,f.GP)(new Date(Z.publishDate),"MMMM dd, yyyy")}),Z.author&&(0,s.jsxs)("span",{className:"ml-4",children:["by ",_(Z.author)]})]})]})]}),(0,s.jsx)(w.aR,{children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsx)("div",{children:!T&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(w.ZB,{className:"text-3xl",children:Z.title}),(0,s.jsxs)(w.BT,{className:"flex items-center gap-2 mt-2",children:[(0,s.jsx)(p.A,{className:"h-4 w-4"}),Z.publishDate&&(0,s.jsx)("span",{className:"text-gray-500",children:(0,f.GP)(new Date(Z.publishDate),"MMMM dd, yyyy")}),Z.author&&(0,s.jsxs)("span",{className:"ml-2 text-gray-500",children:["by ",_(Z.author)]})]})]})}),!Z.published&&(0,s.jsx)(A,{variant:"outline",className:"bg-yellow-100 text-yellow-800 border-yellow-200",children:"Draft"})]})}),(0,s.jsx)(w.Wu,{className:"prose prose-lg max-w-none",children:(0,s.jsx)("div",{dangerouslySetInnerHTML:{__html:(0,N.oH)(Z.body)}})})]}),P.length>1&&(0,s.jsxs)(w.Zp,{className:"mt-6",children:[(0,s.jsx)(w.aR,{children:(0,s.jsxs)(w.ZB,{className:"flex items-center",children:[(0,s.jsx)(o,{className:"mr-2 h-5 w-5"}),"Image Gallery (",P.length,")"]})}),(0,s.jsx)(w.Wu,{children:(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:P.slice(1).map((e,t)=>(0,s.jsxs)("div",{className:"relative group cursor-pointer",onClick:()=>U(e.url,t+1),children:[(0,s.jsx)("div",{className:"aspect-square bg-gray-100 rounded-lg overflow-hidden relative",children:(0,s.jsx)(l.default,{src:e.url,alt:e.filename,fill:!0,className:"object-cover transition-transform duration-300 group-hover:scale-105",sizes:"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw",onError:e=>{e.currentTarget.src="/placeholder.svg"}})}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 rounded-lg"})]},e._id))})})]}),V.length>0&&(0,s.jsxs)(w.Zp,{className:"mt-6",children:[(0,s.jsx)(w.aR,{children:(0,s.jsxs)(w.ZB,{className:"flex items-center",children:[(0,s.jsx)(x,{className:"mr-2 h-5 w-5"}),"Attachments (",V.length,")"]})}),(0,s.jsx)(w.Wu,{children:(0,s.jsx)("div",{className:"space-y-3",children:V.map(e=>{var t,a,r,n;let i=(n=e.mimeType).startsWith("image/")?o:n.includes("text")||n.includes("document")?m:h,l=(null==(t=e.mimeType)?void 0:t.includes("text"))||(null==(a=e.filename)?void 0:a.endsWith(".txt"))||(null==(r=e.filename)?void 0:r.endsWith(".md"));return(0,s.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(i,{className:"h-6 w-6 text-gray-500"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:e.filename}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[e.mimeType," • ",(e.size/1024).toFixed(1)," KB"]})]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[l&&(0,s.jsx)(b.$,{variant:"outline",size:"sm",onClick:()=>H(e._id,e.filename),disabled:k[e._id],children:k[e._id]?(0,s.jsx)(u.A,{className:"h-4 w-4 animate-spin"}):"Preview"}),(0,s.jsx)(b.$,{variant:"outline",size:"sm",asChild:!0,children:(0,s.jsx)("a",{href:e.downloadUrl,download:!0,children:"Download"})})]})]}),d[e._id]&&(0,s.jsxs)("div",{className:"mt-4 p-3 bg-gray-50 rounded border",children:[(0,s.jsx)("h4",{className:"font-medium mb-2",children:d[e._id].filename}),(0,s.jsx)("pre",{className:"text-sm whitespace-pre-wrap text-gray-700 max-h-64 overflow-y-auto",children:d[e._id].content})]})]},e._id)})})})]}),z&&(0,s.jsx)("div",{className:"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"relative max-w-4xl max-h-full",children:[(0,s.jsx)("button",{onClick:O,className:"absolute top-4 right-4 text-white hover:text-gray-300 z-10",children:(0,s.jsx)(y.A,{className:"h-6 w-6"})}),P.length>1&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("button",{onClick:()=>F("prev"),className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10",children:(0,s.jsx)(g.A,{className:"h-8 w-8"})}),(0,s.jsx)("button",{onClick:()=>F("next"),className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10",children:(0,s.jsx)(v.A,{className:"h-8 w-8"})})]}),(0,s.jsx)("img",{src:z,alt:"Full size",className:"max-w-full max-h-full object-contain",crossOrigin:"anonymous"})]})})]})}},2355:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},3052:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4416:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4631:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},5731:(e,t,a)=>{"use strict";a.d(t,{AY:()=>m,EO:()=>i,TP:()=>c,YV:()=>l,jE:()=>h,l4:()=>o,lM:()=>d});var s=a(3464),r=a(1008);let n=s.A.create({baseURL:r.A.baseURL,timeout:r.A.timeout,withCredentials:r.A.withCredentials});n.interceptors.request.use(async e=>{{let t=localStorage.getItem("authToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(e)});let i={getContent:async()=>(await n.get("/about")).data,updateContent:async e=>(await n.post("/about",e)).data},l={getAll:async e=>({teamMembers:(await n.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await n.get("/team/".concat(e))).data}),create:async e=>(await n.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,t)=>(await n.put("/team/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}})).data,delete:async e=>(await n.delete("/team/".concat(e))).data},c={submit:async e=>(await n.post("/contact",e)).data,getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0,s={page:e,limit:t,...a?{status:a}:{}};return(await n.get("/contact",{params:s})).data},getById:async e=>(await n.get("/contact/".concat(e))).data,updateStatus:async(e,t)=>(await n.put("/contact/".concat(e,"/status"),{status:t})).data,delete:async e=>(await n.delete("/contact/".concat(e))).data},d={getAll:async e=>({locations:(await n.get("/locations",{params:e})).data}),getById:async e=>({location:(await n.get("/locations/".concat(e))).data}),create:async e=>{let t={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await n.post("/locations",t)).data},update:async(e,t)=>{let a={...t,isMainOffice:void 0!==t.isMainOffice?String(t.isMainOffice):void 0,active:void 0!==t.active?String(t.active):void 0};return(await n.put("/locations/".concat(e),a)).data},delete:async e=>(await n.delete("/locations/".concat(e))).data},o={getAll:async()=>(await n.get("/faqs")).data,getAllAdmin:async()=>(await n.get("/admin/faqs")).data,getById:async e=>(await n.get("/admin/faqs/".concat(e))).data,create:async e=>(await n.post("/admin/faqs",e)).data,update:async function(e,t){let a,s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(s)a={isActive:void 0===t.isActive||!!t.isActive};else{var r,i,l;a={question:null==(r=t.question)?void 0:r.trim(),answer:null==(i=t.answer)?void 0:i.trim(),category:(null==(l=t.category)?void 0:l.trim())||"General",order:"number"==typeof t.order?t.order:0,isActive:void 0===t.isActive||!!t.isActive}}try{return(await n.put("/admin/faqs/".concat(e),a)).data}catch(e){throw e}},delete:async e=>(await n.delete("/admin/faqs/".concat(e))).data},m={getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await n.get("/news?page=".concat(e,"&limit=").concat(t,"&includeAttachments=true"))).data},getBySlug:async e=>(await n.get("/news/".concat(e,"?includeAttachments=true"))).data.news,getById:async e=>(await n.get("/admin/news/".concat(e))).data.news,create:async e=>(await n.post("/admin/news",e)).data.news,update:async(e,t)=>(await n.put("/admin/news/".concat(e),t)).data.news,delete:async e=>(await n.delete("/admin/news/".concat(e))).data},h={getAllAlbums:async()=>(await n.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await n.get("/gallery/albums/".concat(e))).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await n.get("/admin/gallery/albums/".concat(e))).data}catch(e){throw e}},createAlbum:async e=>(await n.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,t)=>(await n.put("/admin/gallery/albums/".concat(e),t)).data.album,deleteAlbum:async e=>{await n.delete("/admin/gallery/albums/".concat(e))},uploadImage:async(e,t)=>{var a;let s=null==(a=document.querySelector('meta[name="csrf-token"]'))?void 0:a.getAttribute("content");return s&&t.append("_csrf",s),(await n.post("/admin/gallery/albums/".concat(e,"/images"),t,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await n.delete("/admin/gallery/images/".concat(e))}}},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>i,aR:()=>l,wL:()=>m});var s=a(5155),r=a(2115),n=a(9434);let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});i.displayName="Card";let l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...r})});l.displayName="CardHeader";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",a),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",a),...r})});o.displayName="CardContent";let m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",a),...r})});m.displayName="CardFooter"},7692:(e,t,a)=>{Promise.resolve().then(a.bind(a,1883))},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,oH:()=>i,vV:()=>l});var s=a(2596),r=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function i(e){return e.replace(/\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g,(e,t)=>{let a=t.split(",").map(e=>e.trim()).filter(Boolean);if(0===a.length)return e;let s=a.map(e=>'<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">'.concat(e,"</span>")).join("");return'<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">'.concat(s,"</div></div>")})}let l=(e,t)=>{}}},e=>{var t=t=>e(e.s=t);e.O(0,[598,967,951,874,38,766,441,684,358],()=>t(7692)),_N_E=e.O()}]);