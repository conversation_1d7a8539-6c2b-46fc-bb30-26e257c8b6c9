[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\about\\AboutPageContent.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\about\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\about\\route.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\auth\\login\\route.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\contact\\route.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\faqs\\route.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\gallery\\albums\\route.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\gallery\\albums\\[slug]\\route.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\news\\attachments\\[id]\\content\\route.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\news\\route.ts": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\news\\[slug]\\route.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\proxy\\route.ts": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\contact\\ContactPageContent.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\contact\\page.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\faq\\FAQPageContent.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\faq\\page.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\GalleryPageContent.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\loading.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\page.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\[slug]\\GalleryDetailPageContent.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\[slug]\\page.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\layout.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\loading.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\loading.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\NewsPageContent.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\page.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\[slug]\\NewsDetailPageContent.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\[slug]\\page.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\page.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\robots.txt\\route.ts": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\sitemap.xml\\route.ts": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\admin\\layout.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\admin\\news\\page.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\admin\\page.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\layout.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\login\\page.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\page.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\search\\page.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\about\\TeamSection.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\admin\\AdminSidebar.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\auth\\ProtectedRoute.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\DonationGoals.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\error\\ErrorTester.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\error\\GlobalErrorHandler.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\HeroSlider.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\InteractiveMap.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Footer.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Header.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\NewsletterSignup.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\PartnersSection.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\providers\\AppProviders.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\providers\\QueryProvider.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\seo\\StructuredData.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\accordion.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\alert-dialog.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\api-error.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\badge.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\button.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\card.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\form.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\input.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\label.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\pagination.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\progress.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\skeleton.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\table.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\tabs.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\textarea.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\use-toast.ts": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\utils\\Preload.tsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\utils\\ScrollToTop.tsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\config\\api.ts": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\contexts\\AuthContext.tsx": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\contexts\\SocketContext.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\hooks\\use-csrf.ts": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\hooks\\use-mobile.tsx": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\api.ts": "78", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\apiClient.ts": "79", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\query-client.ts": "80", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\query-utils.ts": "81", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\services\\galleryApi.ts": "82", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\services\\index.ts": "83", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\services\\newsApi.ts": "84", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\services\\searchApi.ts": "85", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\utils.ts": "86", "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\middleware.ts": "87"}, {"size": 9223, "mtime": 1752413157454, "results": "88", "hashOfConfig": "89"}, {"size": 924, "mtime": 1752350245082, "results": "90", "hashOfConfig": "89"}, {"size": 920, "mtime": 1752352312042, "results": "91", "hashOfConfig": "89"}, {"size": 1002, "mtime": 1752353785178, "results": "92", "hashOfConfig": "89"}, {"size": 1830, "mtime": 1752352297469, "results": "93", "hashOfConfig": "89"}, {"size": 905, "mtime": 1752352283832, "results": "94", "hashOfConfig": "89"}, {"size": 927, "mtime": 1752352263631, "results": "95", "hashOfConfig": "89"}, {"size": 1188, "mtime": 1752353502382, "results": "96", "hashOfConfig": "89"}, {"size": 1644, "mtime": 1752353528429, "results": "97", "hashOfConfig": "89"}, {"size": 905, "mtime": 1752352231785, "results": "98", "hashOfConfig": "89"}, {"size": 1183, "mtime": 1752353515617, "results": "99", "hashOfConfig": "89"}, {"size": 2398, "mtime": 1752413213052, "results": "100", "hashOfConfig": "89"}, {"size": 12185, "mtime": 1752413239280, "results": "101", "hashOfConfig": "89"}, {"size": 933, "mtime": 1752351077422, "results": "102", "hashOfConfig": "89"}, {"size": 3624, "mtime": 1752351365556, "results": "103", "hashOfConfig": "89"}, {"size": 978, "mtime": 1752351342947, "results": "104", "hashOfConfig": "89"}, {"size": 5484, "mtime": 1752352815346, "results": "105", "hashOfConfig": "89"}, {"size": 1143, "mtime": 1752353125919, "results": "106", "hashOfConfig": "89"}, {"size": 901, "mtime": 1752351989933, "results": "107", "hashOfConfig": "89"}, {"size": 9708, "mtime": 1752352849198, "results": "108", "hashOfConfig": "89"}, {"size": 987, "mtime": 1752352029429, "results": "109", "hashOfConfig": "89"}, {"size": 3396, "mtime": 1752353076542, "results": "110", "hashOfConfig": "89"}, {"size": 833, "mtime": 1752353101216, "results": "111", "hashOfConfig": "89"}, {"size": 1050, "mtime": 1752353115477, "results": "112", "hashOfConfig": "89"}, {"size": 9259, "mtime": 1752351665155, "results": "113", "hashOfConfig": "89"}, {"size": 930, "mtime": 1752351625854, "results": "114", "hashOfConfig": "89"}, {"size": 15243, "mtime": 1752352740178, "results": "115", "hashOfConfig": "89"}, {"size": 2205, "mtime": 1752353031618, "results": "116", "hashOfConfig": "89"}, {"size": 13764, "mtime": 1752350000375, "results": "117", "hashOfConfig": "89"}, {"size": 626, "mtime": 1752352931817, "results": "118", "hashOfConfig": "89"}, {"size": 4348, "mtime": 1752352920957, "results": "119", "hashOfConfig": "89"}, {"size": 788, "mtime": 1752411342855, "results": "120", "hashOfConfig": "89"}, {"size": 8222, "mtime": 1752411834225, "results": "121", "hashOfConfig": "89"}, {"size": 23351, "mtime": 1752411752655, "results": "122", "hashOfConfig": "89"}, {"size": 3392, "mtime": 1752410715143, "results": "123", "hashOfConfig": "89"}, {"size": 4978, "mtime": 1752411918551, "results": "124", "hashOfConfig": "89"}, {"size": 14662, "mtime": 1752412831549, "results": "125", "hashOfConfig": "89"}, {"size": 9719, "mtime": 1752412181520, "results": "126", "hashOfConfig": "89"}, {"size": 4742, "mtime": 1752350236689, "results": "127", "hashOfConfig": "89"}, {"size": 3600, "mtime": 1752411365886, "results": "128", "hashOfConfig": "89"}, {"size": 2397, "mtime": 1752411105231, "results": "129", "hashOfConfig": "89"}, {"size": 5530, "mtime": 1752410781698, "results": "130", "hashOfConfig": "89"}, {"size": 5287, "mtime": 1752411168904, "results": "131", "hashOfConfig": "89"}, {"size": 3841, "mtime": 1752411140710, "results": "132", "hashOfConfig": "89"}, {"size": 8781, "mtime": 1752410820189, "results": "133", "hashOfConfig": "89"}, {"size": 13208, "mtime": 1752410946604, "results": "134", "hashOfConfig": "89"}, {"size": 6806, "mtime": 1752349767022, "results": "135", "hashOfConfig": "89"}, {"size": 6112, "mtime": 1752349729096, "results": "136", "hashOfConfig": "89"}, {"size": 4478, "mtime": 1752410977870, "results": "137", "hashOfConfig": "89"}, {"size": 2384, "mtime": 1752411021157, "results": "138", "hashOfConfig": "89"}, {"size": 2045, "mtime": 1752353488451, "results": "139", "hashOfConfig": "89"}, {"size": 654, "mtime": 1752410689114, "results": "140", "hashOfConfig": "89"}, {"size": 2506, "mtime": 1752412619976, "results": "141", "hashOfConfig": "89"}, {"size": 2920, "mtime": 1752352894187, "results": "142", "hashOfConfig": "89"}, {"size": 1977, "mtime": 1752351291353, "results": "143", "hashOfConfig": "89"}, {"size": 4434, "mtime": 1752412362687, "results": "144", "hashOfConfig": "89"}, {"size": 1841, "mtime": 1752351553495, "results": "145", "hashOfConfig": "89"}, {"size": 1128, "mtime": 1752351534324, "results": "146", "hashOfConfig": "89"}, {"size": 2524, "mtime": 1752349321898, "results": "147", "hashOfConfig": "89"}, {"size": 1877, "mtime": 1752349339191, "results": "148", "hashOfConfig": "89"}, {"size": 4085, "mtime": 1752351196012, "results": "149", "hashOfConfig": "89"}, {"size": 907, "mtime": 1752349691444, "results": "150", "hashOfConfig": "89"}, {"size": 710, "mtime": 1752350957225, "results": "151", "hashOfConfig": "89"}, {"size": 2751, "mtime": 1752351518586, "results": "152", "hashOfConfig": "89"}, {"size": 791, "mtime": 1752412570654, "results": "153", "hashOfConfig": "89"}, {"size": 261, "mtime": 1752350431732, "results": "154", "hashOfConfig": "89"}, {"size": 2765, "mtime": 1752412337433, "results": "155", "hashOfConfig": "89"}, {"size": 1897, "mtime": 1752412552986, "results": "156", "hashOfConfig": "89"}, {"size": 838, "mtime": 1752350970385, "results": "157", "hashOfConfig": "89"}, {"size": 314, "mtime": 1752350991464, "results": "158", "hashOfConfig": "89"}, {"size": 1539, "mtime": 1752411246977, "results": "159", "hashOfConfig": "89"}, {"size": 591, "mtime": 1752411260256, "results": "160", "hashOfConfig": "89"}, {"size": 656, "mtime": 1752352337273, "results": "161", "hashOfConfig": "89"}, {"size": 2727, "mtime": 1752410584456, "results": "162", "hashOfConfig": "89"}, {"size": 3722, "mtime": 1752410615125, "results": "163", "hashOfConfig": "89"}, {"size": 1113, "mtime": 1752412274307, "results": "164", "hashOfConfig": "89"}, {"size": 659, "mtime": 1752412285437, "results": "165", "hashOfConfig": "89"}, {"size": 12152, "mtime": 1752353335272, "results": "166", "hashOfConfig": "89"}, {"size": 7073, "mtime": 1752411978602, "results": "167", "hashOfConfig": "89"}, {"size": 1466, "mtime": 1752352452256, "results": "168", "hashOfConfig": "89"}, {"size": 3332, "mtime": 1752412647782, "results": "169", "hashOfConfig": "89"}, {"size": 2440, "mtime": 1752412035513, "results": "170", "hashOfConfig": "89"}, {"size": 171, "mtime": 1752412050606, "results": "171", "hashOfConfig": "89"}, {"size": 3106, "mtime": 1752412005297, "results": "172", "hashOfConfig": "89"}, {"size": 435, "mtime": 1752412144030, "results": "173", "hashOfConfig": "89"}, {"size": 3329, "mtime": 1752410656664, "results": "174", "hashOfConfig": "89"}, {"size": 3399, "mtime": 1752412223009, "results": "175", "hashOfConfig": "89"}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1li7zyw", {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 11, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 7, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\about\\AboutPageContent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\about\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\auth\\login\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\contact\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\faqs\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\gallery\\albums\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\gallery\\albums\\[slug]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\news\\attachments\\[id]\\content\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\news\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\news\\[slug]\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\proxy\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\contact\\ContactPageContent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\faq\\FAQPageContent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\faq\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\GalleryPageContent.tsx", ["437"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\loading.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\[slug]\\GalleryDetailPageContent.tsx", ["438", "439", "440", "441", "442", "443"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\[slug]\\page.tsx", ["444"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\loading.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\loading.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\NewsPageContent.tsx", ["445", "446", "447"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\[slug]\\NewsDetailPageContent.tsx", ["448", "449", "450", "451", "452", "453"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\[slug]\\page.tsx", ["454", "455", "456"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\page.tsx", ["457", "458", "459", "460", "461", "462", "463", "464"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\robots.txt\\route.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\sitemap.xml\\route.ts", ["465", "466"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\admin\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\admin\\news\\page.tsx", ["467"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\admin\\page.tsx", ["468", "469", "470", "471"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\login\\page.tsx", ["472"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\page.tsx", ["473", "474", "475", "476", "477", "478", "479", "480", "481", "482", "483"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\app\\search\\page.tsx", ["484", "485", "486", "487", "488", "489", "490", "491", "492", "493", "494", "495"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\about\\TeamSection.tsx", ["496"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\admin\\AdminSidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\DonationGoals.tsx", ["497"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\error\\ErrorTester.tsx", ["498", "499"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\error\\GlobalErrorHandler.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\HeroSlider.tsx", ["500", "501"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\InteractiveMap.tsx", ["502", "503", "504", "505", "506", "507", "508", "509"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Footer.tsx", ["510", "511", "512", "513", "514", "515", "516"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Header.tsx", ["517", "518"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\NewsletterSignup.tsx", ["519", "520"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\PartnersSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx", ["521"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\providers\\AppProviders.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\providers\\QueryProvider.tsx", ["522", "523", "524"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\seo\\StructuredData.tsx", ["525"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\api-error.tsx", ["526", "527"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\textarea.tsx", ["528"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\ui\\use-toast.ts", ["529"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\utils\\Preload.tsx", ["530"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\utils\\ScrollToTop.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\config\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\contexts\\SocketContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\hooks\\use-csrf.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\hooks\\use-mobile.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\api.ts", ["531", "532", "533", "534", "535"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\apiClient.ts", ["536", "537", "538", "539", "540", "541"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\query-client.ts", ["542"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\query-utils.ts", ["543", "544", "545", "546"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\services\\galleryApi.ts", ["547"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\services\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\services\\newsApi.ts", ["548", "549", "550"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\services\\searchApi.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\lib\\utils.ts", ["551", "552", "553", "554", "555"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\middleware.ts", ["556"], [], {"ruleId": "557", "severity": 2, "message": "558", "line": 82, "column": 38, "nodeType": "559", "messageId": "560", "endLine": 82, "endColumn": 41, "suggestions": "561"}, {"ruleId": "562", "severity": 2, "message": "563", "line": 7, "column": 51, "nodeType": null, "messageId": "564", "endLine": 7, "endColumn": 60}, {"ruleId": "562", "severity": 2, "message": "565", "line": 21, "column": 10, "nodeType": null, "messageId": "564", "endLine": 21, "endColumn": 25}, {"ruleId": "566", "severity": 2, "message": "567", "line": 119, "column": 56, "nodeType": "568", "messageId": "569", "suggestions": "570"}, {"ruleId": "562", "severity": 2, "message": "571", "line": 131, "column": 9, "nodeType": null, "messageId": "564", "endLine": 131, "endColumn": 19}, {"ruleId": "557", "severity": 2, "message": "558", "line": 153, "column": 31, "nodeType": "559", "messageId": "560", "endLine": 153, "endColumn": 34, "suggestions": "572"}, {"ruleId": "573", "severity": 1, "message": "574", "line": 255, "column": 13, "nodeType": "575", "endLine": 260, "endColumn": 15}, {"ruleId": "562", "severity": 2, "message": "576", "line": 9, "column": 9, "nodeType": null, "messageId": "564", "endLine": 9, "endColumn": 13}, {"ruleId": "562", "severity": 2, "message": "577", "line": 6, "column": 28, "nodeType": null, "messageId": "564", "endLine": 6, "endColumn": 33}, {"ruleId": "562", "severity": 2, "message": "578", "line": 9, "column": 10, "nodeType": null, "messageId": "564", "endLine": 9, "endColumn": 16}, {"ruleId": "573", "severity": 1, "message": "574", "line": 129, "column": 21, "nodeType": "575", "endLine": 139, "endColumn": 23}, {"ruleId": "557", "severity": 2, "message": "558", "line": 37, "column": 50, "nodeType": "559", "messageId": "560", "endLine": 37, "endColumn": 53, "suggestions": "579"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 53, "column": 67, "nodeType": "559", "messageId": "560", "endLine": 53, "endColumn": 70, "suggestions": "580"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 62, "column": 58, "nodeType": "559", "messageId": "560", "endLine": 62, "endColumn": 61, "suggestions": "581"}, {"ruleId": "582", "severity": 1, "message": "583", "line": 135, "column": 6, "nodeType": "584", "endLine": 135, "endColumn": 41, "suggestions": "585"}, {"ruleId": "562", "severity": 2, "message": "586", "line": 260, "column": 23, "nodeType": null, "messageId": "564", "endLine": 260, "endColumn": 36}, {"ruleId": "573", "severity": 1, "message": "574", "line": 385, "column": 13, "nodeType": "575", "endLine": 390, "endColumn": 15}, {"ruleId": "562", "severity": 2, "message": "587", "line": 4, "column": 10, "nodeType": null, "messageId": "564", "endLine": 4, "endColumn": 28}, {"ruleId": "562", "severity": 2, "message": "588", "line": 4, "column": 30, "nodeType": null, "messageId": "564", "endLine": 4, "endColumn": 54}, {"ruleId": "562", "severity": 2, "message": "589", "line": 41, "column": 12, "nodeType": null, "messageId": "564", "endLine": 41, "endColumn": 17}, {"ruleId": "562", "severity": 2, "message": "590", "line": 3, "column": 20, "nodeType": null, "messageId": "564", "endLine": 3, "endColumn": 29}, {"ruleId": "562", "severity": 2, "message": "591", "line": 5, "column": 59, "nodeType": null, "messageId": "564", "endLine": 5, "endColumn": 63}, {"ruleId": "562", "severity": 2, "message": "592", "line": 7, "column": 10, "nodeType": null, "messageId": "564", "endLine": 7, "endColumn": 18}, {"ruleId": "562", "severity": 2, "message": "593", "line": 17, "column": 26, "nodeType": null, "messageId": "564", "endLine": 17, "endColumn": 43}, {"ruleId": "562", "severity": 2, "message": "594", "line": 18, "column": 29, "nodeType": null, "messageId": "564", "endLine": 18, "endColumn": 49}, {"ruleId": "573", "severity": 1, "message": "574", "line": 175, "column": 23, "nodeType": "575", "endLine": 179, "endColumn": 25}, {"ruleId": "573", "severity": 1, "message": "574", "line": 249, "column": 23, "nodeType": "575", "endLine": 253, "endColumn": 25}, {"ruleId": "595", "severity": 1, "message": "596", "line": 256, "column": 25, "nodeType": "575", "endLine": 256, "endColumn": 70}, {"ruleId": "557", "severity": 2, "message": "558", "line": 14, "column": 20, "nodeType": "559", "messageId": "560", "endLine": 14, "endColumn": 23, "suggestions": "597"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 15, "column": 23, "nodeType": "559", "messageId": "560", "endLine": 15, "endColumn": 26, "suggestions": "598"}, {"ruleId": null, "nodeType": null, "fatal": true, "severity": 2, "message": "599", "line": 196, "column": 9}, {"ruleId": "566", "severity": 2, "message": "567", "line": 182, "column": 47, "nodeType": "568", "messageId": "569", "suggestions": "600"}, {"ruleId": "595", "severity": 1, "message": "596", "line": 245, "column": 15, "nodeType": "575", "endLine": 245, "endColumn": 65}, {"ruleId": "595", "severity": 1, "message": "596", "line": 448, "column": 21, "nodeType": "575", "endLine": 448, "endColumn": 71}, {"ruleId": "595", "severity": 1, "message": "596", "line": 503, "column": 15, "nodeType": "575", "endLine": 503, "endColumn": 49}, {"ruleId": "557", "severity": 2, "message": "558", "line": 59, "column": 21, "nodeType": "559", "messageId": "560", "endLine": 59, "endColumn": 24, "suggestions": "601"}, {"ruleId": "562", "severity": 2, "message": "591", "line": 5, "column": 59, "nodeType": null, "messageId": "564", "endLine": 5, "endColumn": 63}, {"ruleId": "562", "severity": 2, "message": "602", "line": 11, "column": 8, "nodeType": null, "messageId": "564", "endLine": 11, "endColumn": 24}, {"ruleId": "562", "severity": 2, "message": "603", "line": 12, "column": 8, "nodeType": null, "messageId": "564", "endLine": 12, "endColumn": 23}, {"ruleId": "562", "severity": 2, "message": "604", "line": 13, "column": 8, "nodeType": null, "messageId": "564", "endLine": 13, "endColumn": 22}, {"ruleId": "562", "severity": 2, "message": "605", "line": 15, "column": 8, "nodeType": null, "messageId": "564", "endLine": 15, "endColumn": 21}, {"ruleId": "562", "severity": 2, "message": "606", "line": 17, "column": 8, "nodeType": null, "messageId": "564", "endLine": 17, "endColumn": 17}, {"ruleId": "557", "severity": 2, "message": "558", "line": 54, "column": 65, "nodeType": "559", "messageId": "560", "endLine": 54, "endColumn": 68, "suggestions": "607"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 69, "column": 66, "nodeType": "559", "messageId": "560", "endLine": 69, "endColumn": 69, "suggestions": "608"}, {"ruleId": "573", "severity": 1, "message": "574", "line": 200, "column": 23, "nodeType": "575", "endLine": 204, "endColumn": 25}, {"ruleId": "573", "severity": 1, "message": "574", "line": 274, "column": 23, "nodeType": "575", "endLine": 278, "endColumn": 25}, {"ruleId": "595", "severity": 1, "message": "596", "line": 281, "column": 25, "nodeType": "575", "endLine": 281, "endColumn": 70}, {"ruleId": "566", "severity": 2, "message": "609", "line": 70, "column": 31, "nodeType": "568", "messageId": "569", "suggestions": "610"}, {"ruleId": "566", "severity": 2, "message": "609", "line": 70, "column": 44, "nodeType": "568", "messageId": "569", "suggestions": "611"}, {"ruleId": "566", "severity": 2, "message": "609", "line": 74, "column": 44, "nodeType": "568", "messageId": "569", "suggestions": "612"}, {"ruleId": "566", "severity": 2, "message": "609", "line": 74, "column": 57, "nodeType": "568", "messageId": "569", "suggestions": "613"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 102, "column": 53, "nodeType": "559", "messageId": "560", "endLine": 102, "endColumn": 56, "suggestions": "614"}, {"ruleId": "595", "severity": 1, "message": "596", "line": 131, "column": 19, "nodeType": "575", "endLine": 131, "endColumn": 53}, {"ruleId": "557", "severity": 2, "message": "558", "line": 134, "column": 55, "nodeType": "559", "messageId": "560", "endLine": 134, "endColumn": 58, "suggestions": "615"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 164, "column": 39, "nodeType": "559", "messageId": "560", "endLine": 164, "endColumn": 42, "suggestions": "616"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 188, "column": 41, "nodeType": "559", "messageId": "560", "endLine": 188, "endColumn": 44, "suggestions": "617"}, {"ruleId": "566", "severity": 2, "message": "567", "line": 215, "column": 22, "nodeType": "568", "messageId": "569", "suggestions": "618"}, {"ruleId": "566", "severity": 2, "message": "609", "line": 215, "column": 46, "nodeType": "568", "messageId": "569", "suggestions": "619"}, {"ruleId": "566", "severity": 2, "message": "609", "line": 215, "column": 59, "nodeType": "568", "messageId": "569", "suggestions": "620"}, {"ruleId": "573", "severity": 1, "message": "574", "line": 70, "column": 11, "nodeType": "575", "endLine": 74, "endColumn": 13}, {"ruleId": "566", "severity": 2, "message": "567", "line": 56, "column": 47, "nodeType": "568", "messageId": "569", "suggestions": "621"}, {"ruleId": "566", "severity": 2, "message": "567", "line": 49, "column": 58, "nodeType": "568", "messageId": "569", "suggestions": "622"}, {"ruleId": "562", "severity": 2, "message": "589", "line": 101, "column": 35, "nodeType": null, "messageId": "564", "endLine": 101, "endColumn": 40}, {"ruleId": "557", "severity": 2, "message": "558", "line": 62, "column": 73, "nodeType": "559", "messageId": "560", "endLine": 62, "endColumn": 76, "suggestions": "623"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 77, "column": 62, "nodeType": "559", "messageId": "560", "endLine": 77, "endColumn": 65, "suggestions": "624"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 11, "column": 8, "nodeType": "559", "messageId": "560", "endLine": 11, "endColumn": 11, "suggestions": "625"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 12, "column": 14, "nodeType": "559", "messageId": "560", "endLine": 12, "endColumn": 17, "suggestions": "626"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 18, "column": 19, "nodeType": "559", "messageId": "560", "endLine": 18, "endColumn": 22, "suggestions": "627"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 19, "column": 30, "nodeType": "559", "messageId": "560", "endLine": 19, "endColumn": 33, "suggestions": "628"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 95, "column": 10, "nodeType": "559", "messageId": "560", "endLine": 95, "endColumn": 13, "suggestions": "629"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 96, "column": 29, "nodeType": "559", "messageId": "560", "endLine": 96, "endColumn": 32, "suggestions": "630"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 218, "column": 37, "nodeType": "559", "messageId": "560", "endLine": 218, "endColumn": 40, "suggestions": "631"}, {"ruleId": "582", "severity": 1, "message": "632", "line": 267, "column": 20, "nodeType": "633", "endLine": 267, "endColumn": 27}, {"ruleId": "562", "severity": 2, "message": "634", "line": 5, "column": 38, "nodeType": null, "messageId": "564", "endLine": 5, "endColumn": 50}, {"ruleId": "562", "severity": 2, "message": "635", "line": 5, "column": 127, "nodeType": null, "messageId": "564", "endLine": 5, "endColumn": 135}, {"ruleId": "562", "severity": 2, "message": "636", "line": 5, "column": 137, "nodeType": null, "messageId": "564", "endLine": 5, "endColumn": 144}, {"ruleId": "562", "severity": 2, "message": "637", "line": 5, "column": 146, "nodeType": null, "messageId": "564", "endLine": 5, "endColumn": 155}, {"ruleId": "562", "severity": 2, "message": "638", "line": 5, "column": 163, "nodeType": null, "messageId": "564", "endLine": 5, "endColumn": 169}, {"ruleId": "562", "severity": 2, "message": "592", "line": 6, "column": 10, "nodeType": null, "messageId": "564", "endLine": 6, "endColumn": 18}, {"ruleId": "582", "severity": 1, "message": "639", "line": 21, "column": 9, "nodeType": "640", "endLine": 30, "endColumn": 4}, {"ruleId": "562", "severity": 2, "message": "641", "line": 10, "column": 10, "nodeType": null, "messageId": "564", "endLine": 10, "endColumn": 12}, {"ruleId": "562", "severity": 2, "message": "642", "line": 20, "column": 9, "nodeType": null, "messageId": "564", "endLine": 20, "endColumn": 13}, {"ruleId": "557", "severity": 2, "message": "558", "line": 56, "column": 19, "nodeType": "559", "messageId": "560", "endLine": 56, "endColumn": 22, "suggestions": "643"}, {"ruleId": "566", "severity": 2, "message": "567", "line": 85, "column": 42, "nodeType": "568", "messageId": "569", "suggestions": "644"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 6, "column": 34, "nodeType": "559", "messageId": "560", "endLine": 6, "endColumn": 37, "suggestions": "645"}, {"ruleId": "562", "severity": 2, "message": "646", "line": 5, "column": 10, "nodeType": null, "messageId": "564", "endLine": 5, "endColumn": 18}, {"ruleId": "557", "severity": 2, "message": "558", "line": 16, "column": 38, "nodeType": "559", "messageId": "560", "endLine": 16, "endColumn": 41, "suggestions": "647"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 30, "column": 26, "nodeType": "559", "messageId": "560", "endLine": 30, "endColumn": 29, "suggestions": "648"}, {"ruleId": "562", "severity": 2, "message": "649", "line": 1, "column": 20, "nodeType": null, "messageId": "564", "endLine": 1, "endColumn": 29}, {"ruleId": "557", "severity": 2, "message": "558", "line": 15, "column": 33, "nodeType": "559", "messageId": "560", "endLine": 15, "endColumn": 36, "suggestions": "650"}, {"ruleId": "562", "severity": 2, "message": "651", "line": 28, "column": 3, "nodeType": null, "messageId": "564", "endLine": 28, "endColumn": 10}, {"ruleId": "652", "severity": 2, "message": "653", "line": 5, "column": 18, "nodeType": "633", "messageId": "654", "endLine": 5, "endColumn": 31, "suggestions": "655"}, {"ruleId": "562", "severity": 2, "message": "656", "line": 4, "column": 35, "nodeType": null, "messageId": "564", "endLine": 4, "endColumn": 42}, {"ruleId": "557", "severity": 2, "message": "558", "line": 36, "column": 18, "nodeType": "559", "messageId": "560", "endLine": 36, "endColumn": 21, "suggestions": "657"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 140, "column": 17, "nodeType": "559", "messageId": "560", "endLine": 140, "endColumn": 20, "suggestions": "658"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 312, "column": 24, "nodeType": "559", "messageId": "560", "endLine": 312, "endColumn": 27, "suggestions": "659"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 362, "column": 28, "nodeType": "559", "messageId": "560", "endLine": 362, "endColumn": 31, "suggestions": "660"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 367, "column": 40, "nodeType": "559", "messageId": "560", "endLine": 367, "endColumn": 43, "suggestions": "661"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 419, "column": 69, "nodeType": "559", "messageId": "560", "endLine": 419, "endColumn": 72, "suggestions": "662"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 109, "column": 17, "nodeType": "559", "messageId": "560", "endLine": 109, "endColumn": 20, "suggestions": "663"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 110, "column": 17, "nodeType": "559", "messageId": "560", "endLine": 110, "endColumn": 20, "suggestions": "664"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 119, "column": 31, "nodeType": "559", "messageId": "560", "endLine": 119, "endColumn": 34, "suggestions": "665"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 175, "column": 44, "nodeType": "559", "messageId": "560", "endLine": 175, "endColumn": 47, "suggestions": "666"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 189, "column": 43, "nodeType": "559", "messageId": "560", "endLine": 189, "endColumn": 46, "suggestions": "667"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 216, "column": 45, "nodeType": "559", "messageId": "560", "endLine": 216, "endColumn": 48, "suggestions": "668"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 11, "column": 38, "nodeType": "559", "messageId": "560", "endLine": 11, "endColumn": 41, "suggestions": "669"}, {"ruleId": "562", "severity": 2, "message": "670", "line": 1, "column": 34, "nodeType": null, "messageId": "564", "endLine": 1, "endColumn": 51}, {"ruleId": "557", "severity": 2, "message": "558", "line": 41, "column": 28, "nodeType": "559", "messageId": "560", "endLine": 41, "endColumn": 31, "suggestions": "671"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 64, "column": 21, "nodeType": "559", "messageId": "560", "endLine": 64, "endColumn": 24, "suggestions": "672"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 68, "column": 56, "nodeType": "559", "messageId": "560", "endLine": 68, "endColumn": 59, "suggestions": "673"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 84, "column": 69, "nodeType": "559", "messageId": "560", "endLine": 84, "endColumn": 72, "suggestions": "674"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 14, "column": 17, "nodeType": "559", "messageId": "560", "endLine": 14, "endColumn": 20, "suggestions": "675"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 79, "column": 72, "nodeType": "559", "messageId": "560", "endLine": 79, "endColumn": 75, "suggestions": "676"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 87, "column": 69, "nodeType": "559", "messageId": "560", "endLine": 87, "endColumn": 72, "suggestions": "677"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 40, "column": 36, "nodeType": "559", "messageId": "560", "endLine": 40, "endColumn": 39, "suggestions": "678"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 40, "column": 42, "nodeType": "559", "messageId": "560", "endLine": 40, "endColumn": 45, "suggestions": "679"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 70, "column": 49, "nodeType": "559", "messageId": "560", "endLine": 70, "endColumn": 52, "suggestions": "680"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 84, "column": 51, "nodeType": "559", "messageId": "560", "endLine": 84, "endColumn": 54, "suggestions": "681"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 102, "column": 52, "nodeType": "559", "messageId": "560", "endLine": 102, "endColumn": 55, "suggestions": "682"}, {"ruleId": "557", "severity": 2, "message": "558", "line": 20, "column": 77, "nodeType": "559", "messageId": "560", "endLine": 20, "endColumn": 80, "suggestions": "683"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["684", "685"], "@typescript-eslint/no-unused-vars", "'Maximize2' is defined but never used.", "unusedVar", "'imageDimensions' is assigned a value but never used.", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["686", "687", "688", "689"], "'imageCount' is assigned a value but never used.", ["690", "691"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'slug' is assigned a value but never used.", "'Clock' is defined but never used.", "'Button' is defined but never used.", ["692", "693"], ["694", "695"], ["696", "697"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'navigateImage'. Either include it or remove the dependency array.", "ArrayExpression", ["698"], "'IconComponent' is assigned a value but never used.", "'NewsStructuredData' is defined but never used.", "'BreadcrumbStructuredData' is defined but never used.", "'error' is defined but never used.", "'useEffect' is defined but never used.", "'Mail' is defined but never used.", "'useQuery' is defined but never used.", "'setNewsThumbnails' is assigned a value but never used.", "'setGalleryThumbnails' is assigned a value but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", ["699", "700"], ["701", "702"], "Parsing error: JSX element 'AlertDialogContent' has no corresponding closing tag.", ["703", "704", "705", "706"], ["707", "708"], "'NewsletterSignup' is defined but never used.", "'PartnersSection' is defined but never used.", "'InteractiveMap' is defined but never used.", "'DonationGoals' is defined but never used.", "'NextImage' is defined but never used.", ["709", "710"], ["711", "712"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["713", "714", "715", "716"], ["717", "718", "719", "720"], ["721", "722", "723", "724"], ["725", "726", "727", "728"], ["729", "730"], ["731", "732"], ["733", "734"], ["735", "736"], ["737", "738", "739", "740"], ["741", "742", "743", "744"], ["745", "746", "747", "748"], ["749", "750", "751", "752"], ["753", "754", "755", "756"], ["757", "758"], ["759", "760"], ["761", "762"], ["763", "764"], ["765", "766"], ["767", "768"], ["769", "770"], ["771", "772"], ["773", "774"], "The ref value 'mapRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'mapRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", "'ExternalLink' is defined but never used.", "'Facebook' is defined but never used.", "'Twitter' is defined but never used.", "'Instagram' is defined but never used.", "'Loader' is defined but never used.", "The 'locationsData' object makes the dependencies of useMemo Hook (at line 36) change on every render. Move it inside the useMemo callback. Alternatively, wrap the initialization of 'locationsData' in its own useMemo() Hook.", "VariableDeclarator", "'cn' is defined but never used.", "'user' is assigned a value but never used.", ["775", "776"], ["777", "778", "779", "780"], ["781", "782"], "'useState' is defined but never used.", ["783", "784"], ["785", "786"], "'AlbumItem' is defined but never used.", ["787", "788"], "'variant' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "noEmptyInterfaceWithSuper", ["789"], "'variant' is defined but never used.", ["790", "791"], ["792", "793"], ["794", "795"], ["796", "797"], ["798", "799"], ["800", "801"], ["802", "803"], ["804", "805"], ["806", "807"], ["808", "809"], ["810", "811"], ["812", "813"], ["814", "815"], "'HydrationBoundary' is defined but never used.", ["816", "817"], ["818", "819"], ["820", "821"], ["822", "823"], ["824", "825"], ["826", "827"], ["828", "829"], ["830", "831"], ["832", "833"], ["834", "835"], ["836", "837"], ["838", "839"], ["840", "841"], {"messageId": "842", "fix": "843", "desc": "844"}, {"messageId": "845", "fix": "846", "desc": "847"}, {"messageId": "848", "data": "849", "fix": "850", "desc": "851"}, {"messageId": "848", "data": "852", "fix": "853", "desc": "854"}, {"messageId": "848", "data": "855", "fix": "856", "desc": "857"}, {"messageId": "848", "data": "858", "fix": "859", "desc": "860"}, {"messageId": "842", "fix": "861", "desc": "844"}, {"messageId": "845", "fix": "862", "desc": "847"}, {"messageId": "842", "fix": "863", "desc": "844"}, {"messageId": "845", "fix": "864", "desc": "847"}, {"messageId": "842", "fix": "865", "desc": "844"}, {"messageId": "845", "fix": "866", "desc": "847"}, {"messageId": "842", "fix": "867", "desc": "844"}, {"messageId": "845", "fix": "868", "desc": "847"}, {"desc": "869", "fix": "870"}, {"messageId": "842", "fix": "871", "desc": "844"}, {"messageId": "845", "fix": "872", "desc": "847"}, {"messageId": "842", "fix": "873", "desc": "844"}, {"messageId": "845", "fix": "874", "desc": "847"}, {"messageId": "848", "data": "875", "fix": "876", "desc": "851"}, {"messageId": "848", "data": "877", "fix": "878", "desc": "854"}, {"messageId": "848", "data": "879", "fix": "880", "desc": "857"}, {"messageId": "848", "data": "881", "fix": "882", "desc": "860"}, {"messageId": "842", "fix": "883", "desc": "844"}, {"messageId": "845", "fix": "884", "desc": "847"}, {"messageId": "842", "fix": "885", "desc": "844"}, {"messageId": "845", "fix": "886", "desc": "847"}, {"messageId": "842", "fix": "887", "desc": "844"}, {"messageId": "845", "fix": "888", "desc": "847"}, {"messageId": "848", "data": "889", "fix": "890", "desc": "891"}, {"messageId": "848", "data": "892", "fix": "893", "desc": "894"}, {"messageId": "848", "data": "895", "fix": "896", "desc": "897"}, {"messageId": "848", "data": "898", "fix": "899", "desc": "900"}, {"messageId": "848", "data": "901", "fix": "902", "desc": "891"}, {"messageId": "848", "data": "903", "fix": "904", "desc": "894"}, {"messageId": "848", "data": "905", "fix": "906", "desc": "897"}, {"messageId": "848", "data": "907", "fix": "908", "desc": "900"}, {"messageId": "848", "data": "909", "fix": "910", "desc": "891"}, {"messageId": "848", "data": "911", "fix": "912", "desc": "894"}, {"messageId": "848", "data": "913", "fix": "914", "desc": "897"}, {"messageId": "848", "data": "915", "fix": "916", "desc": "900"}, {"messageId": "848", "data": "917", "fix": "918", "desc": "891"}, {"messageId": "848", "data": "919", "fix": "920", "desc": "894"}, {"messageId": "848", "data": "921", "fix": "922", "desc": "897"}, {"messageId": "848", "data": "923", "fix": "924", "desc": "900"}, {"messageId": "842", "fix": "925", "desc": "844"}, {"messageId": "845", "fix": "926", "desc": "847"}, {"messageId": "842", "fix": "927", "desc": "844"}, {"messageId": "845", "fix": "928", "desc": "847"}, {"messageId": "842", "fix": "929", "desc": "844"}, {"messageId": "845", "fix": "930", "desc": "847"}, {"messageId": "842", "fix": "931", "desc": "844"}, {"messageId": "845", "fix": "932", "desc": "847"}, {"messageId": "848", "data": "933", "fix": "934", "desc": "851"}, {"messageId": "848", "data": "935", "fix": "936", "desc": "854"}, {"messageId": "848", "data": "937", "fix": "938", "desc": "857"}, {"messageId": "848", "data": "939", "fix": "940", "desc": "860"}, {"messageId": "848", "data": "941", "fix": "942", "desc": "891"}, {"messageId": "848", "data": "943", "fix": "944", "desc": "894"}, {"messageId": "848", "data": "945", "fix": "946", "desc": "897"}, {"messageId": "848", "data": "947", "fix": "948", "desc": "900"}, {"messageId": "848", "data": "949", "fix": "950", "desc": "891"}, {"messageId": "848", "data": "951", "fix": "952", "desc": "894"}, {"messageId": "848", "data": "953", "fix": "954", "desc": "897"}, {"messageId": "848", "data": "955", "fix": "956", "desc": "900"}, {"messageId": "848", "data": "957", "fix": "958", "desc": "851"}, {"messageId": "848", "data": "959", "fix": "960", "desc": "854"}, {"messageId": "848", "data": "961", "fix": "962", "desc": "857"}, {"messageId": "848", "data": "963", "fix": "964", "desc": "860"}, {"messageId": "848", "data": "965", "fix": "966", "desc": "851"}, {"messageId": "848", "data": "967", "fix": "968", "desc": "854"}, {"messageId": "848", "data": "969", "fix": "970", "desc": "857"}, {"messageId": "848", "data": "971", "fix": "972", "desc": "860"}, {"messageId": "842", "fix": "973", "desc": "844"}, {"messageId": "845", "fix": "974", "desc": "847"}, {"messageId": "842", "fix": "975", "desc": "844"}, {"messageId": "845", "fix": "976", "desc": "847"}, {"messageId": "842", "fix": "977", "desc": "844"}, {"messageId": "845", "fix": "978", "desc": "847"}, {"messageId": "842", "fix": "979", "desc": "844"}, {"messageId": "845", "fix": "980", "desc": "847"}, {"messageId": "842", "fix": "981", "desc": "844"}, {"messageId": "845", "fix": "982", "desc": "847"}, {"messageId": "842", "fix": "983", "desc": "844"}, {"messageId": "845", "fix": "984", "desc": "847"}, {"messageId": "842", "fix": "985", "desc": "844"}, {"messageId": "845", "fix": "986", "desc": "847"}, {"messageId": "842", "fix": "987", "desc": "844"}, {"messageId": "845", "fix": "988", "desc": "847"}, {"messageId": "842", "fix": "989", "desc": "844"}, {"messageId": "845", "fix": "990", "desc": "847"}, {"messageId": "842", "fix": "991", "desc": "844"}, {"messageId": "845", "fix": "992", "desc": "847"}, {"messageId": "848", "data": "993", "fix": "994", "desc": "851"}, {"messageId": "848", "data": "995", "fix": "996", "desc": "854"}, {"messageId": "848", "data": "997", "fix": "998", "desc": "857"}, {"messageId": "848", "data": "999", "fix": "1000", "desc": "860"}, {"messageId": "842", "fix": "1001", "desc": "844"}, {"messageId": "845", "fix": "1002", "desc": "847"}, {"messageId": "842", "fix": "1003", "desc": "844"}, {"messageId": "845", "fix": "1004", "desc": "847"}, {"messageId": "842", "fix": "1005", "desc": "844"}, {"messageId": "845", "fix": "1006", "desc": "847"}, {"messageId": "842", "fix": "1007", "desc": "844"}, {"messageId": "845", "fix": "1008", "desc": "847"}, {"messageId": "1009", "fix": "1010", "desc": "1011"}, {"messageId": "842", "fix": "1012", "desc": "844"}, {"messageId": "845", "fix": "1013", "desc": "847"}, {"messageId": "842", "fix": "1014", "desc": "844"}, {"messageId": "845", "fix": "1015", "desc": "847"}, {"messageId": "842", "fix": "1016", "desc": "844"}, {"messageId": "845", "fix": "1017", "desc": "847"}, {"messageId": "842", "fix": "1018", "desc": "844"}, {"messageId": "845", "fix": "1019", "desc": "847"}, {"messageId": "842", "fix": "1020", "desc": "844"}, {"messageId": "845", "fix": "1021", "desc": "847"}, {"messageId": "842", "fix": "1022", "desc": "844"}, {"messageId": "845", "fix": "1023", "desc": "847"}, {"messageId": "842", "fix": "1024", "desc": "844"}, {"messageId": "845", "fix": "1025", "desc": "847"}, {"messageId": "842", "fix": "1026", "desc": "844"}, {"messageId": "845", "fix": "1027", "desc": "847"}, {"messageId": "842", "fix": "1028", "desc": "844"}, {"messageId": "845", "fix": "1029", "desc": "847"}, {"messageId": "842", "fix": "1030", "desc": "844"}, {"messageId": "845", "fix": "1031", "desc": "847"}, {"messageId": "842", "fix": "1032", "desc": "844"}, {"messageId": "845", "fix": "1033", "desc": "847"}, {"messageId": "842", "fix": "1034", "desc": "844"}, {"messageId": "845", "fix": "1035", "desc": "847"}, {"messageId": "842", "fix": "1036", "desc": "844"}, {"messageId": "845", "fix": "1037", "desc": "847"}, {"messageId": "842", "fix": "1038", "desc": "844"}, {"messageId": "845", "fix": "1039", "desc": "847"}, {"messageId": "842", "fix": "1040", "desc": "844"}, {"messageId": "845", "fix": "1041", "desc": "847"}, {"messageId": "842", "fix": "1042", "desc": "844"}, {"messageId": "845", "fix": "1043", "desc": "847"}, {"messageId": "842", "fix": "1044", "desc": "844"}, {"messageId": "845", "fix": "1045", "desc": "847"}, {"messageId": "842", "fix": "1046", "desc": "844"}, {"messageId": "845", "fix": "1047", "desc": "847"}, {"messageId": "842", "fix": "1048", "desc": "844"}, {"messageId": "845", "fix": "1049", "desc": "847"}, {"messageId": "842", "fix": "1050", "desc": "844"}, {"messageId": "845", "fix": "1051", "desc": "847"}, {"messageId": "842", "fix": "1052", "desc": "844"}, {"messageId": "845", "fix": "1053", "desc": "847"}, {"messageId": "842", "fix": "1054", "desc": "844"}, {"messageId": "845", "fix": "1055", "desc": "847"}, {"messageId": "842", "fix": "1056", "desc": "844"}, {"messageId": "845", "fix": "1057", "desc": "847"}, {"messageId": "842", "fix": "1058", "desc": "844"}, {"messageId": "845", "fix": "1059", "desc": "847"}, {"messageId": "842", "fix": "1060", "desc": "844"}, {"messageId": "845", "fix": "1061", "desc": "847"}, {"messageId": "842", "fix": "1062", "desc": "844"}, {"messageId": "845", "fix": "1063", "desc": "847"}, "suggestUnknown", {"range": "1064", "text": "1065"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1066", "text": "1067"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "replaceWithAlt", {"alt": "1068"}, {"range": "1069", "text": "1070"}, "Replace with `&apos;`.", {"alt": "1071"}, {"range": "1072", "text": "1073"}, "Replace with `&lsquo;`.", {"alt": "1074"}, {"range": "1075", "text": "1076"}, "Replace with `&#39;`.", {"alt": "1077"}, {"range": "1078", "text": "1079"}, "Replace with `&rsquo;`.", {"range": "1080", "text": "1065"}, {"range": "1081", "text": "1067"}, {"range": "1082", "text": "1065"}, {"range": "1083", "text": "1067"}, {"range": "1084", "text": "1065"}, {"range": "1085", "text": "1067"}, {"range": "1086", "text": "1065"}, {"range": "1087", "text": "1067"}, "Update the dependencies array to be: [navigateImage, selectedImage, selectedImageIndex]", {"range": "1088", "text": "1089"}, {"range": "1090", "text": "1065"}, {"range": "1091", "text": "1067"}, {"range": "1092", "text": "1065"}, {"range": "1093", "text": "1067"}, {"alt": "1068"}, {"range": "1094", "text": "1095"}, {"alt": "1071"}, {"range": "1096", "text": "1097"}, {"alt": "1074"}, {"range": "1098", "text": "1099"}, {"alt": "1077"}, {"range": "1100", "text": "1101"}, {"range": "1102", "text": "1065"}, {"range": "1103", "text": "1067"}, {"range": "1104", "text": "1065"}, {"range": "1105", "text": "1067"}, {"range": "1106", "text": "1065"}, {"range": "1107", "text": "1067"}, {"alt": "1108"}, {"range": "1109", "text": "1110"}, "Replace with `&quot;`.", {"alt": "1111"}, {"range": "1112", "text": "1113"}, "Replace with `&ldquo;`.", {"alt": "1114"}, {"range": "1115", "text": "1116"}, "Replace with `&#34;`.", {"alt": "1117"}, {"range": "1118", "text": "1119"}, "Replace with `&rdquo;`.", {"alt": "1108"}, {"range": "1120", "text": "1121"}, {"alt": "1111"}, {"range": "1122", "text": "1123"}, {"alt": "1114"}, {"range": "1124", "text": "1125"}, {"alt": "1117"}, {"range": "1126", "text": "1127"}, {"alt": "1108"}, {"range": "1128", "text": "1129"}, {"alt": "1111"}, {"range": "1130", "text": "1131"}, {"alt": "1114"}, {"range": "1132", "text": "1133"}, {"alt": "1117"}, {"range": "1134", "text": "1135"}, {"alt": "1108"}, {"range": "1136", "text": "1137"}, {"alt": "1111"}, {"range": "1138", "text": "1139"}, {"alt": "1114"}, {"range": "1140", "text": "1141"}, {"alt": "1117"}, {"range": "1142", "text": "1143"}, {"range": "1144", "text": "1065"}, {"range": "1145", "text": "1067"}, {"range": "1146", "text": "1065"}, {"range": "1147", "text": "1067"}, {"range": "1148", "text": "1065"}, {"range": "1149", "text": "1067"}, {"range": "1150", "text": "1065"}, {"range": "1151", "text": "1067"}, {"alt": "1068"}, {"range": "1152", "text": "1153"}, {"alt": "1071"}, {"range": "1154", "text": "1155"}, {"alt": "1074"}, {"range": "1156", "text": "1157"}, {"alt": "1077"}, {"range": "1158", "text": "1159"}, {"alt": "1108"}, {"range": "1160", "text": "1161"}, {"alt": "1111"}, {"range": "1162", "text": "1163"}, {"alt": "1114"}, {"range": "1164", "text": "1165"}, {"alt": "1117"}, {"range": "1166", "text": "1167"}, {"alt": "1108"}, {"range": "1168", "text": "1169"}, {"alt": "1111"}, {"range": "1170", "text": "1171"}, {"alt": "1114"}, {"range": "1172", "text": "1173"}, {"alt": "1117"}, {"range": "1174", "text": "1175"}, {"alt": "1068"}, {"range": "1176", "text": "1177"}, {"alt": "1071"}, {"range": "1178", "text": "1179"}, {"alt": "1074"}, {"range": "1180", "text": "1181"}, {"alt": "1077"}, {"range": "1182", "text": "1183"}, {"alt": "1068"}, {"range": "1184", "text": "1185"}, {"alt": "1071"}, {"range": "1186", "text": "1187"}, {"alt": "1074"}, {"range": "1188", "text": "1189"}, {"alt": "1077"}, {"range": "1190", "text": "1191"}, {"range": "1192", "text": "1065"}, {"range": "1193", "text": "1067"}, {"range": "1194", "text": "1065"}, {"range": "1195", "text": "1067"}, {"range": "1196", "text": "1065"}, {"range": "1197", "text": "1067"}, {"range": "1198", "text": "1065"}, {"range": "1199", "text": "1067"}, {"range": "1200", "text": "1065"}, {"range": "1201", "text": "1067"}, {"range": "1202", "text": "1065"}, {"range": "1203", "text": "1067"}, {"range": "1204", "text": "1065"}, {"range": "1205", "text": "1067"}, {"range": "1206", "text": "1065"}, {"range": "1207", "text": "1067"}, {"range": "1208", "text": "1065"}, {"range": "1209", "text": "1067"}, {"range": "1210", "text": "1065"}, {"range": "1211", "text": "1067"}, {"alt": "1068"}, {"range": "1212", "text": "1213"}, {"alt": "1071"}, {"range": "1214", "text": "1215"}, {"alt": "1074"}, {"range": "1216", "text": "1217"}, {"alt": "1077"}, {"range": "1218", "text": "1219"}, {"range": "1220", "text": "1065"}, {"range": "1221", "text": "1067"}, {"range": "1222", "text": "1065"}, {"range": "1223", "text": "1067"}, {"range": "1224", "text": "1065"}, {"range": "1225", "text": "1067"}, {"range": "1226", "text": "1065"}, {"range": "1227", "text": "1067"}, "replaceEmptyInterfaceWithSuper", {"range": "1228", "text": "1229"}, "Replace empty interface with a type alias.", {"range": "1230", "text": "1065"}, {"range": "1231", "text": "1067"}, {"range": "1232", "text": "1065"}, {"range": "1233", "text": "1067"}, {"range": "1234", "text": "1065"}, {"range": "1235", "text": "1067"}, {"range": "1236", "text": "1065"}, {"range": "1237", "text": "1067"}, {"range": "1238", "text": "1065"}, {"range": "1239", "text": "1067"}, {"range": "1240", "text": "1065"}, {"range": "1241", "text": "1067"}, {"range": "1242", "text": "1065"}, {"range": "1243", "text": "1067"}, {"range": "1244", "text": "1065"}, {"range": "1245", "text": "1067"}, {"range": "1246", "text": "1065"}, {"range": "1247", "text": "1067"}, {"range": "1248", "text": "1065"}, {"range": "1249", "text": "1067"}, {"range": "1250", "text": "1065"}, {"range": "1251", "text": "1067"}, {"range": "1252", "text": "1065"}, {"range": "1253", "text": "1067"}, {"range": "1254", "text": "1065"}, {"range": "1255", "text": "1067"}, {"range": "1256", "text": "1065"}, {"range": "1257", "text": "1067"}, {"range": "1258", "text": "1065"}, {"range": "1259", "text": "1067"}, {"range": "1260", "text": "1065"}, {"range": "1261", "text": "1067"}, {"range": "1262", "text": "1065"}, {"range": "1263", "text": "1067"}, {"range": "1264", "text": "1065"}, {"range": "1265", "text": "1067"}, {"range": "1266", "text": "1065"}, {"range": "1267", "text": "1067"}, {"range": "1268", "text": "1065"}, {"range": "1269", "text": "1067"}, {"range": "1270", "text": "1065"}, {"range": "1271", "text": "1067"}, {"range": "1272", "text": "1065"}, {"range": "1273", "text": "1067"}, {"range": "1274", "text": "1065"}, {"range": "1275", "text": "1067"}, {"range": "1276", "text": "1065"}, {"range": "1277", "text": "1067"}, {"range": "1278", "text": "1065"}, {"range": "1279", "text": "1067"}, {"range": "1280", "text": "1065"}, {"range": "1281", "text": "1067"}, [3159, 3162], "unknown", [3159, 3162], "never", "&apos;", [4051, 4115], "The album you&apos;re looking for does not exist or has been removed.", "&lsquo;", [4051, 4115], "The album you&lsquo;re looking for does not exist or has been removed.", "&#39;", [4051, 4115], "The album you&#39;re looking for does not exist or has been removed.", "&rsquo;", [4051, 4115], "The album you&rsquo;re looking for does not exist or has been removed.", [5227, 5230], [5227, 5230], [1356, 1359], [1356, 1359], [2158, 2161], [2158, 2161], [2528, 2531], [2528, 2531], [4860, 4895], "[navigateImage, selectedImage, selectedImageIndex]", [476, 479], [476, 479], [510, 513], [510, 513], [6369, 6415], ". Here&apos;s an overview of your website.\n        ", [6369, 6415], ". Here&lsquo;s an overview of your website.\n        ", [6369, 6415], ". Here&#39;s an overview of your website.\n        ", [6369, 6415], ". Here&rsquo;s an overview of your website.\n        ", [1891, 1894], [1891, 1894], [1957, 1960], [1957, 1960], [2636, 2639], [2636, 2639], "&quot;", [2521, 2553], "\n                Searching for &quot;", "&ldquo;", [2521, 2553], "\n                Searching for &ldquo;", "&#34;", [2521, 2553], "\n                Searching for &#34;", "&rdquo;", [2521, 2553], "\n                Searching for &rdquo;", [2565, 2584], "&quot;...\n              ", [2565, 2584], "&ldquo;...\n              ", [2565, 2584], "&#34;...\n              ", [2565, 2584], "&rdquo;...\n              ", [2661, 2675], " results for &quot;", [2661, 2675], " results for &ldquo;", [2661, 2675], " results for &#34;", [2661, 2675], " results for &rdquo;", [2687, 2703], "&quot;\n              ", [2687, 2703], "&ldquo;\n              ", [2687, 2703], "&#34;\n              ", [2687, 2703], "&rdquo;\n              ", [3749, 3752], [3749, 3752], [5225, 5228], [5225, 5228], [6542, 6545], [6542, 6545], [7636, 7639], [7636, 7639], [8777, 8824], "\n            We couldn&apos;t find any matches for \"", [8777, 8824], "\n            We couldn&lsquo;t find any matches for \"", [8777, 8824], "\n            We couldn&#39;t find any matches for \"", [8777, 8824], "\n            We couldn&rsquo;t find any matches for \"", [8777, 8824], "\n            We couldn't find any matches for &quot;", [8777, 8824], "\n            We couldn't find any matches for &ldquo;", [8777, 8824], "\n            We couldn't find any matches for &#34;", [8777, 8824], "\n            We couldn't find any matches for &rdquo;", [8836, 8876], "&quot;. Please try another search.\n          ", [8836, 8876], "&ldquo;. Please try another search.\n          ", [8836, 8876], "&#34;. Please try another search.\n          ", [8836, 8876], "&rdquo;. Please try another search.\n          ", [1874, 2017], "\n          These are the current initiatives we&apos;re tracking. Visit our How to Donate page to learn more about supporting these causes.\n        ", [1874, 2017], "\n          These are the current initiatives we&lsquo;re tracking. Visit our How to Donate page to learn more about supporting these causes.\n        ", [1874, 2017], "\n          These are the current initiatives we&#39;re tracking. Visit our How to Donate page to learn more about supporting these causes.\n        ", [1874, 2017], "\n          These are the current initiatives we&rsquo;re tracking. Visit our How to Donate page to learn more about supporting these causes.\n        ", [1422, 1574], "\n        This component will cause an async error that won&apos;t be caught by the ErrorBoundary.\n        It will be caught by the GlobalErrorHandler.\n      ", [1422, 1574], "\n        This component will cause an async error that won&lsquo;t be caught by the ErrorBoundary.\n        It will be caught by the GlobalErrorHandler.\n      ", [1422, 1574], "\n        This component will cause an async error that won&#39;t be caught by the ErrorBoundary.\n        It will be caught by the GlobalErrorHandler.\n      ", [1422, 1574], "\n        This component will cause an async error that won&rsquo;t be caught by the ErrorBoundary.\n        It will be caught by the GlobalErrorHandler.\n      ", [2548, 2551], [2548, 2551], [3049, 3052], [3049, 3052], [303, 306], [303, 306], [321, 324], [321, 324], [461, 464], [461, 464], [495, 498], [495, 498], [2472, 2475], [2472, 2475], [2505, 2508], [2505, 2508], [6502, 6505], [6502, 6505], [1563, 1566], [1563, 1566], [2522, 2613], "\n            Thank you for subscribing! We&apos;ve sent a confirmation to your email.\n          ", [2522, 2613], "\n            Thank you for subscribing! We&lsquo;ve sent a confirmation to your email.\n          ", [2522, 2613], "\n            Thank you for subscribing! We&#39;ve sent a confirmation to your email.\n          ", [2522, 2613], "\n            Thank you for subscribing! We&rsquo;ve sent a confirmation to your email.\n          ", [149, 152], [149, 152], [606, 609], [606, 609], [1136, 1139], [1136, 1139], [457, 460], [457, 460], [73, 159], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [843, 846], [843, 846], [2822, 2825], [2822, 2825], [8164, 8167], [8164, 8167], [9745, 9748], [9745, 9748], [9912, 9915], [9912, 9915], [11587, 11590], [11587, 11590], [2885, 2888], [2885, 2888], [2932, 2935], [2932, 2935], [3191, 3194], [3191, 3194], [4616, 4619], [4616, 4619], [4977, 4980], [4977, 4980], [5646, 5649], [5646, 5649], [432, 435], [432, 435], [1099, 1102], [1099, 1102], [1603, 1606], [1603, 1606], [1726, 1729], [1726, 1729], [2158, 2161], [2158, 2161], [320, 323], [320, 323], [2004, 2007], [2004, 2007], [2290, 2293], [2290, 2293], [1401, 1404], [1401, 1404], [1407, 1410], [1407, 1410], [2348, 2351], [2348, 2351], [2676, 2679], [2676, 2679], [3144, 3147], [3144, 3147], [644, 647], [644, 647]]