import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals", "next/typescript"),
  {
    rules: {
      // Disable img element warnings for now - can be addressed later
      "@next/next/no-img-element": "off",
      // Disable alt text warnings for now - can be addressed later
      "jsx-a11y/alt-text": "off",
      // Allow exhaustive deps warnings - can be addressed later
      "react-hooks/exhaustive-deps": "warn",
    },
  },
];

export default eslintConfig;
