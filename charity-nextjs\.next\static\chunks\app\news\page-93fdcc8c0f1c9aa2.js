(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[324],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>c});var n=a(5155),s=a(2115),i=a(9708),r=a(2085),l=a(9434);let c=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200",{variants:{variant:{default:"bg-charity-primary text-white hover:bg-charity-secondary",destructive:"bg-charity-destructive text-white hover:bg-charity-destructive/90",outline:"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground",secondary:"bg-charity-light text-charity-dark hover:bg-charity-light/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-charity-primary underline-offset-4 hover:underline",accent:"bg-charity-accent text-charity-dark hover:bg-charity-accent/80",success:"bg-charity-success text-white hover:bg-charity-success/90",warning:"bg-charity-warning text-white hover:bg-charity-warning/90",info:"bg-charity-info text-white hover:bg-charity-info/90",donate:"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 py-1.5 text-xs",lg:"h-11 rounded-md px-8 py-2.5",xl:"h-12 rounded-md px-10 py-3 text-base",icon:"h-10 w-10 rounded-full",wide:"h-10 px-8 py-2 w-full"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,t)=>{let{className:a,variant:s,size:r,asChild:o=!1,...d}=e,m=o?i.DX:"button";return(0,n.jsx)(m,{className:(0,l.cn)(c({variant:s,size:r,className:a})),ref:t,...d})});o.displayName="Button"},1008:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},1667:(e,t,a)=>{Promise.resolve().then(a.bind(a,7237))},2138:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2355:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},3052:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4631:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(9946).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},5731:(e,t,a)=>{"use strict";a.d(t,{AY:()=>m,EO:()=>r,TP:()=>c,YV:()=>l,jE:()=>u,l4:()=>d,lM:()=>o});var n=a(3464),s=a(1008);let i=n.A.create({baseURL:s.A.baseURL,timeout:s.A.timeout,withCredentials:s.A.withCredentials});i.interceptors.request.use(async e=>{{let t=localStorage.getItem("authToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(e)});let r={getContent:async()=>(await i.get("/about")).data,updateContent:async e=>(await i.post("/about",e)).data},l={getAll:async e=>({teamMembers:(await i.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await i.get("/team/".concat(e))).data}),create:async e=>(await i.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,t)=>(await i.put("/team/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}})).data,delete:async e=>(await i.delete("/team/".concat(e))).data},c={submit:async e=>(await i.post("/contact",e)).data,getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0,n={page:e,limit:t,...a?{status:a}:{}};return(await i.get("/contact",{params:n})).data},getById:async e=>(await i.get("/contact/".concat(e))).data,updateStatus:async(e,t)=>(await i.put("/contact/".concat(e,"/status"),{status:t})).data,delete:async e=>(await i.delete("/contact/".concat(e))).data},o={getAll:async e=>({locations:(await i.get("/locations",{params:e})).data}),getById:async e=>({location:(await i.get("/locations/".concat(e))).data}),create:async e=>{let t={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await i.post("/locations",t)).data},update:async(e,t)=>{let a={...t,isMainOffice:void 0!==t.isMainOffice?String(t.isMainOffice):void 0,active:void 0!==t.active?String(t.active):void 0};return(await i.put("/locations/".concat(e),a)).data},delete:async e=>(await i.delete("/locations/".concat(e))).data},d={getAll:async()=>(await i.get("/faqs")).data,getAllAdmin:async()=>(await i.get("/admin/faqs")).data,getById:async e=>(await i.get("/admin/faqs/".concat(e))).data,create:async e=>(await i.post("/admin/faqs",e)).data,update:async function(e,t){let a,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(n)a={isActive:void 0===t.isActive||!!t.isActive};else{var s,r,l;a={question:null==(s=t.question)?void 0:s.trim(),answer:null==(r=t.answer)?void 0:r.trim(),category:(null==(l=t.category)?void 0:l.trim())||"General",order:"number"==typeof t.order?t.order:0,isActive:void 0===t.isActive||!!t.isActive}}try{return(await i.put("/admin/faqs/".concat(e),a)).data}catch(e){throw e}},delete:async e=>(await i.delete("/admin/faqs/".concat(e))).data},m={getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await i.get("/news?page=".concat(e,"&limit=").concat(t,"&includeAttachments=true"))).data},getBySlug:async e=>(await i.get("/news/".concat(e,"?includeAttachments=true"))).data.news,getById:async e=>(await i.get("/admin/news/".concat(e))).data.news,create:async e=>(await i.post("/admin/news",e)).data.news,update:async(e,t)=>(await i.put("/admin/news/".concat(e),t)).data.news,delete:async e=>(await i.delete("/admin/news/".concat(e))).data},u={getAllAlbums:async()=>(await i.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await i.get("/gallery/albums/".concat(e))).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await i.get("/admin/gallery/albums/".concat(e))).data}catch(e){throw e}},createAlbum:async e=>(await i.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,t)=>(await i.put("/admin/gallery/albums/".concat(e),t)).data.album,deleteAlbum:async e=>{await i.delete("/admin/gallery/albums/".concat(e))},uploadImage:async(e,t)=>{var a;let n=null==(a=document.querySelector('meta[name="csrf-token"]'))?void 0:a.getAttribute("content");return n&&t.append("_csrf",n),(await i.post("/admin/gallery/albums/".concat(e,"/images"),t,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await i.delete("/admin/gallery/images/".concat(e))}}},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>r,aR:()=>l,wL:()=>m});var n=a(5155),s=a(2115),i=a(9434);let r=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...s})});r.displayName="Card";let l=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),...s})});l.displayName="CardHeader";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",a),...s})});c.displayName="CardTitle";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...s})});o.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",a),...s})});d.displayName="CardContent";let m=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",a),...s})});m.displayName="CardFooter"},7237:(e,t,a)=>{"use strict";a.d(t,{NewsPageContent:()=>M});var n=a(5155),s=a(2115),i=a(2960),r=a(6874),l=a.n(r),c=a(4631),o=a(9074),d=a(2138),m=a(3319),u=a(6695),h=a(2355),p=a(3052);let g=(0,a(9946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);var y=a(9434),f=a(285);let x=e=>{let{className:t,...a}=e;return(0,n.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,y.cn)("mx-auto flex w-full justify-center",t),...a})};x.displayName="Pagination";let v=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("ul",{ref:t,className:(0,y.cn)("flex flex-row items-center gap-1",a),...s})});v.displayName="PaginationContent";let w=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,n.jsx)("li",{ref:t,className:(0,y.cn)("",a),...s})});w.displayName="PaginationItem";let b=e=>{let{className:t,isActive:a,size:s="icon",...i}=e;return(0,n.jsx)("a",{"aria-current":a?"page":void 0,className:(0,y.cn)((0,f.r)({variant:a?"outline":"ghost",size:s}),t),...i})};b.displayName="PaginationLink";let j=e=>{let{className:t,...a}=e;return(0,n.jsxs)(b,{"aria-label":"Go to previous page",size:"default",className:(0,y.cn)("gap-1 pl-2.5",t),...a,children:[(0,n.jsx)(h.A,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Previous"})]})};j.displayName="PaginationPrevious";let N=e=>{let{className:t,...a}=e;return(0,n.jsxs)(b,{"aria-label":"Go to next page",size:"default",className:(0,y.cn)("gap-1 pr-2.5",t),...a,children:[(0,n.jsx)("span",{children:"Next"}),(0,n.jsx)(p.A,{className:"h-4 w-4"})]})};N.displayName="PaginationNext";let A=e=>{let{className:t,...a}=e;return(0,n.jsxs)("span",{"aria-hidden":!0,className:(0,y.cn)("flex h-9 w-9 items-center justify-center",t),...a,children:[(0,n.jsx)(g,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"More pages"})]})};A.displayName="PaginationEllipsis";var k=a(5731),C=a(1008);function M(){let[e,t]=(0,s.useState)(1),[a,r]=(0,s.useState)({}),[h,p]=(0,s.useState)({}),{data:g,isLoading:f,error:M}=(0,i.I)({queryKey:["news",e],queryFn:()=>k.AY.getAll(e)});(0,s.useEffect)(()=>{if((null==g?void 0:g.news)&&g.news.length>0){let e={},t={};g.news.forEach(a=>{if(a.attachments&&a.attachments.length>0){let n=a.attachments.find(e=>e.mimetype&&e.mimetype.startsWith("image/"));if(n){let s="".concat(C.A.baseURL,"/api/news/attachments/").concat(n._id,"/content");e[a._id]=s,t[a._id]=!1}}}),r(e),p(t)}},[g]);let _=e=>{p(t=>({...t,[e]:!0}))},R=e=>{r(t=>{let a={...t};return delete a[e],a}),p(t=>({...t,[e]:!0}))};return f?(0,n.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,n.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,n.jsx)(c.A,{className:"h-8 w-8 animate-spin text-primary"})})}):M||!g?(0,n.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,n.jsxs)(u.Zp,{className:"border-red-200 bg-red-50",children:[(0,n.jsxs)(u.aR,{children:[(0,n.jsx)(u.ZB,{className:"text-red-800",children:"Error"}),(0,n.jsx)(u.BT,{className:"text-red-700",children:"Failed to load news"})]}),(0,n.jsx)(u.Wu,{children:(0,n.jsx)("p",{className:"text-red-600",children:M instanceof Error?M.message:"Unknown error occurred"})})]})}):(0,n.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Latest News"}),(0,n.jsx)("div",{className:"news-grid",children:g.news.map((e,t)=>{let s=a[e._id],i=h[e._id]||!s;return(0,n.jsx)(l(),{href:"/news/".concat(e.slug),className:"group block",children:(0,n.jsxs)(u.Zp,{className:(0,y.cn)("overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 h-full flex flex-col",0===t&&"md:col-span-2 md:row-span-2"),children:[s&&(0,n.jsxs)("div",{className:(0,y.cn)("relative overflow-hidden bg-gray-100",0===t?"aspect-[16/10]":"aspect-video"),children:[!i&&(0,n.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,n.jsx)(c.A,{className:"h-6 w-6 animate-spin text-gray-400"})}),(0,n.jsx)("img",{src:s,alt:e.title,className:(0,y.cn)("w-full h-full object-cover transition-all duration-700 group-hover:scale-105",!i&&"opacity-0"),onLoad:()=>_(e._id),onError:()=>R(e._id),loading:"lazy"}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,n.jsxs)(u.Wu,{className:"p-5",children:[(0,n.jsx)("h3",{className:"text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300",children:e.title}),(0,n.jsx)("div",{className:"line-clamp-3 text-sm text-gray-600",dangerouslySetInnerHTML:{__html:(0,y.oH)(e.body.substring(0,150)+"...")}})]}),(0,n.jsxs)(u.wL,{className:"px-5 pb-5 pt-0 flex flex-col gap-2 w-full",children:[(0,n.jsxs)("div",{className:"flex items-center text-gray-500 w-full",children:[(0,n.jsx)(o.A,{className:"h-4 w-4 mr-2"}),(0,m.GP)(new Date(e.publishDate),"MMMM d, yyyy")]}),(0,n.jsxs)("div",{className:"text-primary font-medium flex items-center w-full",children:["Read More ",(0,n.jsx)(d.A,{className:"h-4 w-4 ml-1 transition-transform duration-300 group-hover:translate-x-1"})]})]})]})},e._id)})}),g.pagination&&g.pagination.pages>1&&(0,n.jsx)(x,{className:"mt-8",children:(0,n.jsxs)(v,{children:[(0,n.jsx)(w,{children:(0,n.jsx)(j,{href:"#",onClick:a=>{a.preventDefault(),e>1&&t(e-1)},className:1===e?"pointer-events-none opacity-50":""})}),e>2&&(0,n.jsx)(w,{children:(0,n.jsx)(b,{href:"#",onClick:e=>{e.preventDefault(),t(1)},children:"1"})}),e>3&&(0,n.jsx)(w,{children:(0,n.jsx)(A,{})}),(0,n.jsx)(w,{children:(0,n.jsx)(b,{href:"#",isActive:!0,children:e})}),e<g.pagination.pages&&(0,n.jsx)(w,{children:(0,n.jsx)(b,{href:"#",onClick:a=>{a.preventDefault(),t(e+1)},children:e+1})}),e<g.pagination.pages-2&&(0,n.jsx)(w,{children:(0,n.jsx)(A,{})}),e<g.pagination.pages-1&&(0,n.jsx)(w,{children:(0,n.jsx)(b,{href:"#",onClick:e=>{e.preventDefault(),t(g.pagination.pages)},children:g.pagination.pages})}),(0,n.jsx)(w,{children:(0,n.jsx)(N,{href:"#",onClick:a=>{a.preventDefault(),e<g.pagination.pages&&t(e+1)},className:e===g.pagination.pages?"pointer-events-none opacity-50":""})})]})})]})}},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i,oH:()=>r,vV:()=>l});var n=a(2596),s=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,n.$)(t))}function r(e){return e.replace(/\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g,(e,t)=>{let a=t.split(",").map(e=>e.trim()).filter(Boolean);if(0===a.length)return e;let n=a.map(e=>'<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">'.concat(e,"</span>")).join("");return'<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">'.concat(n,"</div></div>")})}let l=(e,t)=>{}}},e=>{var t=t=>e(e.s=t);e.O(0,[598,967,951,874,38,441,684,358],()=>t(1667)),_N_E=e.O()}]);