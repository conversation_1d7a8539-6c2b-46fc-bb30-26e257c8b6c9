import React, { useState, useEffect, useRef } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Award, Save, ArrowLeft, Loader2, Upload, X, Link as LinkIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { partnersApi } from "@/services/api";

interface Partner {
  _id: string;
  name: string;
  logo: string;
  website?: string;
  description?: string;
  partnerType: 'sponsor' | 'partner' | 'supporter';
  featured: boolean;
  displayOrder: number;
  active: boolean;
}

export function AdminPartnerEditPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const isEditMode = !!id;

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    website: "",
    description: "",
    partnerType: "partner",
    featured: false,
    displayOrder: 0,
    active: true,
  });

  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  // Fetch partner data if in edit mode
  const { data: partnerData, isLoading: isLoadingPartner } = useQuery({
    queryKey: ["partner", id],
    queryFn: () => partnersApi.getById(id!),
    enabled: isEditMode,
  });

  // Update form data when partner data is loaded
  useEffect(() => {
    if (partnerData?.partner) {
      const partner = partnerData.partner;
      setFormData({
        name: partner.name,
        website: partner.website || "",
        description: partner.description || "",
        partnerType: partner.partnerType,
        featured: partner.featured,
        displayOrder: partner.displayOrder,
        active: partner.active,
      });

      if (partner.logo) {
        setLogoPreview(`${import.meta.env.VITE_API_URL || 'http://localhost:5000'}${partner.logo}`);
      }
    }
  }, [partnerData]);

  // Create mutation
  const createMutation = useMutation({
    mutationFn: (formData: FormData) => partnersApi.create(formData),
    onSuccess: () => {
      toast({
        title: "Partner created",
        description: "The partner has been successfully created.",
      });
      queryClient.invalidateQueries({ queryKey: ["partners"] });
      navigate("/admin/partners");
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to create partner. Please try again.",
      });
      console.error("Create error:", error);
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, formData }: { id: string; formData: FormData }) =>
      partnersApi.update(id, formData),
    onSuccess: () => {
      toast({
        title: "Partner updated",
        description: "The partner has been successfully updated.",
      });
      queryClient.invalidateQueries({ queryKey: ["partners"] });
      queryClient.invalidateQueries({ queryKey: ["partner", id] });
      navigate("/admin/partners");
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update partner. Please try again.",
      });
      console.error("Update error:", error);
    },
  });

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle number input changes
  const handleNumberChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: parseInt(value) || 0 }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle switch changes
  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  // Handle logo file selection
  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setLogoFile(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Clear logo selection
  const handleClearLogo = () => {
    setLogoFile(null);
    setLogoPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Partner name is required.",
      });
      return;
    }

    if (!isEditMode && !logoFile) {
      toast({
        variant: "destructive",
        title: "Validation Error",
        description: "Logo image is required.",
      });
      return;
    }

    // Create FormData object
    const submitFormData = new FormData();
    submitFormData.append("name", formData.name);
    submitFormData.append("website", formData.website);
    submitFormData.append("description", formData.description);
    submitFormData.append("partnerType", formData.partnerType);
    submitFormData.append("featured", formData.featured.toString());
    submitFormData.append("displayOrder", formData.displayOrder.toString());
    submitFormData.append("active", formData.active.toString());

    // Add logo file if selected
    if (logoFile) {
      submitFormData.append("logo", logoFile);
    }

    if (isEditMode && id) {
      updateMutation.mutate({ id, formData: submitFormData });
    } else {
      createMutation.mutate(submitFormData);
    }
  };

  const isLoading = isLoadingPartner || createMutation.isPending || updateMutation.isPending;

  return (
    <div className="container mx-auto p-6">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate("/admin/partners")}
          className="mr-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" /> Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">
            {isEditMode ? "Edit Partner" : "Add New Partner"}
          </h1>
          <p className="text-gray-600 mt-1">
            {isEditMode
              ? "Update the details of this partner"
              : "Create a new partner or sponsor"}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Partner Details</CardTitle>
                <CardDescription>
                  Basic information about this partner
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Partner Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="e.g., Company Name"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Website URL</Label>
                  <div className="relative">
                    <LinkIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                    <Input
                      id="website"
                      name="website"
                      value={formData.website}
                      onChange={handleInputChange}
                      placeholder="https://example.com"
                      className="pl-10"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Brief description of this partner"
                    rows={3}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="partnerType">Partner Type *</Label>
                  <Select
                    value={formData.partnerType}
                    onValueChange={(value) => handleSelectChange("partnerType", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select partner type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sponsor">Sponsor</SelectItem>
                      <SelectItem value="partner">Partner</SelectItem>
                      <SelectItem value="supporter">Supporter</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card className="mt-6">
              <CardHeader>
                <CardTitle>Logo</CardTitle>
                <CardDescription>
                  Upload a logo image for this partner
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-center p-6">
                    {logoPreview ? (
                      <div className="relative">
                        <img
                          src={logoPreview}
                          alt="Logo preview"
                          className="max-h-40 max-w-full object-contain"
                          onError={(e) => {
                            console.error(`Failed to load logo preview`);
                            e.currentTarget.src = '/placeholder.svg';
                          }}
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="icon"
                          className="absolute -top-3 -right-3 h-7 w-7 rounded-full"
                          onClick={handleClearLogo}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ) : (
                      <div className="text-center">
                        <Award className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                        <div className="text-sm text-gray-600 mb-2">
                          {isEditMode ? "Upload a new logo" : "Upload partner logo"}
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                        >
                          <Upload className="h-4 w-4 mr-2" /> Select Image
                        </Button>
                      </div>
                    )}
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      className="hidden"
                      onChange={handleLogoChange}
                    />
                  </div>
                  <div className="text-sm text-gray-500">
                    Recommended: PNG or SVG with transparent background, square aspect ratio
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader>
                <CardTitle>Display Settings</CardTitle>
                <CardDescription>
                  Configure how this partner appears
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="featured">Featured</Label>
                    <p className="text-sm text-gray-500">
                      Highlight this partner on the website
                    </p>
                  </div>
                  <Switch
                    id="featured"
                    checked={formData.featured}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("featured", checked)
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="active">Active</Label>
                    <p className="text-sm text-gray-500">
                      Show this partner on the website
                    </p>
                  </div>
                  <Switch
                    id="active"
                    checked={formData.active}
                    onCheckedChange={(checked) =>
                      handleSwitchChange("active", checked)
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="displayOrder">Display Order</Label>
                  <Input
                    id="displayOrder"
                    name="displayOrder"
                    type="number"
                    min="0"
                    value={formData.displayOrder}
                    onChange={handleNumberChange}
                    placeholder="0"
                  />
                  <p className="text-xs text-gray-500">
                    Lower numbers appear first in the list
                  </p>
                </div>
              </CardContent>
              <CardFooter>
                <Button
                  type="submit"
                  className="w-full"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      {isEditMode ? "Updating..." : "Creating..."}
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      {isEditMode ? "Update Partner" : "Create Partner"}
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
