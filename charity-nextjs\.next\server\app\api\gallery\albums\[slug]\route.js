(()=>{var e={};e.id=965,e.ids=[965],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67592:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>p});var a=r(96559),o=r(48088),n=r(37719),i=r(32190);let u=process.env.BACKEND_URL||"http://localhost:5000";async function p(e,{params:t}){try{let{slug:r}=await t,s=new URL(e.url).searchParams,a=`${u}/api/gallery/albums/${r}?${s.toString()}`,o=await fetch(a,{method:"GET",headers:{"Content-Type":"application/json"}});if(!o.ok){if(404===o.status)return i.NextResponse.json({error:"Album not found"},{status:404});throw Error(`Backend responded with status: ${o.status}`)}let n=await o.json();return i.NextResponse.json(n)}catch(e){return i.NextResponse.json({error:"Failed to fetch album"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/gallery/albums/[slug]/route",pathname:"/api/gallery/albums/[slug]",filename:"route",bundlePath:"app/api/gallery/albums/[slug]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\gallery\\albums\\[slug]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:h}=l;function x(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},78335:()=>{},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(67592));module.exports=s})();