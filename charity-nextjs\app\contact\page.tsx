import { Metadata } from 'next';
import { ContactPageContent } from './ContactPageContent';

export const metadata: Metadata = {
  title: 'Contact Us - Charity Welcome Hub',
  description: 'Get in touch with us. Send us a message, find our location, and learn how to get involved with our charitable work.',
  keywords: ['contact', 'charity', 'get in touch', 'location', 'volunteer', 'support'],
  openGraph: {
    title: 'Contact Us - Charity Welcome Hub',
    description: 'Get in touch with us. Send us a message, find our location, and learn how to get involved with our charitable work.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact Us - Charity Welcome Hub',
    description: 'Get in touch with us. Send us a message, find our location, and learn how to get involved with our charitable work.',
  },
};

export default function ContactPage() {
  return <ContactPageContent />;
}
