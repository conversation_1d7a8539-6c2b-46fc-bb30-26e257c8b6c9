import { Metadata } from 'next';
import { NewsPageContent } from './NewsPageContent';

export const metadata: Metadata = {
  title: 'Latest News - Charity Welcome Hub',
  description: 'Stay updated with the latest news, announcements, and stories from our charity organization and community impact.',
  keywords: ['news', 'updates', 'announcements', 'charity news', 'community', 'stories', 'impact'],
  openGraph: {
    title: 'Latest News - Charity Welcome Hub',
    description: 'Stay updated with the latest news, announcements, and stories from our charity organization and community impact.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Latest News - Charity Welcome Hub',
    description: 'Stay updated with the latest news, announcements, and stories from our charity organization and community impact.',
  },
};

export default function NewsPage() {
  return <NewsPageContent />;
}
