exports.id=775,exports.ids=[775],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n,oH:()=>o,vV:()=>a});var s=r(49384),i=r(82348);function n(...e){return(0,i.QP)((0,s.$)(e))}function o(e){return e.replace(/\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g,(e,t)=>{let r=t.split(",").map(e=>e.trim()).filter(Boolean);if(0===r.length)return e;let s=r.map(e=>`<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">${e}</span>`).join("");return`<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">${s}</div></div>`})}let a=(e,t)=>{}},6717:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},6972:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var s=r(37413),i=r(10974);function n({className:e,...t}){return(0,s.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...t})}},8702:(e,t,r)=>{Promise.resolve().then(r.bind(r,84712)),Promise.resolve().then(r.bind(r,68926)),Promise.resolve().then(r.bind(r,30980)),Promise.resolve().then(r.bind(r,22888))},10974:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(75986),i=r(8974);function n(...e){return(0,i.QP)((0,s.$)(e))}},15736:(e,t,r)=>{"use strict";r.d(t,{Header:()=>x});var s=r(60687),i=r(43210),n=r(85814),o=r.n(n),a=r(16189),l=r(99270),c=r(98712),h=r(11860),d=r(12941),m=r(29523),u=r(89667);function x(){let[e,t]=(0,i.useState)(!1),[r,n]=(0,i.useState)(""),x=(0,a.useRouter)(),p=e=>{e.preventDefault(),r.trim()&&(x.push(`/search?q=${encodeURIComponent(r)}`),n(""),t(!1))};return(0,s.jsxs)("header",{className:"sticky top-0 z-50 bg-charity-muted border-b border-gray-200 shadow-md",children:[(0,s.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between bg-charity-muted",children:[(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsx)(o(),{href:"/",className:"font-bold text-2xl text-primary mr-4",children:"Charity Info"})}),(0,s.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,s.jsx)(o(),{href:"/news",className:"text-gray-700 hover:text-primary font-medium",children:"News"}),(0,s.jsx)(o(),{href:"/gallery",className:"text-gray-700 hover:text-primary font-medium",children:"Gallery"}),(0,s.jsx)(o(),{href:"/about",className:"text-gray-700 hover:text-primary font-medium",children:"About Us"}),(0,s.jsx)(o(),{href:"/contact",className:"text-gray-700 hover:text-primary font-medium",children:"Contact"}),(0,s.jsxs)("form",{onSubmit:p,className:"relative",children:[(0,s.jsx)(u.p,{type:"search",placeholder:"Search...",className:"w-64 pr-10",value:r,onChange:e=>n(e.target.value)}),(0,s.jsx)(m.$,{type:"submit",size:"icon",variant:"ghost",className:"absolute right-0 top-0 h-full",children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})]}),(0,s.jsx)(m.$,{asChild:!0,children:(0,s.jsxs)(o(),{href:"/login",children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"})," Login"]})})]}),(0,s.jsx)(m.$,{variant:"ghost",size:"icon",className:"md:hidden",onClick:()=>t(!e),children:e?(0,s.jsx)(h.A,{className:"h-6 w-6"}):(0,s.jsx)(d.A,{className:"h-6 w-6"})})]}),e&&(0,s.jsx)("div",{className:"md:hidden bg-charity-muted border-t border-gray-200",children:(0,s.jsxs)("nav",{className:"container mx-auto px-4 py-4 space-y-4",children:[(0,s.jsx)(o(),{href:"/news",className:"block text-gray-700 hover:text-primary font-medium",onClick:()=>t(!1),children:"News"}),(0,s.jsx)(o(),{href:"/gallery",className:"block text-gray-700 hover:text-primary font-medium",onClick:()=>t(!1),children:"Gallery"}),(0,s.jsx)(o(),{href:"/about",className:"block text-gray-700 hover:text-primary font-medium",onClick:()=>t(!1),children:"About Us"}),(0,s.jsx)(o(),{href:"/contact",className:"block text-gray-700 hover:text-primary font-medium",onClick:()=>t(!1),children:"Contact"}),(0,s.jsxs)("form",{onSubmit:p,className:"relative",children:[(0,s.jsx)(u.p,{type:"search",placeholder:"Search...",className:"w-full pr-10",value:r,onChange:e=>n(e.target.value)}),(0,s.jsx)(m.$,{type:"submit",size:"icon",variant:"ghost",className:"absolute right-0 top-0 h-full",children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})]}),(0,s.jsx)(m.$,{asChild:!0,className:"w-full",children:(0,s.jsxs)(o(),{href:"/login",onClick:()=>t(!1),children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4"})," Login"]})})]})})]})}},22888:(e,t,r)=>{"use strict";r.d(t,{QueryProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\providers\\QueryProvider.tsx","QueryProvider")},24122:(e,t,r)=>{"use strict";r.d(t,{QueryProvider:()=>a});var s=r(60687),i=r(25217),n=r(8693),o=r(4780);function a({children:e}){let t=new i.E({defaultOptions:{queries:{staleTime:6e4,gcTime:6e5,retry:(e,t)=>(!t?.status||!(t.status>=400)||!(t.status<500)||!![408,429].includes(t.status))&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:"always"},mutations:{retry:!1,onError:e=>{(0,o.vV)("Mutation error:",e)}}}});return(0,s.jsxs)(n.Ht,{client:t,children:[e,!1]})}},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>l});var s=r(60687),i=r(43210),n=r(8730),o=r(24224),a=r(4780);let l=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200",{variants:{variant:{default:"bg-charity-primary text-white hover:bg-charity-secondary",destructive:"bg-charity-destructive text-white hover:bg-charity-destructive/90",outline:"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground",secondary:"bg-charity-light text-charity-dark hover:bg-charity-light/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-charity-primary underline-offset-4 hover:underline",accent:"bg-charity-accent text-charity-dark hover:bg-charity-accent/80",success:"bg-charity-success text-white hover:bg-charity-success/90",warning:"bg-charity-warning text-white hover:bg-charity-warning/90",info:"bg-charity-info text-white hover:bg-charity-info/90",donate:"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 py-1.5 text-xs",lg:"h-11 rounded-md px-8 py-2.5",xl:"h-12 rounded-md px-10 py-3 text-base",icon:"h-10 w-10 rounded-full",wide:"h-10 px-8 py-2 w-full"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef(({className:e,variant:t,size:r,asChild:i=!1,...o},c)=>{let h=i?n.DX:"button";return(0,s.jsx)(h,{className:(0,a.cn)(l({variant:t,size:r,className:e})),ref:c,...o})});c.displayName="Button"},30980:(e,t,r)=>{"use strict";r.d(t,{WebVitals:()=>i});var s=r(12907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx","WebVitals");(0,s.registerClientReference)(function(){throw Error("Attempted to call usePerformanceMetric() from the server but usePerformanceMetric is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx","usePerformanceMetric"),(0,s.registerClientReference)(function(){throw Error("Attempted to call PageLoadMetrics() from the server but PageLoadMetrics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx","PageLoadMetrics")},36573:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},48870:(e,t,r)=>{Promise.resolve().then(r.bind(r,92830)),Promise.resolve().then(r.bind(r,15736)),Promise.resolve().then(r.bind(r,88662)),Promise.resolve().then(r.bind(r,24122))},68178:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>m});var s=r(37413),i=r(1455),n=r.n(i);r(82704);var o=r(68926),a=r(84712),l=r(22888);function c({name:e,description:t,url:r,logo:i,contactPoint:n}){let o={"@context":"https://schema.org","@type":"Organization",name:e,description:t,url:r,...i&&{logo:i},...n&&{contactPoint:n}};return(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(o)}})}function h({name:e,description:t,url:r}){let i={"@context":"https://schema.org","@type":"WebSite",name:e,description:t,url:r,potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:`${r}/search?q={search_term_string}`},"query-input":"required name=search_term_string"}};return(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(i)}})}var d=r(30980);let m={title:{default:"Charity Welcome Hub - Supporting Communities Through Action",template:"%s | Charity Welcome Hub"},description:"Join our mission to support communities through charitable work, transparency, and meaningful impact. Discover our programs, news, and ways to get involved.",keywords:["charity","community","support","donations","volunteer","nonprofit","humanitarian","social impact","community service","charitable organization","helping others","social good"],authors:[{name:"Charity Welcome Hub"}],creator:"Charity Welcome Hub",publisher:"Charity Welcome Hub",metadataBase:new URL(process.env.NEXT_PUBLIC_SITE_URL||"http://localhost:3000"),alternates:{canonical:"/"},openGraph:{title:"Charity Welcome Hub - Supporting Communities Through Action",description:"Join our mission to support communities through charitable work, transparency, and meaningful impact.",type:"website",locale:"en_US",url:"/",siteName:"Charity Welcome Hub"},twitter:{card:"summary_large_image",title:"Charity Welcome Hub - Supporting Communities Through Action",description:"Join our mission to support communities through charitable work, transparency, and meaningful impact.",creator:"@CharityWelcomeHub"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function u({children:e}){return(0,s.jsxs)("html",{lang:"en",className:n().variable,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)(c,{name:"Charity Welcome Hub",description:"Supporting communities through charitable work, transparency, and meaningful impact",url:process.env.NEXT_PUBLIC_SITE_URL||"http://localhost:3000",contactPoint:{telephone:"******-0123",contactType:"customer service",email:"<EMAIL>"}}),(0,s.jsx)(h,{name:"Charity Welcome Hub",description:"Supporting communities through charitable work, transparency, and meaningful impact",url:process.env.NEXT_PUBLIC_SITE_URL||"http://localhost:3000"})]}),(0,s.jsxs)("body",{className:"min-h-screen bg-background font-sans antialiased",children:[(0,s.jsx)(d.WebVitals,{}),(0,s.jsx)(l.QueryProvider,{children:(0,s.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,s.jsx)(o.Header,{}),(0,s.jsx)("main",{className:"flex-1",children:e}),(0,s.jsx)(a.Footer,{})]})})]})]})}},68926:(e,t,r)=>{"use strict";r.d(t,{Header:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Header.tsx","Header")},78335:()=>{},82704:()=>{},84712:(e,t,r)=>{"use strict";r.d(t,{Footer:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Footer.tsx","Footer")},88662:(e,t,r)=>{"use strict";function s(){return null}r.d(t,{WebVitals:()=>s}),r(43210),r(20369)},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var s=r(60687),i=r(43210),n=r(4780);let o=i.forwardRef(({className:e,type:t,...r},i)=>(0,s.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border-[1px] border-input bg-background px-3 py-2 text-base text-foreground ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground/60 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-charity-primary focus-visible:border-charity-primary focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200 md:text-sm shadow-none",e),ref:i,...r}));o.displayName="Input"},92830:(e,t,r)=>{"use strict";r.d(t,{Footer:()=>y});var s=r(60687),i=r(43210),n=r.n(i),o=r(85814),a=r.n(o),l=r(48340),c=r(41550),h=r(97992),d=r(40228),m=r(48224),u=r(9005),x=r(96882),p=r(99270),f=r(44709),v=r(80428),b=r(67760);function y(){let e=new Date().getFullYear(),t={locations:[{isMainOffice:!0,phone:"+****************",email:"<EMAIL>",address:"123 Charity Street, City, State 12345"}]},r=n().useMemo(()=>t?.locations?t.locations.find(e=>e.isMainOffice)||t.locations[0]:null,[t]);return(0,s.jsx)("footer",{className:"bg-teal-50 border-t border-teal-100 py-10 mt-12",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,s.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,s.jsx)("h3",{className:"font-bold text-xl mb-4 text-teal-800",children:"Charity Info"}),(0,s.jsx)("p",{className:"text-teal-700 mb-4",children:"Supporting communities and making a difference"}),(0,s.jsxs)("div",{className:"space-y-2",children:[r?.phone&&(0,s.jsxs)("div",{className:"flex items-center text-teal-700 hover:text-teal-900 transition-colors",children:[(0,s.jsx)(l.A,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{children:r.phone})]}),r?.email&&(0,s.jsxs)("div",{className:"flex items-center text-teal-700 hover:text-teal-900 transition-colors",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("a",{href:`mailto:${r.email}`,className:"hover:text-teal-900",children:r.email})]}),r?.address&&(0,s.jsxs)("div",{className:"flex items-center text-teal-700 hover:text-teal-900 transition-colors",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),(0,s.jsx)("span",{children:r.address})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold mb-4 text-teal-800",children:"Quick Links"}),(0,s.jsxs)("ul",{className:"space-y-3",children:[(0,s.jsx)("li",{children:(0,s.jsxs)(a(),{href:"/",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Home"]})}),(0,s.jsx)("li",{children:(0,s.jsxs)(a(),{href:"/news",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"News"]})}),(0,s.jsx)("li",{children:(0,s.jsxs)(a(),{href:"/gallery",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Gallery"]})}),(0,s.jsx)("li",{children:(0,s.jsxs)(a(),{href:"/about",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"About Us"]})}),(0,s.jsx)("li",{children:(0,s.jsxs)(a(),{href:"/contact",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Contact Us"]})}),(0,s.jsx)("li",{children:(0,s.jsxs)(a(),{href:"/search",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Search"]})})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-bold mb-4 text-teal-800",children:"Resources"}),(0,s.jsxs)("ul",{className:"space-y-3",children:[(0,s.jsx)("li",{children:(0,s.jsxs)(a(),{href:"/faq",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"FAQ"]})}),(0,s.jsx)("li",{children:(0,s.jsxs)(a(),{href:"/donate",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"How to Donate"]})})]})]})]}),(0,s.jsxs)("div",{className:"mt-8 pt-6 border-t border-teal-100 text-center text-teal-700",children:[(0,s.jsxs)("p",{children:["\xa9 ",e," Charity Info. All rights reserved."]}),(0,s.jsxs)("p",{className:"mt-2 flex items-center justify-center text-sm",children:["Built with"," ",(0,s.jsx)(b.A,{className:"h-4 w-4 mx-1 text-pink-500 fill-pink-500"})," for charitable causes"]})]})]})})}},96487:()=>{},99766:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413),i=r(6972);function n(){return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)(i.E,{className:"h-8 w-64 mb-4"}),(0,s.jsx)(i.E,{className:"h-4 w-96"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[...Array(6)].map((e,t)=>(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(i.E,{className:"h-48 w-full rounded-lg"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.E,{className:"h-4 w-full"}),(0,s.jsx)(i.E,{className:"h-4 w-3/4"}),(0,s.jsx)(i.E,{className:"h-4 w-1/2"})]})]},t))})]})}}};