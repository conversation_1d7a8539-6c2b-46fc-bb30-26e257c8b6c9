(()=>{var e={};e.id=784,e.ids=[784],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7043:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>u,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{GET:()=>l});var a=r(96559),o=r(48088),i=r(37719),n=r(32190);let p=process.env.NEXT_PUBLIC_SITE_URL||"http://localhost:3000";async function l(){let e=`User-agent: *
Allow: /

# Disallow admin and API routes
Disallow: /admin/
Disallow: /api/

# Allow specific API routes that should be crawled
Allow: /api/sitemap

# Sitemap location
Sitemap: ${p}/sitemap.xml

# Crawl delay (optional)
Crawl-delay: 1`;return new n.NextResponse(e,{status:200,headers:{"Content-Type":"text/plain","Cache-Control":"public, max-age=86400"}})}let u=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/robots.txt/route",pathname:"/robots.txt",filename:"route",bundlePath:"app/robots.txt/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\robots.txt\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:x}=u;function h(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(7043));module.exports=s})();