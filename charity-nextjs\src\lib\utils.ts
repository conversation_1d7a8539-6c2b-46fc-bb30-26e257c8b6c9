import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Parse and format tags in news content
 * Converts text in the format "**Tags:** tag1, tag2, tag3" to HTML tags
 * @param content - The HTML content to parse
 * @returns Formatted HTML content with tags
 */
export function parseNewsContent(content: string): string {
  // Regular expression to match tag format: **Tags:** tag1, tag2, tag3
  const tagRegex = /\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g;

  // Replace matched tag sections with formatted tags
  return content.replace(tagRegex, (match, tagList) => {
    // Split the tag list by commas and trim whitespace
    const tags = tagList.split(',').map(tag => tag.trim()).filter(Boolean);

    if (tags.length === 0) {
      return match; // No valid tags found, return original text
    }

    // Create HTML for tags
    const tagsHtml = tags.map(tag =>
      `<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">${tag}</span>`
    ).join('');

    return `<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">${tagsHtml}</div></div>`;
  });
}

/**
 * Sanitize sensitive data before logging
 * Removes passwords, tokens, etc.
 */
export const sanitizeData = (data: any): any => {
  if (!data || typeof data !== 'object') return data;

  // Create a shallow copy to avoid modifying the original
  const sanitized = { ...data };

  // List of sensitive fields to mask
  const sensitiveFields = [
    'password', 'passwordHash', 'token', 'secret', 'apiKey',
    'authorization', 'accessToken', 'refreshToken', 'csrf',
    'cookie', 'session', 'key', 'credential', 'auth'
  ];

  // Mask sensitive fields
  Object.keys(sanitized).forEach(key => {
    const lowerKey = key.toLowerCase();
    if (sensitiveFields.some(field => lowerKey.includes(field))) {
      sanitized[key] = '[REDACTED]';
    } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      // Recursively sanitize nested objects
      sanitized[key] = sanitizeData(sanitized[key]);
    }
  });

  return sanitized;
};

/**
 * Log information to the console in development mode
 */
export const logInfo = (message: string, data?: any): void => {
  if (process.env.NODE_ENV !== 'production') {
    if (data) {
      console.log(message, sanitizeData(data));
    } else {
      console.log(message);
    }
  }
};

/**
 * Log errors to the console
 * Always logs in production, but sanitizes sensitive data
 */
export const logError = (message: string, error?: any): void => {
  if (error instanceof Error) {
    // For Error objects, log the message and stack
    console.error(message, {
      message: error.message,
      stack: error.stack,
    });
  } else if (error) {
    // For other data, sanitize before logging
    console.error(message, sanitizeData(error));
  } else {
    console.error(message);
  }
};

/**
 * Log warnings to the console in development mode
 */
export const logWarning = (message: string, data?: any): void => {
  if (process.env.NODE_ENV !== 'production') {
    if (data) {
      console.warn(message, sanitizeData(data));
    } else {
      console.warn(message);
    }
  }
};


