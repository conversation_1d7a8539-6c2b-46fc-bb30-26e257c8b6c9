'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

export default function ScrollToTop() {
  const pathname = usePathname();

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Scroll to top when the route changes
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: 'auto'
    });

    // Log for debugging in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Scrolled to top for path: ${pathname}`);
    }
  }, [pathname]);

  return null;
}
