import { Metadata } from 'next';
import { NewsDetailPageContent } from './NewsDetailPageContent';
import { newsApi } from '@/lib/api';
// import { NewsStructuredData, BreadcrumbStructuredData } from '@/components/seo/StructuredData';

type Props = {
  params: { slug: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const slug = params.slug;

  try {
    // Fetch the news article for metadata
    const article = await newsApi.getBySlug(slug);
    const description = article.body.replace(/<[^>]*>/g, '').substring(0, 160);

    return {
      title: article.title,
      description,
      keywords: ['news', 'article', 'charity', 'updates', 'community', article.title.toLowerCase()],
      authors: [{ name: typeof article.author === 'string' ? article.author : article.author?.username || 'Charity Welcome Hub' }],
      publishedTime: article.publishDate,
      openGraph: {
        title: article.title,
        description,
        type: 'article',
        publishedTime: article.publishDate,
        authors: [typeof article.author === 'string' ? article.author : article.author?.username || 'Charity Welcome Hub'],
        section: 'News',
      },
      twitter: {
        card: 'summary_large_image',
        title: article.title,
        description,
      },
      alternates: {
        canonical: `/news/${slug}`,
      },
    };
  } catch {
    // Fallback metadata if article fetch fails
    return {
      title: `News Article - Charity Welcome Hub`,
      description: 'Read the latest news and updates from our charity organization.',
      keywords: ['news', 'article', 'charity', 'updates', 'community'],
      openGraph: {
        title: `News Article - Charity Welcome Hub`,
        description: 'Read the latest news and updates from our charity organization.',
        type: 'article',
      },
      twitter: {
        card: 'summary_large_image',
        title: `News Article - Charity Welcome Hub`,
        description: 'Read the latest news and updates from our charity organization.',
      },
    };
  }
}

export default function NewsDetailPage({ params }: Props) {
  return <NewsDetailPageContent slug={params.slug} />;
}
