'use client';

import React, { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import Link from 'next/link';
import { Loader, Calendar, ArrowRight } from 'lucide-react';
import { format } from 'date-fns';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { newsApi } from '@/lib/api';
import { parseNewsContent } from '@/lib/utils';
import { cn } from '@/lib/utils';
import API_CONFIG from '@/config/api';

export function NewsPageContent() {
  const [currentPage, setCurrentPage] = useState(1);
  const [featuredImages, setFeaturedImages] = useState<Record<string, string>>({});
  const [imagesLoaded, setImagesLoaded] = useState<Record<string, boolean>>({});

  const { data, isLoading, error } = useQuery({
    queryKey: ['news', currentPage],
    queryFn: () => newsApi.getAll(currentPage),
  });

  // Set featured images directly from the news data
  useEffect(() => {
    if (data?.news && data.news.length > 0) {
      // Process all articles at once to set featured images
      const newFeaturedImages: Record<string, string> = {};
      const newImagesLoaded: Record<string, boolean> = {};

      data.news.forEach((article) => {
        if (article.attachments && article.attachments.length > 0) {
          // Find the first image attachment
          const imageAttachment = article.attachments.find(att =>
            att.mimeType && att.mimeType.startsWith('image/')
          );
          
          if (imageAttachment) {
            const imageUrl = `${API_CONFIG.baseURL}/api/news/attachments/${imageAttachment._id}/content`;
            newFeaturedImages[article._id] = imageUrl;
            newImagesLoaded[article._id] = false;
          }
        }
      });

      setFeaturedImages(newFeaturedImages);
      setImagesLoaded(newImagesLoaded);
    }
  }, [data]);

  // Handle image load
  const handleImageLoad = (articleId: string) => {
    setImagesLoaded(prev => ({
      ...prev,
      [articleId]: true
    }));
  };

  // Handle image error
  const handleImageError = (articleId: string) => {
    setFeaturedImages(prev => {
      const updated = { ...prev };
      delete updated[articleId];
      return updated;
    });
    setImagesLoaded(prev => ({
      ...prev,
      [articleId]: true
    }));
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <Loader className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error</CardTitle>
            <CardDescription className="text-red-700">
              Failed to load news
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">
              {error instanceof Error ? error.message : 'Unknown error occurred'}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Latest News</h1>

      <div className="news-grid">
        {data.news.map((article, index) => {
          const hasImage = featuredImages[article._id];
          const isLoaded = imagesLoaded[article._id] || !hasImage;

          return (
            <Link key={article._id} href={`/news/${article.slug}`} className="group block">
              <Card className={cn(
                "overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 h-full flex flex-col",
                index === 0 && "md:col-span-2 md:row-span-2"
              )}>
                {hasImage && (
                  <div className={cn(
                    "relative overflow-hidden bg-gray-100",
                    index === 0 ? "aspect-[16/10]" : "aspect-video"
                  )}>
                    {!isLoaded && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Loader className="h-6 w-6 animate-spin text-gray-400" />
                      </div>
                    )}
                    <img
                      src={hasImage}
                      alt={article.title}
                      className={cn(
                        "w-full h-full object-cover transition-all duration-700 group-hover:scale-105",
                        !isLoaded && "opacity-0"
                      )}
                      onLoad={() => handleImageLoad(article._id)}
                      onError={() => handleImageError(article._id)}
                      loading="lazy"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  </div>
                )}
                <CardContent className="p-5">
                  <h3 className="text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300">{article.title}</h3>
                  <div
                    className="line-clamp-3 text-sm text-gray-600"
                    dangerouslySetInnerHTML={{
                      __html: parseNewsContent(article.body.substring(0, 150) + '...')
                    }}
                  />
                </CardContent>
                <CardFooter className="px-5 pb-5 pt-0 flex flex-col gap-2 w-full">
                  <div className="flex items-center text-gray-500 w-full">
                    <Calendar className="h-4 w-4 mr-2" />
                    {format(new Date(article.publishDate), 'MMMM d, yyyy')}
                  </div>
                  <div className="text-primary font-medium flex items-center w-full">
                    Read More <ArrowRight className="h-4 w-4 ml-1 transition-transform duration-300 group-hover:translate-x-1" />
                  </div>
                </CardFooter>
              </Card>
            </Link>
          );
        })}
      </div>

      {/* Pagination */}
      {data.pagination && data.pagination.pages > 1 && (
        <Pagination className="mt-8">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage > 1) setCurrentPage(currentPage - 1);
                }}
                className={currentPage === 1 ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>

            {/* First page */}
            {currentPage > 2 && (
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={(e) => { e.preventDefault(); setCurrentPage(1); }}
                >
                  1
                </PaginationLink>
              </PaginationItem>
            )}

            {/* Ellipsis */}
            {currentPage > 3 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Current page */}
            <PaginationItem>
              <PaginationLink href="#" isActive>
                {currentPage}
              </PaginationLink>
            </PaginationItem>

            {/* Next pages */}
            {currentPage < data.pagination.pages && (
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={(e) => { e.preventDefault(); setCurrentPage(currentPage + 1); }}
                >
                  {currentPage + 1}
                </PaginationLink>
              </PaginationItem>
            )}

            {/* Ellipsis */}
            {currentPage < data.pagination.pages - 2 && (
              <PaginationItem>
                <PaginationEllipsis />
              </PaginationItem>
            )}

            {/* Last page */}
            {currentPage < data.pagination.pages - 1 && (
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={(e) => { e.preventDefault(); setCurrentPage(data.pagination.pages); }}
                >
                  {data.pagination.pages}
                </PaginationLink>
              </PaginationItem>
            )}

            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  if (currentPage < data.pagination.pages) setCurrentPage(currentPage + 1);
                }}
                className={currentPage === data.pagination.pages ? 'pointer-events-none opacity-50' : ''}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  );
}
