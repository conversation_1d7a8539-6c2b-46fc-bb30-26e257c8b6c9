import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200",
  {
    variants: {
      variant: {
        default: "bg-charity-primary text-white hover:bg-charity-secondary",
        destructive:
          "bg-charity-destructive text-white hover:bg-charity-destructive/90",
        outline:
          "border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-charity-light text-charity-dark hover:bg-charity-light/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-charity-primary underline-offset-4 hover:underline",
        accent: "bg-charity-accent text-charity-dark hover:bg-charity-accent/80",
        success: "bg-charity-success text-white hover:bg-charity-success/90",
        warning: "bg-charity-warning text-white hover:bg-charity-warning/90",
        info: "bg-charity-info text-white hover:bg-charity-info/90",
        donate: "bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3 py-1.5 text-xs",
        lg: "h-11 rounded-md px-8 py-2.5",
        xl: "h-12 rounded-md px-10 py-3 text-base",
        icon: "h-10 w-10 rounded-full",
        wide: "h-10 px-8 py-2 w-full",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
