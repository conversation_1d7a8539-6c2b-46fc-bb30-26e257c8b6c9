import { Metadata } from 'next';
import { FAQPageContent } from './FAQPageContent';

export const metadata: Metadata = {
  title: 'Frequently Asked Questions - Charity Welcome Hub',
  description: 'Find answers to common questions about our charity, services, volunteering opportunities, and how to get involved.',
  keywords: ['FAQ', 'frequently asked questions', 'charity', 'help', 'support', 'volunteer', 'donate'],
  openGraph: {
    title: 'Frequently Asked Questions - Charity Welcome Hub',
    description: 'Find answers to common questions about our charity, services, volunteering opportunities, and how to get involved.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Frequently Asked Questions - Charity Welcome Hub',
    description: 'Find answers to common questions about our charity, services, volunteering opportunities, and how to get involved.',
  },
};

export default function FAQPage() {
  return <FAQPageContent />;
}
