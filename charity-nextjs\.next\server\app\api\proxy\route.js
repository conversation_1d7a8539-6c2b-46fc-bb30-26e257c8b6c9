(()=>{var e={};e.id=246,e.ids=[246],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96666:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>y,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>T});var s={};r.r(s),r.d(s,{DELETE:()=>l,GET:()=>p,PATCH:()=>x,POST:()=>c,PUT:()=>d});var n=r(96559),a=r(48088),o=r(37719),i=r(32190);let u=process.env.BACKEND_URL||"http://localhost:5000";async function p(e){return h(e,"GET")}async function c(e){return h(e,"POST")}async function d(e){return h(e,"PUT")}async function l(e){return h(e,"DELETE")}async function x(e){return h(e,"PATCH")}async function h(e,t){try{let r,s=new URL(e.url),n=s.pathname.replace("/api/proxy",""),a=`${u}${n}${s.search}`,o={};if(e.headers.forEach((e,t)=>{["host","connection","content-length"].includes(t.toLowerCase())||(o[t]=e)}),["POST","PUT","PATCH"].includes(t)){let t=e.headers.get("content-type");r=t?.includes("application/json")?JSON.stringify(await e.json()):t?.includes("multipart/form-data")?await e.formData():await e.text()}let p=await fetch(a,{method:t,headers:o,body:r}),c=await p.text();return new i.NextResponse(c,{status:p.status,statusText:p.statusText,headers:{"Content-Type":p.headers.get("content-type")||"application/json",...p.headers.get("set-cookie")&&{"Set-Cookie":p.headers.get("set-cookie")}}})}catch(e){return i.NextResponse.json({error:"Internal server error"},{status:500})}}let y=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/proxy/route",pathname:"/api/proxy",filename:"route",bundlePath:"app/api/proxy/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\proxy\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:T,serverHooks:g}=y;function v(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:T})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(96666));module.exports=s})();