import React from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export interface ApiErrorProps {
  title?: string;
  description?: string;
  error: Error | unknown;
  onRetry?: () => void;
  resetErrorBoundary?: () => void;
  variant?: 'alert' | 'card';
}

function getErrorMessage(error: any): string {
  if (typeof error === 'string') return error;
  if (error?.message) return error.message;
  if (error?.response?.data?.message) return error.response.data.message;
  return 'An unexpected error occurred';
}

export function ApiError({
  title = "An error occurred",
  description,
  error,
  onRetry,
  resetErrorBoundary,
  variant = 'alert'
}: ApiErrorProps) {
  const errorMessage = getErrorMessage(error);
  const displayDescription = description || errorMessage;

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
    if (resetErrorBoundary) {
      resetErrorBoundary();
    }
  };

  return (
    <Card className="border-red-200 bg-red-50">
      <CardHeader>
        <CardTitle className="text-red-800 flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2" />
          {title}
        </CardTitle>
        <CardDescription className="text-red-700">
          {displayDescription}
        </CardDescription>
      </CardHeader>
      {(onRetry || resetErrorBoundary) && (
        <CardContent>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRetry}
            className="border-red-300 text-red-700 hover:bg-red-100"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </CardContent>
      )}
    </Card>
  );
}
