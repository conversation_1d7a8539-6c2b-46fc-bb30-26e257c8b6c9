import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Refresh<PERSON><PERSON> } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export interface ApiErrorProps {
  title?: string;
  description?: string;
  error: Error | unknown;
  onRetry?: () => void;
  resetErrorBoundary?: () => void;
  variant?: 'alert' | 'card';
}

function getErrorMessage(error: unknown): string {
  if (typeof error === 'string') return error;

  // Check for Error object with message
  if (error instanceof Error) {
    return error.message;
  }

  // Check for object with message property
  if (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as { message: unknown }).message === 'string'
  ) {
    return (error as { message: string }).message;
  }

  // Check for axios-style error with response.data.message
  if (
    typeof error === 'object' &&
    error !== null &&
    'response' in error
  ) {
    const errorWithResponse = error as { response: unknown };
    if (
      typeof errorWithResponse.response === 'object' &&
      errorWithResponse.response !== null &&
      'data' in errorWithResponse.response
    ) {
      const responseData = (errorWithResponse.response as { data: unknown }).data;
      if (
        typeof responseData === 'object' &&
        responseData !== null &&
        'message' in responseData &&
        typeof (responseData as { message: unknown }).message === 'string'
      ) {
        return (responseData as { message: string }).message;
      }
    }
  }

  return 'An unexpected error occurred';
}

export function ApiError({
  title = "An error occurred",
  description,
  error,
  onRetry,
  resetErrorBoundary,
}: ApiErrorProps) {
  const errorMessage = getErrorMessage(error);
  const displayDescription = description || errorMessage;

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
    if (resetErrorBoundary) {
      resetErrorBoundary();
    }
  };

  return (
    <Card className="border-red-200 bg-red-50">
      <CardHeader>
        <CardTitle className="text-red-800 flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2" />
          {title}
        </CardTitle>
        <CardDescription className="text-red-700">
          {displayDescription}
        </CardDescription>
      </CardHeader>
      {(onRetry || resetErrorBoundary) && (
        <CardContent>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRetry}
            className="border-red-300 text-red-700 hover:bg-red-100"
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
        </CardContent>
      )}
    </Card>
  );
}
