(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[605],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>c});var n=a(5155),r=a(2115),i=a(9708),s=a(2085),l=a(9434);let c=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200",{variants:{variant:{default:"bg-charity-primary text-white hover:bg-charity-secondary",destructive:"bg-charity-destructive text-white hover:bg-charity-destructive/90",outline:"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground",secondary:"bg-charity-light text-charity-dark hover:bg-charity-light/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-charity-primary underline-offset-4 hover:underline",accent:"bg-charity-accent text-charity-dark hover:bg-charity-accent/80",success:"bg-charity-success text-white hover:bg-charity-success/90",warning:"bg-charity-warning text-white hover:bg-charity-warning/90",info:"bg-charity-info text-white hover:bg-charity-info/90",donate:"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 py-1.5 text-xs",lg:"h-11 rounded-md px-8 py-2.5",xl:"h-12 rounded-md px-10 py-3 text-base",icon:"h-10 w-10 rounded-full",wide:"h-10 px-8 py-2 w-full"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,t)=>{let{className:a,variant:r,size:s,asChild:o=!1,...d}=e,u=o?i.DX:"button";return(0,n.jsx)(u,{className:(0,l.cn)(c({variant:r,size:s,className:a})),ref:t,...d})});o.displayName="Button"},1008:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},2085:(e,t,a)=>{"use strict";a.d(t,{F:()=>s});var n=a(2596);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,s=(e,t)=>a=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:s,defaultVariants:l}=t,c=Object.keys(s).map(e=>{let t=null==a?void 0:a[e],n=null==l?void 0:l[e];if(null===t)return null;let i=r(t)||r(n);return s[e][i]}),o=a&&Object.entries(a).reduce((e,t)=>{let[a,n]=t;return void 0===n||(e[a]=n),e},{});return i(e,c,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:a,className:n,...r}=t;return Object.entries(r).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...l,...o}[t]):({...l,...o})[t]===a})?[...e,a,n]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}},2355:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},3052:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},4416:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5731:(e,t,a)=>{"use strict";a.d(t,{AY:()=>u,EO:()=>s,TP:()=>c,YV:()=>l,jE:()=>m,l4:()=>d,lM:()=>o});var n=a(3464),r=a(1008);let i=n.A.create({baseURL:r.A.baseURL,timeout:r.A.timeout,withCredentials:r.A.withCredentials});i.interceptors.request.use(async e=>{{let t=localStorage.getItem("authToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(e)});let s={getContent:async()=>(await i.get("/about")).data,updateContent:async e=>(await i.post("/about",e)).data},l={getAll:async e=>({teamMembers:(await i.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await i.get("/team/".concat(e))).data}),create:async e=>(await i.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,t)=>(await i.put("/team/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}})).data,delete:async e=>(await i.delete("/team/".concat(e))).data},c={submit:async e=>(await i.post("/contact",e)).data,getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0,n={page:e,limit:t,...a?{status:a}:{}};return(await i.get("/contact",{params:n})).data},getById:async e=>(await i.get("/contact/".concat(e))).data,updateStatus:async(e,t)=>(await i.put("/contact/".concat(e,"/status"),{status:t})).data,delete:async e=>(await i.delete("/contact/".concat(e))).data},o={getAll:async e=>({locations:(await i.get("/locations",{params:e})).data}),getById:async e=>({location:(await i.get("/locations/".concat(e))).data}),create:async e=>{let t={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await i.post("/locations",t)).data},update:async(e,t)=>{let a={...t,isMainOffice:void 0!==t.isMainOffice?String(t.isMainOffice):void 0,active:void 0!==t.active?String(t.active):void 0};return(await i.put("/locations/".concat(e),a)).data},delete:async e=>(await i.delete("/locations/".concat(e))).data},d={getAll:async()=>(await i.get("/faqs")).data,getAllAdmin:async()=>(await i.get("/admin/faqs")).data,getById:async e=>(await i.get("/admin/faqs/".concat(e))).data,create:async e=>(await i.post("/admin/faqs",e)).data,update:async function(e,t){let a,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(n)a={isActive:void 0===t.isActive||!!t.isActive};else{var r,s,l;a={question:null==(r=t.question)?void 0:r.trim(),answer:null==(s=t.answer)?void 0:s.trim(),category:(null==(l=t.category)?void 0:l.trim())||"General",order:"number"==typeof t.order?t.order:0,isActive:void 0===t.isActive||!!t.isActive}}try{return(await i.put("/admin/faqs/".concat(e),a)).data}catch(e){throw e}},delete:async e=>(await i.delete("/admin/faqs/".concat(e))).data},u={getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await i.get("/news?page=".concat(e,"&limit=").concat(t,"&includeAttachments=true"))).data},getBySlug:async e=>(await i.get("/news/".concat(e,"?includeAttachments=true"))).data.news,getById:async e=>(await i.get("/admin/news/".concat(e))).data.news,create:async e=>(await i.post("/admin/news",e)).data.news,update:async(e,t)=>(await i.put("/admin/news/".concat(e),t)).data.news,delete:async e=>(await i.delete("/admin/news/".concat(e))).data},m={getAllAlbums:async()=>(await i.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await i.get("/gallery/albums/".concat(e))).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await i.get("/admin/gallery/albums/".concat(e))).data}catch(e){throw e}},createAlbum:async e=>(await i.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,t)=>(await i.put("/admin/gallery/albums/".concat(e),t)).data.album,deleteAlbum:async e=>{await i.delete("/admin/gallery/albums/".concat(e))},uploadImage:async(e,t)=>{var a;let n=null==(a=document.querySelector('meta[name="csrf-token"]'))?void 0:a.getAttribute("content");return n&&t.append("_csrf",n),(await i.post("/admin/gallery/albums/".concat(e,"/images"),t,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await i.delete("/admin/gallery/images/".concat(e))}}},6101:(e,t,a)=>{"use strict";a.d(t,{s:()=>s,t:()=>i});var n=a(2115);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let a=!1,n=e.map(e=>{let n=r(e,t);return a||"function"!=typeof n||(a=!0),n});if(a)return()=>{for(let t=0;t<n.length;t++){let a=n[t];"function"==typeof a?a():r(e[t],null)}}}}function s(...e){return n.useCallback(i(...e),e)}},6669:(e,t,a)=>{"use strict";a.d(t,{GalleryDetailPageContent:()=>x});var n=a(5155),r=a(2115),i=a(6874),s=a.n(i),l=a(6766),c=a(2960),o=a(9946);let d=(0,o.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),u=(0,o.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var m=a(4416),h=a(2355),g=a(3052),y=a(285),p=a(5731),v=a(9434),f=a(1008);function x(e){let{slug:t}=e,[a,i]=(0,r.useState)(null),[o,x]=(0,r.useState)(-1),[w,b]=(0,r.useState)({}),[j,A]=(0,r.useState)({}),{data:k,isLoading:N,isError:C}=(0,c.I)({queryKey:["albumDetail",t],queryFn:async()=>t?await p.jE.getAlbumBySlug(t):Promise.reject("No slug provided"),enabled:!!t}),_=(0,r.useCallback)((e,t)=>{let{naturalHeight:a,naturalWidth:n}=t.currentTarget;A(t=>({...t,[e]:{height:a,width:n}})),b(t=>({...t,[e]:!0}))},[]),S=(0,r.useCallback)((e,t)=>{i(e),x(t)},[]),E=(0,r.useCallback)(()=>{i(null),x(-1)},[]),O=(0,r.useCallback)(e=>{if(!(null==k?void 0:k.images))return;let t=k.images.length,a=o;x(a="prev"===e?o>0?o-1:t-1:o<t-1?o+1:0),i("".concat(f.A.backendURL,"/uploads/gallery/").concat(k.album._id,"/").concat(k.images[a].filename))},[o,k]);if((0,r.useEffect)(()=>{let e=e=>{a&&("Escape"===e.key?E():"ArrowLeft"===e.key?O("prev"):"ArrowRight"===e.key&&O("next"))};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[a,E,O]),N)return(0,n.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,n.jsxs)("div",{className:"animate-pulse",children:[(0,n.jsx)("div",{className:"h-8 bg-gray-200 rounded w-32 mb-4"}),(0,n.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-3"}),(0,n.jsx)("div",{className:"h-6 bg-gray-100 rounded w-96 mb-8"}),(0,n.jsx)("div",{className:"masonry-grid",children:[...Array(6)].map((e,t)=>(0,n.jsx)("div",{className:"masonry-item",children:(0,n.jsx)("div",{className:"bg-gray-200 rounded-lg",style:{height:"".concat(200+200*Math.random(),"px")}})},t))})]})});if(C||!k)return(0,n.jsxs)("div",{className:"container mx-auto px-4 py-12 text-center",children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Album Not Found"}),(0,n.jsx)("p",{className:"text-gray-600 mb-6",children:"The album you're looking for does not exist or has been removed."}),(0,n.jsx)(y.$,{asChild:!0,children:(0,n.jsxs)(s(),{href:"/gallery",children:[(0,n.jsx)(d,{className:"mr-2 h-4 w-4"})," Back to Gallery"]})})]});let{album:B,images:I}=k;return void 0!==B.imageCount?B.imageCount:I.length,(0,n.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,n.jsx)(y.$,{asChild:!0,variant:"ghost",className:"mb-4",children:(0,n.jsxs)(s(),{href:"/gallery",children:[(0,n.jsx)(d,{className:"mr-2 h-4 w-4"})," Back to Gallery"]})}),(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-3",children:B.title}),B.description&&(0,n.jsx)("p",{className:"text-gray-600 mb-8 max-w-3xl",children:B.description}),0===I.length?(0,n.jsx)("div",{className:"bg-gray-50 rounded-lg p-12 text-center",children:(0,n.jsx)("p",{className:"text-gray-600 text-lg",children:"This album has no images yet."})}):(0,n.jsx)("div",{className:"masonry-grid",children:I.map((e,t)=>{let a="".concat(f.A.backendURL,"/uploads/gallery/").concat(B._id,"/").concat(e.filename),r=w[e._id];return(0,n.jsxs)("div",{className:(0,v.cn)("masonry-item group relative overflow-hidden cursor-pointer",r&&"loaded"),children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity z-10 flex items-center justify-center",children:(0,n.jsx)(y.$,{variant:"ghost",size:"icon",className:"text-white border border-white/30 hover:bg-white/20",onClick:e=>{e.stopPropagation(),S(a,t)},children:(0,n.jsx)(u,{className:"h-5 w-5"})})}),(0,n.jsx)(l.default,{src:a,alt:"Gallery image ".concat(e._id),width:400,height:300,className:"w-full h-auto object-cover transition-transform duration-500 group-hover:scale-110",sizes:"(max-width: 480px) 100vw, (max-width: 768px) 50vw, 33vw",onLoad:t=>{_(e._id,t)},onError:t=>{t.currentTarget.src="/placeholder.svg",A(t=>({...t,[e._id]:{height:200,width:200}})),b(t=>({...t,[e._id]:!0}))},onClick:()=>S(a,t)})]},e._id)})}),a&&(0,n.jsxs)("div",{className:"fixed inset-0 bg-black/98 z-50 flex items-center justify-center p-4 backdrop-blur-sm",onClick:E,children:[(0,n.jsx)("div",{className:"absolute top-4 right-4 flex space-x-2",children:(0,n.jsx)(y.$,{variant:"ghost",size:"icon",className:"text-white hover:bg-white/10 rounded-full",onClick:E,children:(0,n.jsx)(m.A,{className:"h-6 w-6"})})}),(0,n.jsx)(y.$,{variant:"ghost",size:"icon",className:"absolute left-4 md:left-8 text-white hover:bg-white/10 h-12 w-12 rounded-full opacity-70 hover:opacity-100",onClick:e=>{e.stopPropagation(),O("prev")},children:(0,n.jsx)(h.A,{className:"h-6 w-6"})}),(0,n.jsx)(y.$,{variant:"ghost",size:"icon",className:"absolute right-4 md:right-8 text-white hover:bg-white/10 h-12 w-12 rounded-full opacity-70 hover:opacity-100",onClick:e=>{e.stopPropagation(),O("next")},children:(0,n.jsx)(g.A,{className:"h-6 w-6"})}),(0,n.jsxs)("div",{className:"relative max-h-[90vh] max-w-[90vw] animate-fadeIn",children:[(0,n.jsx)("img",{src:a,alt:"Enlarged gallery image",className:"max-h-[90vh] max-w-[90vw] object-contain rounded-lg shadow-2xl",onClick:e=>e.stopPropagation()}),(0,n.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 text-center text-white text-sm py-3 bg-gradient-to-t from-black/70 to-transparent rounded-b-lg",children:[o+1," / ",I.length]})]})]})]})}},8905:(e,t,a)=>{Promise.resolve().then(a.bind(a,6669))},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i,oH:()=>s,vV:()=>l});var n=a(2596),r=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,n.$)(t))}function s(e){return e.replace(/\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g,(e,t)=>{let a=t.split(",").map(e=>e.trim()).filter(Boolean);if(0===a.length)return e;let n=a.map(e=>'<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">'.concat(e,"</span>")).join("");return'<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">'.concat(n,"</div></div>")})}let l=(e,t)=>{}},9708:(e,t,a)=>{"use strict";a.d(t,{DX:()=>l,TL:()=>s});var n=a(2115),r=a(6101),i=a(5155);function s(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:a,...i}=e;if(n.isValidElement(a)){var s;let e,l,c=(s=a,(l=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(l=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),o=function(e,t){let a={...t};for(let n in t){let r=e[n],i=t[n];/^on[A-Z]/.test(n)?r&&i?a[n]=(...e)=>{let t=i(...e);return r(...e),t}:r&&(a[n]=r):"style"===n?a[n]={...r,...i}:"className"===n&&(a[n]=[r,i].filter(Boolean).join(" "))}return{...e,...a}}(i,a.props);return a.type!==n.Fragment&&(o.ref=t?(0,r.t)(t,c):c),n.cloneElement(a,o)}return n.Children.count(a)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=n.forwardRef((e,a)=>{let{children:r,...s}=e,l=n.Children.toArray(r),c=l.find(o);if(c){let e=c.props.children,r=l.map(t=>t!==c?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...s,ref:a,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,i.jsx)(t,{...s,ref:a,children:r})});return a.displayName=`${e}.Slot`,a}var l=s("Slot"),c=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}}},e=>{var t=t=>e(e.s=t);e.O(0,[598,967,951,874,766,441,684,358],()=>t(8905)),_N_E=e.O()}]);