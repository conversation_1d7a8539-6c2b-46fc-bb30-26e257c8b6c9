'use client';

import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Loader } from 'lucide-react';
import Image from 'next/image';
import API_CONFIG from '@/config/api';

interface Partner {
  _id: string;
  name: string;
  logo: string;
  website?: string;
}

interface PartnersSectionProps {
  className?: string;
}

const PartnersSection: React.FC<PartnersSectionProps> = ({ className }) => {
  const { data, isLoading, error } = useQuery({
    queryKey: ['partners'],
    queryFn: async () => {
      const response = await fetch(`${API_CONFIG.backendURL}/api/partners?active=true&featured=true`);
      if (!response.ok) {
        throw new Error('Failed to fetch partners');
      }
      return response.json();
    },
  });

  if (isLoading) {
    return (
      <div className={`flex justify-center items-center py-8 ${className}`}>
        <Loader className="h-8 w-8 animate-spin text-teal-600" />
      </div>
    );
  }

  if (error || !data?.partners || data.partners.length === 0) {
    return null; // Don't show the section if there are no partners
  }

  return (
    <section className={`py-16 bg-white ${className}`}>
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold text-center mb-12 text-teal-800">Our Partners & Sponsors</h2>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8 items-center justify-items-center">
          {data.partners.map((partner: Partner) => (
            <div key={partner._id} className="flex flex-col items-center">
              <a
                href={partner.website || '#'}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center h-32 w-full relative"
              >
                <Image
                  src={`${API_CONFIG.backendURL}${partner.logo}`}
                  alt={`${partner.name} logo`}
                  fill
                  className="object-contain"
                  onError={() => {
                    // Handle error silently for Next.js Image component
                  }}
                />
              </a>
              <p className="mt-2 text-sm font-medium text-gray-700 text-center">{partner.name}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PartnersSection;
