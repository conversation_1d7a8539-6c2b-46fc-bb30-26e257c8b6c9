"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dc1d03ae9e9a\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFzaWJcXE9uZURyaXZlXFxEZXNrdG9wXFxjaGFyaXR5X2luZm9cXGNoYXJpdHlfaW5mb1xcY2hhcml0eS1uZXh0anNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkYzFkMDNhZTllOWFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/performance/WebVitals.tsx":
/*!**************************************************!*\
  !*** ./src/components/performance/WebVitals.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageLoadMetrics: () => (/* binding */ PageLoadMetrics),\n/* harmony export */   WebVitals: () => (/* binding */ WebVitals),\n/* harmony export */   usePerformanceMetric: () => (/* binding */ usePerformanceMetric)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var web_vitals__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! web-vitals */ \"(app-pages-browser)/./node_modules/web-vitals/dist/web-vitals.js\");\n/* __next_internal_client_entry_do_not_use__ WebVitals,usePerformanceMetric,PageLoadMetrics auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\nfunction sendToAnalytics(metric) {\n    // In production, you would send this to your analytics service\n    // For now, we'll just log to console in development\n    if (true) {\n        console.log('Web Vital:', metric);\n    }\n// Example: Send to Google Analytics\n// gtag('event', metric.name, {\n//   event_category: 'Web Vitals',\n//   event_label: metric.id,\n//   value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),\n//   non_interaction: true,\n// });\n// Example: Send to custom analytics endpoint\n// fetch('/api/analytics/web-vitals', {\n//   method: 'POST',\n//   headers: { 'Content-Type': 'application/json' },\n//   body: JSON.stringify(metric),\n// });\n}\nfunction WebVitals() {\n    _s();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"WebVitals.useEffect\": ()=>{\n            // Measure Core Web Vitals\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onCLS)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onINP)(sendToAnalytics); // INP replaced FID\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onFCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onLCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onTTFB)(sendToAnalytics);\n        }\n    }[\"WebVitals.useEffect\"], []);\n    return null; // This component doesn't render anything\n}\n_s(WebVitals, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = WebVitals;\n// Hook for measuring custom metrics\nfunction usePerformanceMetric(name, value) {\n    _s1();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceMetric.useEffect\": ()=>{\n            const metric = {\n                name,\n                value,\n                id: \"\".concat(name, \"-\").concat(Date.now()),\n                delta: value,\n                entries: []\n            };\n            sendToAnalytics(metric);\n        }\n    }[\"usePerformanceMetric.useEffect\"], [\n        name,\n        value\n    ]);\n}\n_s1(usePerformanceMetric, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n// Component for measuring page load performance\nfunction PageLoadMetrics(param) {\n    let { pageName } = param;\n    _s2();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"PageLoadMetrics.useEffect\": ()=>{\n            const startTime = performance.now();\n            return ({\n                \"PageLoadMetrics.useEffect\": ()=>{\n                    const endTime = performance.now();\n                    const loadTime = endTime - startTime;\n                    const metric = {\n                        name: 'page_load_time',\n                        value: loadTime,\n                        id: \"\".concat(pageName, \"-\").concat(Date.now()),\n                        delta: loadTime,\n                        entries: [],\n                        page: pageName\n                    };\n                    sendToAnalytics(metric);\n                }\n            })[\"PageLoadMetrics.useEffect\"];\n        }\n    }[\"PageLoadMetrics.useEffect\"], [\n        pageName\n    ]);\n    return null;\n}\n_s2(PageLoadMetrics, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c1 = PageLoadMetrics;\nvar _c, _c1;\n$RefreshReg$(_c, \"WebVitals\");\n$RefreshReg$(_c1, \"PageLoadMetrics\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/performance/WebVitals.tsx\n"));

/***/ })

});