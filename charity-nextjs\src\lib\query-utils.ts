import { QueryClient, dehydrate } from '@tanstack/react-query';
import { cache } from 'react';

// Create a cache function for the query client
export const getQueryClient = cache(() => new QueryClient({
  defaultOptions: {
    queries: {
      // With SSR, we usually want to set some default staleTime
      // above 0 to avoid refetching immediately on the client
      staleTime: 60 * 1000, // 1 minute
      gcTime: 10 * 60 * 1000, // 10 minutes
    },
  },
}));

// Utility function to prefetch data on the server
export async function prefetchQuery<T>(
  queryKey: string[],
  queryFn: () => Promise<T>,
  options?: {
    staleTime?: number;
    gcTime?: number;
  }
) {
  const queryClient = getQueryClient();
  
  await queryClient.prefetchQuery({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? 60 * 1000,
    gcTime: options?.gcTime ?? 10 * 60 * 1000,
  });

  return dehydrate(queryClient);
}

// Utility function to prefetch multiple queries
export async function prefetchQueries(
  queries: Array<{
    queryKey: string[];
    queryFn: () => Promise<unknown>;
    staleTime?: number;
    gcTime?: number;
  }>
) {
  const queryClient = getQueryClient();
  
  await Promise.all(
    queries.map(({ queryKey, queryFn, staleTime, gcTime }) =>
      queryClient.prefetchQuery({
        queryKey,
        queryFn,
        staleTime: staleTime ?? 60 * 1000,
        gcTime: gcTime ?? 10 * 60 * 1000,
      })
    )
  );

  return dehydrate(queryClient);
}

// Type for server-side props with dehydrated state
export interface ServerSideProps {
  dehydratedState?: unknown;
}

// Utility to create server-side props with prefetched data
export function createServerSideProps(dehydratedState: unknown): ServerSideProps {
  return {
    dehydratedState,
  };
}

// Query keys factory for consistent key management
export const queryKeys = {
  // News queries
  news: {
    all: ['news'] as const,
    lists: () => [...queryKeys.news.all, 'list'] as const,
    list: (page: number, limit: number) => [...queryKeys.news.lists(), { page, limit }] as const,
    details: () => [...queryKeys.news.all, 'detail'] as const,
    detail: (slug: string) => [...queryKeys.news.details(), slug] as const,
  },
  
  // Gallery queries
  gallery: {
    all: ['gallery'] as const,
    albums: () => [...queryKeys.gallery.all, 'albums'] as const,
    album: (slug: string) => [...queryKeys.gallery.albums(), slug] as const,
  },
  
  // Search queries
  search: {
    all: ['search'] as const,
    results: (query: string) => [...queryKeys.search.all, query] as const,
  },
  
  // Admin queries
  admin: {
    all: ['admin'] as const,
    analytics: () => [...queryKeys.admin.all, 'analytics'] as const,
    visitStats: () => [...queryKeys.admin.analytics(), 'visits'] as const,
    onlineCount: () => [...queryKeys.admin.analytics(), 'online'] as const,
    users: () => [...queryKeys.admin.all, 'users'] as const,
    contacts: () => [...queryKeys.admin.all, 'contacts'] as const,
    subscribers: () => [...queryKeys.admin.all, 'subscribers'] as const,
  },
  
  // FAQ queries
  faq: {
    all: ['faq'] as const,
    list: () => [...queryKeys.faq.all, 'list'] as const,
  },
  
  // Team queries
  team: {
    all: ['team'] as const,
    members: () => [...queryKeys.team.all, 'members'] as const,
  },
} as const;
