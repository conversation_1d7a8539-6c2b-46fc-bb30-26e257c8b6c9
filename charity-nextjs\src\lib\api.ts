import axios from 'axios';
import API_CONFIG from '../config/api';

// Create axios instance with config
const api = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  withCredentials: API_CONFIG.withCredentials,
});

// Add request interceptor for authentication
api.interceptors.request.use(
  async (config) => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('authToken');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Interfaces
export interface TeamMemberItem {
  _id: string;
  name: string;
  position: string;
  bio: string;
  photo?: string;
  email?: string;
  phone?: string;
  socialLinks?: {
    linkedin?: string;
    twitter?: string;
    facebook?: string;
  };
  displayOrder: number;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AboutContent {
  _id?: string;
  mission: string;
  vision: string;
  foundedYear: string;
  volunteersCount: string;
  peopleHelpedCount: string;
  communitiesCount: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface LocationItem {
  _id: string;
  name: string;
  description: string;
  latitude: number;
  longitude: number;
  address?: string;
  phone?: string;
  email?: string;
  isMainOffice: boolean;
  displayOrder: number;
  active: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LocationsResponse {
  locations: LocationItem[];
}

export interface ContactSubmission {
  name: string;
  email: string;
  subject: string;
  message: string;
}

export interface ContactMessage extends ContactSubmission {
  _id: string;
  status: 'new' | 'read' | 'replied' | 'archived';
  createdAt: string;
  updatedAt: string;
}

export interface ContactResponse {
  contacts: ContactMessage[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface FaqItem {
  _id: string;
  question: string;
  answer: string;
  category: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface FaqResponse {
  faqs: FaqItem[];
}

export interface NewsItem {
  _id: string;
  title: string;
  body: string;
  slug: string;
  publishDate: string;
  expiryDate?: string;
  published: boolean;
  author?: string | { _id: string; username: string };
  images?: string[];
  attachments?: Array<{ _id: string; filename: string; mimeType: string; size: number }>;
}

export interface NewsResponse {
  news: NewsItem[];
  pagination: {
    total: number;
    page: number;
    pages: number;
  };
}

export interface AlbumItem {
  _id: string;
  title: string;
  slug: string;
  description?: string;
  imageCount?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface AlbumResponse {
  albums: AlbumItem[];
}

export interface ImageItem {
  _id: string;
  filename: string;
  caption?: string;
  albumId: string;
}

// About API
export const aboutApi = {
  getContent: async (): Promise<{ aboutContent: AboutContent }> => {
    const response = await api.get('/about');
    return response.data;
  },

  updateContent: async (aboutData: Partial<AboutContent>): Promise<{ message: string, aboutContent: AboutContent }> => {
    const response = await api.post('/about', aboutData);
    return response.data;
  }
};

// Team API
export const teamApi = {
  getAll: async (params?: { active?: boolean, includeInactive?: boolean }): Promise<{ teamMembers: TeamMemberItem[] }> => {
    const response = await api.get('/team', { params });
    return { teamMembers: response.data };
  },

  getById: async (id: string): Promise<{ teamMember: TeamMemberItem }> => {
    const response = await api.get(`/team/${id}`);
    return { teamMember: response.data };
  },

  create: async (formData: FormData): Promise<{ teamMember: TeamMemberItem, message: string }> => {
    const response = await api.post('/team', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  },

  update: async (id: string, formData: FormData): Promise<{ teamMember: TeamMemberItem, message: string }> => {
    const response = await api.put(`/team/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    return response.data;
  },

  delete: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete(`/team/${id}`);
    return response.data;
  }
};

// Contact API
export const contactApi = {
  submit: async (contactData: ContactSubmission): Promise<{ message: string }> => {
    const response = await api.post('/contact', contactData);
    return response.data;
  },

  getAll: async (page = 1, limit = 10, status?: string): Promise<ContactResponse> => {
    const params = { page, limit, ...(status ? { status } : {}) };
    const response = await api.get('/contact', { params });
    return response.data;
  },

  getById: async (id: string): Promise<{ contact: ContactMessage }> => {
    const response = await api.get(`/contact/${id}`);
    return response.data;
  },

  updateStatus: async (id: string, status: 'new' | 'read' | 'replied' | 'archived'): Promise<{ message: string, contact: ContactMessage }> => {
    const response = await api.put(`/contact/${id}/status`, { status });
    return response.data;
  },

  delete: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete(`/contact/${id}`);
    return response.data;
  }
};

// Locations API
export const locationsApi = {
  getAll: async (params?: { active?: boolean, includeInactive?: boolean }): Promise<LocationsResponse> => {
    const response = await api.get('/locations', { params });
    return { locations: response.data };
  },

  getById: async (id: string): Promise<{ location: LocationItem }> => {
    const response = await api.get(`/locations/${id}`);
    return { location: response.data };
  },

  create: async (locationData: Partial<LocationItem>): Promise<{ location: LocationItem, message: string }> => {
    const formattedData = {
      ...locationData,
      isMainOffice: locationData.isMainOffice !== undefined ? String(locationData.isMainOffice) : undefined,
      active: locationData.active !== undefined ? String(locationData.active) : undefined
    };

    const response = await api.post('/locations', formattedData);
    return response.data;
  },

  update: async (id: string, locationData: Partial<LocationItem>): Promise<{ location: LocationItem, message: string }> => {
    const formattedData = {
      ...locationData,
      isMainOffice: locationData.isMainOffice !== undefined ? String(locationData.isMainOffice) : undefined,
      active: locationData.active !== undefined ? String(locationData.active) : undefined
    };

    const response = await api.put(`/locations/${id}`, formattedData);
    return response.data;
  },

  delete: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete(`/locations/${id}`);
    return response.data;
  }
};

// FAQ API
export const faqApi = {
  getAll: async (): Promise<FaqResponse> => {
    const response = await api.get('/faqs');
    return response.data;
  },

  getAllAdmin: async (): Promise<FaqResponse> => {
    const response = await api.get('/admin/faqs');
    return response.data;
  },

  getById: async (id: string): Promise<{ faq: FaqItem }> => {
    const response = await api.get(`/admin/faqs/${id}`);
    return response.data;
  },

  create: async (faqData: Partial<FaqItem>): Promise<{ faq: FaqItem, message: string }> => {
    const response = await api.post('/admin/faqs', faqData);
    return response.data;
  },

  update: async (id: string, faqData: Partial<FaqItem>, isPartialUpdate = false): Promise<{ faq: FaqItem, message: string }> => {
    let sanitizedData: Partial<FaqItem>;

    if (isPartialUpdate) {
      // For partial updates, only include the isActive field
      sanitizedData = {
        isActive: faqData.isActive === undefined ? true : Boolean(faqData.isActive)
      };
    } else {
      // For full updates, include all fields with proper formatting
      sanitizedData = {
        question: faqData.question?.trim(),
        answer: faqData.answer?.trim(),
        category: faqData.category?.trim() || 'General',
        order: typeof faqData.order === 'number' ? faqData.order : 0,
        isActive: faqData.isActive === undefined ? true : Boolean(faqData.isActive)
      };
    }

    try {
      const response = await api.put(`/admin/faqs/${id}`, sanitizedData);
      return response.data;
    } catch (error) {
      console.error('FAQ update error:', error);
      throw error;
    }
  },

  delete: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete(`/admin/faqs/${id}`);
    return response.data;
  }
};

// News API
export const newsApi = {
  getAll: async (page = 1, limit = 10): Promise<NewsResponse> => {
    const response = await api.get(`/news?page=${page}&limit=${limit}&includeAttachments=true`);
    return response.data;
  },

  getBySlug: async (slug: string): Promise<NewsItem> => {
    const response = await api.get(`/news/${slug}?includeAttachments=true`);
    return response.data.news;
  },

  getById: async (id: string): Promise<NewsItem> => {
    const response = await api.get(`/admin/news/${id}`);
    return response.data.news;
  },

  create: async (newsData: Partial<NewsItem>): Promise<NewsItem> => {
    const response = await api.post('/admin/news', newsData);
    return response.data.news;
  },

  update: async (id: string, newsData: Partial<NewsItem>): Promise<NewsItem> => {
    const response = await api.put(`/admin/news/${id}`, newsData);
    return response.data.news;
  },

  delete: async (id: string): Promise<{ message: string }> => {
    const response = await api.delete(`/admin/news/${id}`);
    return response.data;
  }
};

// Gallery API
export const galleryApi = {
  getAllAlbums: async (): Promise<AlbumResponse> => {
    const response = await api.get('/gallery/albums');
    return response.data;
  },

  getAlbumBySlug: async (slug: string): Promise<{ album: AlbumItem, images: ImageItem[] }> => {
    try {
      const response = await api.get(`/gallery/albums/${slug}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching album by slug:', error);
      throw error;
    }
  },

  getAlbumById: async (id: string): Promise<{ album: AlbumItem, images: ImageItem[] }> => {
    try {
      const response = await api.get(`/admin/gallery/albums/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching album by ID:', error);
      throw error;
    }
  },

  createAlbum: async (albumData: Partial<AlbumItem>): Promise<AlbumItem> => {
    const response = await api.post('/admin/gallery/albums', albumData);
    return response.data.album;
  },

  updateAlbum: async (id: string, albumData: Partial<AlbumItem>): Promise<AlbumItem> => {
    const response = await api.put(`/admin/gallery/albums/${id}`, albumData);
    return response.data.album;
  },

  deleteAlbum: async (id: string): Promise<void> => {
    await api.delete(`/admin/gallery/albums/${id}`);
  },

  uploadImage: async (albumId: string, formData: FormData): Promise<{ message: string; images: Array<{ _id: string; filename: string }> }> => {
    // Ensure CSRF token is included
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      formData.append('_csrf', csrfToken);
    }

    const response = await api.post(`/admin/gallery/albums/${albumId}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  deleteImage: async (imageId: string): Promise<void> => {
    await api.delete(`/admin/gallery/images/${imageId}`);
  }
};

export default api;
