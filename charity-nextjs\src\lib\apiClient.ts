import API_CONFIG from '@/config/api';

// Track if we're currently fetching a CSRF token
let isFetchingCsrf = false;
// Store the CSRF token
let csrfToken: string | null = null;
// Create a promise to track pending CSRF requests
let csrfPromise: Promise<string> | null = null;

/**
 * Base API client with common functionality for Next.js
 */
class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor() {
    this.baseURL = API_CONFIG.baseURL;
    this.timeout = API_CONFIG.timeout;
  }

  /**
   * Get CSRF token for secure requests
   */
  private async getCsrfToken(): Promise<string> {
    // Only run on client side
    if (typeof window === 'undefined') {
      return '';
    }

    // If we already have a token, return it
    if (csrfToken) {
      return csrfToken;
    }

    // If we're already fetching a token, wait for that request to complete
    if (isFetchingCsrf && csrfPromise) {
      return csrfPromise;
    }

    // Start a new token fetch
    isFetchingCsrf = true;
    csrfPromise = fetch(`${this.baseURL}/api/csrf-token`, { 
      credentials: 'include' 
    })
      .then(response => response.json())
      .then(data => {
        csrfToken = typeof data.csrfToken === 'string' ? data.csrfToken : '';
        return csrfToken ?? '';
      })
      .finally(() => {
        isFetchingCsrf = false;
      });

    return csrfPromise;
  }

  /**
   * Create request headers
   */
  private async createHeaders(options: RequestInit = {}): Promise<HeadersInit> {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add auth token if available (client-side only)
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('authToken');
      if (token) {
        (headers as Record<string, string>)['Authorization'] = `Bearer ${token}`;
      }
    }

    return headers;
  }

  /**
   * Create request headers with CSRF token for admin routes
   */
  private async createAdminHeaders(url: string, method: string, options: RequestInit = {}): Promise<HeadersInit> {
    const headers = await this.createHeaders(options);

    // Add CSRF token for non-GET admin routes
    if (
      url.includes('/admin/') &&
      ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method.toUpperCase())
    ) {
      try {
        const token = await this.getCsrfToken();
        if (token) {
          (headers as Record<string, string>)['CSRF-Token'] = token;
        }
      } catch (error) {
        console.error('Failed to fetch CSRF token:', error);
      }
    }

    return headers;
  }

  /**
   * Handle fetch response
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const error = new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
      (error as Error & { status?: number; response?: { data: unknown } }).status = response.status;
      (error as Error & { status?: number; response?: { data: unknown } }).response = { data: errorData };
      throw error;
    }

    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return response.json();
    }

    return response.text() as T;
  }

  /**
   * Make a request with retry logic for CSRF token issues
   */
  private async makeRequest<T>(
    url: string,
    options: RequestInit,
    retryCount = 0
  ): Promise<T> {
    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`;
    
    try {
      const response = await fetch(fullUrl, {
        ...options,
        credentials: 'include',
      });

      // Handle CSRF token errors
      if (response.status === 403 && retryCount === 0) {
        const errorData = await response.json().catch(() => ({}));
        if (errorData.message && errorData.message.includes('csrf')) {
          csrfToken = null; // Invalidate token
          
          // Retry with new CSRF token
          const newHeaders = await this.createAdminHeaders(url, options.method || 'GET', options);
          return this.makeRequest<T>(url, {
            ...options,
            headers: newHeaders,
          }, retryCount + 1);
        }
      }

      return this.handleResponse<T>(response);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Make a GET request
   */
  public async get<T>(url: string, options: RequestInit = {}): Promise<T> {
    const headers = await this.createHeaders(options);
    
    return this.makeRequest<T>(url, {
      ...options,
      method: 'GET',
      headers,
    });
  }

  /**
   * Make a POST request
   */
  public async post<T>(url: string, data?: unknown, options: RequestInit = {}): Promise<T> {
    const headers = await this.createAdminHeaders(url, 'POST', options);
    
    return this.makeRequest<T>(url, {
      ...options,
      method: 'POST',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a PUT request
   */
  public async put<T>(url: string, data?: unknown, options: RequestInit = {}): Promise<T> {
    const headers = await this.createAdminHeaders(url, 'PUT', options);
    
    return this.makeRequest<T>(url, {
      ...options,
      method: 'PUT',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Make a DELETE request
   */
  public async delete<T>(url: string, options: RequestInit = {}): Promise<T> {
    const headers = await this.createAdminHeaders(url, 'DELETE', options);
    
    return this.makeRequest<T>(url, {
      ...options,
      method: 'DELETE',
      headers,
    });
  }

  /**
   * Make a PATCH request
   */
  public async patch<T>(url: string, data?: unknown, options: RequestInit = {}): Promise<T> {
    const headers = await this.createAdminHeaders(url, 'PATCH', options);
    
    return this.makeRequest<T>(url, {
      ...options,
      method: 'PATCH',
      headers,
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  /**
   * Upload files using FormData
   */
  public async upload<T>(url: string, formData: FormData, options: RequestInit = {}): Promise<T> {
    // Don't set Content-Type for FormData, let the browser set it
    const headers: HeadersInit = {};

    // Add auth token if available (client-side only)
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('authToken');
      if (token) {
        (headers as Record<string, string>)['Authorization'] = `Bearer ${token}`;
      }
    }

    // Add CSRF token for admin routes
    if (url.includes('/admin/')) {
      try {
        const token = await this.getCsrfToken();
        if (token) {
          (headers as Record<string, string>)['CSRF-Token'] = token;
        }
      } catch (error) {
        console.error('Failed to fetch CSRF token:', error);
      }
    }

    return this.makeRequest<T>(url, {
      ...options,
      method: 'POST',
      headers: {
        ...headers,
        ...options.headers,
      },
      body: formData,
    });
  }
}

// Create and export a singleton instance
const apiClient = new ApiClient();
export default apiClient;
