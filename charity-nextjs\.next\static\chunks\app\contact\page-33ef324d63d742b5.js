(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>c});var s=a(5155),r=a(2115),i=a(9708),n=a(2085),l=a(9434);let c=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200",{variants:{variant:{default:"bg-charity-primary text-white hover:bg-charity-secondary",destructive:"bg-charity-destructive text-white hover:bg-charity-destructive/90",outline:"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground",secondary:"bg-charity-light text-charity-dark hover:bg-charity-light/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-charity-primary underline-offset-4 hover:underline",accent:"bg-charity-accent text-charity-dark hover:bg-charity-accent/80",success:"bg-charity-success text-white hover:bg-charity-success/90",warning:"bg-charity-warning text-white hover:bg-charity-warning/90",info:"bg-charity-info text-white hover:bg-charity-info/90",donate:"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 py-1.5 text-xs",lg:"h-11 rounded-md px-8 py-2.5",xl:"h-12 rounded-md px-10 py-3 text-base",icon:"h-10 w-10 rounded-full",wide:"h-10 px-8 py-2 w-full"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,t)=>{let{className:a,variant:r,size:n,asChild:o=!1,...d}=e,m=o?i.DX:"button";return(0,s.jsx)(m,{className:(0,l.cn)(c({variant:r,size:n,className:a})),ref:t,...d})});o.displayName="Button"},1008:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(5155),r=a(2115),i=a(9434);let n=r.forwardRef((e,t)=>{let{className:a,type:r,...n}=e;return(0,s.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border-[1px] border-input bg-background px-3 py-2 text-base text-foreground ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground/60 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-charity-primary focus-visible:border-charity-primary focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200 md:text-sm shadow-none",a),ref:t,...n})});n.displayName="Input"},3704:(e,t,a)=>{"use strict";a.d(t,{ContactPageContent:()=>T});var s=a(5155),r=a(2115),i=a(2177),n=a(221),l=a(8309),c=a(6695),o=a(285),d=a(9708),m=a(9434),u=a(968);let h=(0,a(2085).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),f=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)(u.b,{ref:t,className:(0,m.cn)(h(),a),...r})});f.displayName=u.b.displayName;let p=i.Op,x=r.createContext({}),g=e=>{let{...t}=e;return(0,s.jsx)(x.Provider,{value:{name:t.name},children:(0,s.jsx)(i.xI,{...t})})},y=()=>{let e=r.useContext(x),t=r.useContext(b),{getFieldState:a,formState:s}=(0,i.xW)(),n=a(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...n}},b=r.createContext({}),v=r.forwardRef((e,t)=>{let{className:a,...i}=e,n=r.useId();return(0,s.jsx)(b.Provider,{value:{id:n},children:(0,s.jsx)("div",{ref:t,className:(0,m.cn)("space-y-2",a),...i})})});v.displayName="FormItem";let w=r.forwardRef((e,t)=>{let{className:a,...r}=e,{error:i,formItemId:n}=y();return(0,s.jsx)(f,{ref:t,className:(0,m.cn)(i&&"text-destructive",a),htmlFor:n,...r})});w.displayName="FormLabel";let j=r.forwardRef((e,t)=>{let{...a}=e,{error:r,formItemId:i,formDescriptionId:n,formMessageId:l}=y();return(0,s.jsx)(d.DX,{ref:t,id:i,"aria-describedby":r?"".concat(n," ").concat(l):"".concat(n),"aria-invalid":!!r,...a})});j.displayName="FormControl",r.forwardRef((e,t)=>{let{className:a,...r}=e,{formDescriptionId:i}=y();return(0,s.jsx)("p",{ref:t,id:i,className:(0,m.cn)("text-sm text-muted-foreground",a),...r})}).displayName="FormDescription";let N=r.forwardRef((e,t)=>{let{className:a,children:r,...i}=e,{error:n,formMessageId:l}=y(),c=n?String(null==n?void 0:n.message):r;return c?(0,s.jsx)("p",{ref:t,id:l,className:(0,m.cn)("text-sm font-medium text-destructive",a),...i,children:c}):null});N.displayName="FormMessage";var A=a(2523);let C=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("textarea",{className:(0,m.cn)("flex min-h-[80px] w-full rounded-md border-[1px] border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-charity-primary focus-visible:border-charity-primary focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 shadow-none",a),ref:t,...r})});C.displayName="Textarea";var M=a(5731),S=a(4631),k=a(4516),F=a(9420),R=a(1264),I=a(2960);let B=l.Ik({name:l.Yj().min(2,{message:"Name must be at least 2 characters."}),email:l.Yj().email({message:"Please enter a valid email address."}),subject:l.Yj().min(5,{message:"Subject must be at least 5 characters."}),message:l.Yj().min(10,{message:"Message must be at least 10 characters."})});function T(){let{toast:e}={toast:e=>{let{title:t,description:a}=e;alert("".concat(t,": ").concat(a))}},[t,a]=r.useState(!1),l="Charity Welcome Hub",{data:d,isLoading:m}=(0,I.I)({queryKey:["locations"],queryFn:()=>M.lM.getAll({active:!0})}),u=r.useMemo(()=>(null==d?void 0:d.locations)?d.locations.find(e=>e.isMainOffice)||d.locations[0]:null,[d]),h=(0,i.mN)({resolver:(0,n.u)(B),defaultValues:{name:"",email:"",subject:"",message:""}}),f=async t=>{a(!0);try{await M.TP.submit(t),e({title:"Message Sent",description:"Thank you for your message. We will get back to you soon.",variant:"success"}),h.reset()}catch(t){e({title:"Error",description:"There was a problem sending your message. Please try again later.",variant:"destructive"})}finally{a(!1)}};return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold mb-6",children:["Contact ",l]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[(0,s.jsxs)(c.Zp,{className:"col-span-1 md:col-span-2",children:[(0,s.jsxs)(c.aR,{children:[(0,s.jsx)(c.ZB,{children:"Send Us a Message"}),(0,s.jsx)(c.BT,{children:"Fill out the form below and we'll get back to you as soon as possible."})]}),(0,s.jsx)(c.Wu,{children:(0,s.jsx)(p,{...h,children:(0,s.jsxs)("form",{onSubmit:h.handleSubmit(f),className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsx)(g,{control:h.control,name:"name",render:e=>{let{field:a}=e;return(0,s.jsxs)(v,{children:[(0,s.jsx)(w,{children:"Name"}),(0,s.jsx)(j,{children:(0,s.jsx)(A.p,{placeholder:"Your name",...a,disabled:t})}),(0,s.jsx)(N,{})]})}}),(0,s.jsx)(g,{control:h.control,name:"email",render:e=>{let{field:a}=e;return(0,s.jsxs)(v,{children:[(0,s.jsx)(w,{children:"Email"}),(0,s.jsx)(j,{children:(0,s.jsx)(A.p,{type:"email",placeholder:"<EMAIL>",...a,disabled:t})}),(0,s.jsx)(N,{})]})}})]}),(0,s.jsx)(g,{control:h.control,name:"subject",render:e=>{let{field:a}=e;return(0,s.jsxs)(v,{children:[(0,s.jsx)(w,{children:"Subject"}),(0,s.jsx)(j,{children:(0,s.jsx)(A.p,{placeholder:"Subject of your message",...a,disabled:t})}),(0,s.jsx)(N,{})]})}}),(0,s.jsx)(g,{control:h.control,name:"message",render:e=>{let{field:a}=e;return(0,s.jsxs)(v,{children:[(0,s.jsx)(w,{children:"Message"}),(0,s.jsx)(j,{children:(0,s.jsx)(C,{placeholder:"Your message",className:"min-h-[150px]",...a,disabled:t})}),(0,s.jsx)(N,{})]})}}),(0,s.jsx)(o.$,{type:"submit",className:"w-full md:w-auto",disabled:t,children:t?"Sending...":"Send Message"})]})})})]}),(0,s.jsxs)(c.Zp,{children:[(0,s.jsxs)(c.aR,{children:[(0,s.jsx)(c.ZB,{children:"Contact Information"}),(0,s.jsx)(c.BT,{children:"Get in touch with us directly using the information below."})]}),(0,s.jsxs)(c.Wu,{className:"space-y-6",children:[m?(0,s.jsx)("div",{className:"flex justify-center py-4",children:(0,s.jsx)(S.A,{className:"h-6 w-6 animate-spin text-teal-600"})}):u?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-lg",children:u.name||l}),(null==u?void 0:u.address)&&(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(k.A,{className:"h-5 w-5 text-charity-primary mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Address"}),(0,s.jsx)("p",{className:"text-gray-600",children:u.address})]})]}),(null==u?void 0:u.phone)&&(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(F.A,{className:"h-5 w-5 text-charity-primary mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Phone"}),(0,s.jsx)("p",{className:"text-gray-600",children:u.phone})]})]}),(null==u?void 0:u.email)&&(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)(R.A,{className:"h-5 w-5 text-charity-primary mt-0.5"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium",children:"Email"}),(0,s.jsx)("a",{href:"mailto:".concat(u.email),className:"text-charity-primary hover:underline",children:u.email})]})]})]}):(0,s.jsx)("div",{className:"text-center py-4 text-gray-500",children:(0,s.jsx)("p",{children:"No office location information available."})}),(0,s.jsxs)("div",{className:"pt-4 border-t",children:[(0,s.jsx)("h4",{className:"font-semibold mb-2",children:"Office Hours"}),(0,s.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,s.jsx)("p",{children:"Monday - Friday: 9:00 AM - 5:00 PM"}),(0,s.jsx)("p",{children:"Saturday: 10:00 AM - 2:00 PM"}),(0,s.jsx)("p",{children:"Sunday: Closed"})]})]})]})]})]}),(0,s.jsx)(c.Zp,{className:"mb-12",children:(0,s.jsx)(c.Wu,{className:"p-6",children:(0,s.jsx)("div",{className:"aspect-w-16 aspect-h-9 w-full relative",children:m?(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100",children:(0,s.jsx)(S.A,{className:"h-8 w-8 animate-spin text-teal-600"})}):u?(0,s.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1000!2d".concat(u.longitude,"!3d").concat(u.latitude,"!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2z").concat(u.latitude,"N_").concat(u.longitude,"E!5e0!3m2!1sen!2sus!4v1644345114693!5m2!1sen!2sus"),width:"100%",height:"450",style:{border:0},allowFullScreen:!0,loading:"lazy",title:"Location Map"}):(0,s.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3024.123456789!2d-74.0059413!3d40.7128!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDDCsDQyJzQ2LjEiTiA3NMKwMDAnMjEuNCJX!5e0!3m2!1sen!2sus!4v1644345114693!5m2!1sen!2sus",width:"100%",height:"450",style:{border:0},allowFullScreen:!0,loading:"lazy",title:"Default Location Map"})})})}),(0,s.jsxs)(c.Zp,{children:[(0,s.jsxs)(c.aR,{children:[(0,s.jsx)(c.ZB,{children:"Frequently Asked Questions"}),(0,s.jsx)(c.BT,{children:"Find answers to common questions about our services and organization."})]}),(0,s.jsxs)(c.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"How can I volunteer with your organization?"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We welcome volunteers who want to contribute their time and skills. Please fill out the contact form above with your interest in volunteering, and our volunteer coordinator will get in touch with you."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"How are donations used?"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Donations directly support our programs and services. We ensure that funds are used efficiently to maximize impact in our community. For detailed information, please visit our Donate page."})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"Do you offer internship opportunities?"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Yes, we offer internship opportunities for students and recent graduates. Please contact us with your resume and area of interest for more information."})]}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsxs)("p",{className:"text-gray-600",children:["For more questions and answers, please visit our ",(0,s.jsx)("a",{href:"/faq",className:"text-charity-primary hover:underline",children:"FAQ page"}),"."]})})]})]})]})}},5731:(e,t,a)=>{"use strict";a.d(t,{AY:()=>m,EO:()=>n,TP:()=>c,YV:()=>l,jE:()=>u,l4:()=>d,lM:()=>o});var s=a(3464),r=a(1008);let i=s.A.create({baseURL:r.A.baseURL,timeout:r.A.timeout,withCredentials:r.A.withCredentials});i.interceptors.request.use(async e=>{{let t=localStorage.getItem("authToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(e)});let n={getContent:async()=>(await i.get("/about")).data,updateContent:async e=>(await i.post("/about",e)).data},l={getAll:async e=>({teamMembers:(await i.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await i.get("/team/".concat(e))).data}),create:async e=>(await i.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,t)=>(await i.put("/team/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}})).data,patch:async(e,t)=>(await i.patch("/team/".concat(e),t)).data,delete:async e=>(await i.delete("/team/".concat(e))).data},c={submit:async e=>(await i.post("/contact",e)).data,getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0,s={page:e,limit:t,...a?{status:a}:{}};return(await i.get("/contact",{params:s})).data},getById:async e=>(await i.get("/contact/".concat(e))).data,updateStatus:async(e,t)=>(await i.put("/contact/".concat(e,"/status"),{status:t})).data,delete:async e=>(await i.delete("/contact/".concat(e))).data},o={getAll:async e=>({locations:(await i.get("/locations",{params:e})).data}),getById:async e=>({location:(await i.get("/locations/".concat(e))).data}),create:async e=>{let t={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await i.post("/locations",t)).data},update:async(e,t)=>{let a={...t,isMainOffice:void 0!==t.isMainOffice?String(t.isMainOffice):void 0,active:void 0!==t.active?String(t.active):void 0};return(await i.put("/locations/".concat(e),a)).data},delete:async e=>(await i.delete("/locations/".concat(e))).data},d={getAll:async()=>(await i.get("/faqs")).data,getAllAdmin:async()=>(await i.get("/admin/faqs")).data,getById:async e=>(await i.get("/admin/faqs/".concat(e))).data,create:async e=>(await i.post("/admin/faqs",e)).data,update:async function(e,t){let a,s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(s)a={isActive:void 0===t.isActive||!!t.isActive};else{var r,n,l;a={question:null==(r=t.question)?void 0:r.trim(),answer:null==(n=t.answer)?void 0:n.trim(),category:(null==(l=t.category)?void 0:l.trim())||"General",order:"number"==typeof t.order?t.order:0,isActive:void 0===t.isActive||!!t.isActive}}try{return(await i.put("/admin/faqs/".concat(e),a)).data}catch(e){throw e}},delete:async e=>(await i.delete("/admin/faqs/".concat(e))).data},m={getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await i.get("/news?page=".concat(e,"&limit=").concat(t,"&includeAttachments=true"))).data},getBySlug:async e=>(await i.get("/news/".concat(e,"?includeAttachments=true"))).data.news,getById:async e=>(await i.get("/admin/news/".concat(e))).data.news,create:async e=>(await i.post("/admin/news",e)).data.news,update:async(e,t)=>(await i.put("/admin/news/".concat(e),t)).data.news,delete:async e=>(await i.delete("/admin/news/".concat(e))).data},u={getAllAlbums:async()=>(await i.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await i.get("/gallery/albums/".concat(e))).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await i.get("/admin/gallery/albums/".concat(e))).data}catch(e){throw e}},createAlbum:async e=>(await i.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,t)=>(await i.put("/admin/gallery/albums/".concat(e),t)).data.album,deleteAlbum:async e=>{await i.delete("/admin/gallery/albums/".concat(e))},uploadImage:async(e,t)=>{var a;let s=null==(a=document.querySelector('meta[name="csrf-token"]'))?void 0:a.getAttribute("content");return s&&t.append("_csrf",s),(await i.post("/admin/gallery/albums/".concat(e,"/images"),t,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await i.delete("/admin/gallery/images/".concat(e))}}},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>n,aR:()=>l,wL:()=>m});var s=a(5155),r=a(2115),i=a(9434);let n=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});n.displayName="Card";let l=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),...r})});l.displayName="CardHeader";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});c.displayName="CardTitle";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",a),...r})});o.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",a),...r})});d.displayName="CardContent";let m=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",a),...r})});m.displayName="CardFooter"},8398:(e,t,a)=>{Promise.resolve().then(a.bind(a,3704))},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i,oH:()=>n,vV:()=>l});var s=a(2596),r=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function n(e){return e.replace(/\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g,(e,t)=>{let a=t.split(",").map(e=>e.trim()).filter(Boolean);if(0===a.length)return e;let s=a.map(e=>'<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">'.concat(e,"</span>")).join("");return'<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">'.concat(s,"</div></div>")})}let l=(e,t)=>{}}},e=>{var t=t=>e(e.s=t);e.O(0,[598,967,951,695,441,684,358],()=>t(8398)),_N_E=e.O()}]);