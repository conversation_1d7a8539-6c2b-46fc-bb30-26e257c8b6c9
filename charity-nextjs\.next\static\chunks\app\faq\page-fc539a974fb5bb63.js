(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[505],{1008:(a,t,e)=>{"use strict";e.d(t,{A:()=>n});let n={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},1173:(a,t,e)=>{"use strict";e.d(t,{FAQPageContent:()=>p});var n=e(5155),s=e(2115),i=e(2960),c=e(2854),l=e(6474),r=e(9434);let o=c.bL,d=s.forwardRef((a,t)=>{let{className:e,...s}=a;return(0,n.jsx)(c.q7,{ref:t,className:(0,r.cn)("border-b",e),...s})});d.displayName="AccordionItem";let m=s.forwardRef((a,t)=>{let{className:e,children:s,...i}=a;return(0,n.jsx)(c.Y9,{className:"flex",children:(0,n.jsxs)(c.l9,{ref:t,className:(0,r.cn)("flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180",e),...i,children:[s,(0,n.jsx)(l.A,{className:"h-4 w-4 shrink-0 transition-transform duration-200"})]})})});m.displayName=c.l9.displayName;let u=s.forwardRef((a,t)=>{let{className:e,children:s,...i}=a;return(0,n.jsx)(c.UC,{ref:t,className:"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down",...i,children:(0,n.jsx)("div",{className:(0,r.cn)("pb-4 pt-0",e),children:s})})});u.displayName=c.UC.displayName;var y=e(4631),g=e(5731);function p(){let[a,t]=(0,s.useState)("All"),{data:e,isLoading:c,error:l}=(0,i.I)({queryKey:["faqs"],queryFn:g.l4.getAll}),r=s.useMemo(()=>{if(!(null==e?void 0:e.faqs))return{};let a={All:e.faqs.filter(a=>a.isActive)};return e.faqs.forEach(t=>{t.isActive&&(a[t.category]||(a[t.category]=[]),a[t.category].push(t))}),a},[e]),p=s.useMemo(()=>Object.keys(r).sort(),[r]),f=s.useMemo(()=>"All"===a?r.All||[]:r[a]||[],[r,a]);return c?(0,n.jsx)("div",{className:"container mx-auto px-4 py-12 flex justify-center items-center min-h-[60vh]",children:(0,n.jsx)(y.A,{className:"h-8 w-8 animate-spin text-teal-600"})}):l?(0,n.jsx)("div",{className:"container mx-auto px-4 py-12",children:(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,n.jsx)("p",{children:"Error loading FAQs. Please try again later."})})}):(0,n.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-8 text-teal-800",children:"Frequently Asked Questions"}),p.length>1&&(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsx)("div",{className:"flex flex-wrap gap-2 justify-center",children:p.map(e=>(0,n.jsx)("button",{onClick:()=>t(e),className:"px-4 py-2 rounded-full text-sm font-medium transition-colors ".concat(a===e?"bg-teal-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:e},e))})}),(0,n.jsx)("div",{className:"max-w-3xl mx-auto",children:f.length>0?(0,n.jsx)(o,{type:"single",collapsible:!0,className:"space-y-4",children:f.map(a=>(0,n.jsxs)(d,{value:a._id,className:"border rounded-lg p-2 shadow-sm",children:[(0,n.jsx)(m,{className:"text-lg font-medium text-teal-700 hover:text-teal-900",children:a.question}),(0,n.jsx)(u,{className:"text-gray-600 pt-2 pb-4 px-2",children:a.answer})]},a._id))}):(0,n.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,n.jsx)("p",{children:"No FAQs found in this category."})})})]})}},4298:(a,t,e)=>{Promise.resolve().then(e.bind(e,1173))},5731:(a,t,e)=>{"use strict";e.d(t,{AY:()=>m,EO:()=>c,TP:()=>r,YV:()=>l,jE:()=>u,l4:()=>d,lM:()=>o});var n=e(3464),s=e(1008);let i=n.A.create({baseURL:s.A.baseURL,timeout:s.A.timeout,withCredentials:s.A.withCredentials});i.interceptors.request.use(async a=>{{let t=localStorage.getItem("authToken");t&&(a.headers.Authorization="Bearer ".concat(t))}return a},a=>Promise.reject(a)),i.interceptors.response.use(a=>a,a=>{var t;return(null==(t=a.response)?void 0:t.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(a)});let c={getContent:async()=>(await i.get("/about")).data,updateContent:async a=>(await i.post("/about",a)).data},l={getAll:async a=>({teamMembers:(await i.get("/team",{params:a})).data}),getById:async a=>({teamMember:(await i.get("/team/".concat(a))).data}),create:async a=>(await i.post("/team",a,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(a,t)=>(await i.put("/team/".concat(a),t,{headers:{"Content-Type":"multipart/form-data"}})).data,delete:async a=>(await i.delete("/team/".concat(a))).data},r={submit:async a=>(await i.post("/contact",a)).data,getAll:async function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,e=arguments.length>2?arguments[2]:void 0,n={page:a,limit:t,...e?{status:e}:{}};return(await i.get("/contact",{params:n})).data},getById:async a=>(await i.get("/contact/".concat(a))).data,updateStatus:async(a,t)=>(await i.put("/contact/".concat(a,"/status"),{status:t})).data,delete:async a=>(await i.delete("/contact/".concat(a))).data},o={getAll:async a=>({locations:(await i.get("/locations",{params:a})).data}),getById:async a=>({location:(await i.get("/locations/".concat(a))).data}),create:async a=>{let t={...a,isMainOffice:void 0!==a.isMainOffice?String(a.isMainOffice):void 0,active:void 0!==a.active?String(a.active):void 0};return(await i.post("/locations",t)).data},update:async(a,t)=>{let e={...t,isMainOffice:void 0!==t.isMainOffice?String(t.isMainOffice):void 0,active:void 0!==t.active?String(t.active):void 0};return(await i.put("/locations/".concat(a),e)).data},delete:async a=>(await i.delete("/locations/".concat(a))).data},d={getAll:async()=>(await i.get("/faqs")).data,getAllAdmin:async()=>(await i.get("/admin/faqs")).data,getById:async a=>(await i.get("/admin/faqs/".concat(a))).data,create:async a=>(await i.post("/admin/faqs",a)).data,update:async function(a,t){let e,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(n)e={isActive:void 0===t.isActive||!!t.isActive};else{var s,c,l;e={question:null==(s=t.question)?void 0:s.trim(),answer:null==(c=t.answer)?void 0:c.trim(),category:(null==(l=t.category)?void 0:l.trim())||"General",order:"number"==typeof t.order?t.order:0,isActive:void 0===t.isActive||!!t.isActive}}try{return(await i.put("/admin/faqs/".concat(a),e)).data}catch(a){throw a}},delete:async a=>(await i.delete("/admin/faqs/".concat(a))).data},m={getAll:async function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await i.get("/news?page=".concat(a,"&limit=").concat(t,"&includeAttachments=true"))).data},getBySlug:async a=>(await i.get("/news/".concat(a,"?includeAttachments=true"))).data.news,getById:async a=>(await i.get("/admin/news/".concat(a))).data.news,create:async a=>(await i.post("/admin/news",a)).data.news,update:async(a,t)=>(await i.put("/admin/news/".concat(a),t)).data.news,delete:async a=>(await i.delete("/admin/news/".concat(a))).data},u={getAllAlbums:async()=>(await i.get("/gallery/albums")).data,getAlbumBySlug:async a=>{try{return(await i.get("/gallery/albums/".concat(a))).data}catch(a){throw a}},getAlbumById:async a=>{try{return(await i.get("/admin/gallery/albums/".concat(a))).data}catch(a){throw a}},createAlbum:async a=>(await i.post("/admin/gallery/albums",a)).data.album,updateAlbum:async(a,t)=>(await i.put("/admin/gallery/albums/".concat(a),t)).data.album,deleteAlbum:async a=>{await i.delete("/admin/gallery/albums/".concat(a))},uploadImage:async(a,t)=>{var e;let n=null==(e=document.querySelector('meta[name="csrf-token"]'))?void 0:e.getAttribute("content");return n&&t.append("_csrf",n),(await i.post("/admin/gallery/albums/".concat(a,"/images"),t,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async a=>{await i.delete("/admin/gallery/images/".concat(a))}}},9434:(a,t,e)=>{"use strict";e.d(t,{cn:()=>i,oH:()=>c,vV:()=>l});var n=e(2596),s=e(9688);function i(){for(var a=arguments.length,t=Array(a),e=0;e<a;e++)t[e]=arguments[e];return(0,s.QP)((0,n.$)(t))}function c(a){return a.replace(/\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g,(a,t)=>{let e=t.split(",").map(a=>a.trim()).filter(Boolean);if(0===e.length)return a;let n=e.map(a=>'<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">'.concat(a,"</span>")).join("");return'<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">'.concat(n,"</div></div>")})}let l=(a,t)=>{}}},a=>{var t=t=>a(a.s=t);a.O(0,[598,967,951,329,441,684,358],()=>t(4298)),_N_E=a.O()}]);