/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/about/page";
exports.ids = ["app/about/page"];
exports.modules = {

/***/ "(rsc)/./app/about/AboutPageContent.tsx":
/*!****************************************!*\
  !*** ./app/about/AboutPageContent.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AboutPageContent: () => (/* binding */ AboutPageContent)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AboutPageContent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AboutPageContent() from the server but AboutPageContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\about\\AboutPageContent.tsx",
"AboutPageContent",
);

/***/ }),

/***/ "(rsc)/./app/about/page.tsx":
/*!****************************!*\
  !*** ./app/about/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AboutPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _AboutPageContent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./AboutPageContent */ \"(rsc)/./app/about/AboutPageContent.tsx\");\n\n\nconst metadata = {\n    title: 'About Us - Charity Welcome Hub',\n    description: 'Learn about our mission, vision, values, and the dedicated team working to make a positive impact in our community.',\n    keywords: [\n        'charity',\n        'about us',\n        'mission',\n        'vision',\n        'team',\n        'community',\n        'volunteers'\n    ],\n    openGraph: {\n        title: 'About Us - Charity Welcome Hub',\n        description: 'Learn about our mission, vision, values, and the dedicated team working to make a positive impact in our community.',\n        type: 'website'\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'About Us - Charity Welcome Hub',\n        description: 'Learn about our mission, vision, values, and the dedicated team working to make a positive impact in our community.'\n    }\n};\nfunction AboutPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AboutPageContent__WEBPACK_IMPORTED_MODULE_1__.AboutPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\page.tsx\",\n        lineNumber: 21,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/about/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"548041302bb1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFzaWJcXE9uZURyaXZlXFxEZXNrdG9wXFxjaGFyaXR5X2luZm9cXGNoYXJpdHlfaW5mb1xcY2hhcml0eS1uZXh0anNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1NDgwNDEzMDJiYjFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/QueryProvider */ \"(rsc)/./src/components/providers/QueryProvider.tsx\");\n/* harmony import */ var _components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/seo/StructuredData */ \"(rsc)/./src/components/seo/StructuredData.tsx\");\n/* harmony import */ var _components_performance_WebVitals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/performance/WebVitals */ \"(rsc)/./src/components/performance/WebVitals.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Charity Welcome Hub - Supporting Communities Through Action\",\n        template: \"%s | Charity Welcome Hub\"\n    },\n    description: \"Join our mission to support communities through charitable work, transparency, and meaningful impact. Discover our programs, news, and ways to get involved.\",\n    keywords: [\n        \"charity\",\n        \"community\",\n        \"support\",\n        \"donations\",\n        \"volunteer\",\n        \"nonprofit\",\n        \"humanitarian\",\n        \"social impact\",\n        \"community service\",\n        \"charitable organization\",\n        \"helping others\",\n        \"social good\"\n    ],\n    authors: [\n        {\n            name: \"Charity Welcome Hub\"\n        }\n    ],\n    creator: \"Charity Welcome Hub\",\n    publisher: \"Charity Welcome Hub\",\n    metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),\n    alternates: {\n        canonical: '/'\n    },\n    openGraph: {\n        title: \"Charity Welcome Hub - Supporting Communities Through Action\",\n        description: \"Join our mission to support communities through charitable work, transparency, and meaningful impact.\",\n        type: \"website\",\n        locale: \"en_US\",\n        url: '/',\n        siteName: \"Charity Welcome Hub\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Charity Welcome Hub - Supporting Communities Through Action\",\n        description: \"Join our mission to support communities through charitable work, transparency, and meaningful impact.\",\n        creator: \"@CharityWelcomeHub\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_5__.OrganizationStructuredData, {\n                        name: \"Charity Welcome Hub\",\n                        description: \"Supporting communities through charitable work, transparency, and meaningful impact\",\n                        url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',\n                        contactPoint: {\n                            telephone: \"******-0123\",\n                            contactType: \"customer service\",\n                            email: \"<EMAIL>\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_5__.WebsiteStructuredData, {\n                        name: \"Charity Welcome Hub\",\n                        description: \"Supporting communities through charitable work, transparency, and meaningful impact\",\n                        url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-background font-sans antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_WebVitals__WEBPACK_IMPORTED_MODULE_6__.WebVitals, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_4__.QueryProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col min-h-screen\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"flex-1\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(rsc)/./src/components/ui/skeleton.tsx\");\n\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-8 w-64 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-4 w-96\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: [\n                    ...Array(6)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                className: \"h-48 w-full rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-3/4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/about/page.tsx */ \"(rsc)/./app/about/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'about',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\"],\n'loading': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/about/page\",\n        pathname: \"/about\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cabout%5C%5CAboutPageContent.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cabout%5C%5CAboutPageContent.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/about/AboutPageContent.tsx */ \"(rsc)/./app/about/AboutPageContent.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hhc2liJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDY2hhcml0eV9pbmZvJTVDJTVDY2hhcml0eV9pbmZvJTVDJTVDY2hhcml0eS1uZXh0anMlNUMlNUNhcHAlNUMlNUNhYm91dCU1QyU1Q0Fib3V0UGFnZUNvbnRlbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQWJvdXRQYWdlQ29udGVudCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEtBQTRMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBYm91dFBhZ2VDb250ZW50XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcaGFzaWJcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxjaGFyaXR5X2luZm9cXFxcY2hhcml0eV9pbmZvXFxcXGNoYXJpdHktbmV4dGpzXFxcXGFwcFxcXFxhYm91dFxcXFxBYm91dFBhZ2VDb250ZW50LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cabout%5C%5CAboutPageContent.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(rsc)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(rsc)/./src/components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/WebVitals.tsx */ \"(rsc)/./src/components/performance/WebVitals.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/QueryProvider.tsx */ \"(rsc)/./src/components/providers/QueryProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ Footer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Footer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Footer.tsx",
"Footer",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./src/components/performance/WebVitals.tsx":
/*!**************************************************!*\
  !*** ./src/components/performance/WebVitals.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PageLoadMetrics: () => (/* binding */ PageLoadMetrics),
/* harmony export */   WebVitals: () => (/* binding */ WebVitals),
/* harmony export */   usePerformanceMetric: () => (/* binding */ usePerformanceMetric)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const WebVitals = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx",
"WebVitals",
);const usePerformanceMetric = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call usePerformanceMetric() from the server but usePerformanceMetric is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx",
"usePerformanceMetric",
);const PageLoadMetrics = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PageLoadMetrics() from the server but PageLoadMetrics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx",
"PageLoadMetrics",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const QueryProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\providers\\QueryProvider.tsx",
"QueryProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/seo/StructuredData.tsx":
/*!***********************************************!*\
  !*** ./src/components/seo/StructuredData.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BreadcrumbStructuredData: () => (/* binding */ BreadcrumbStructuredData),\n/* harmony export */   NewsStructuredData: () => (/* binding */ NewsStructuredData),\n/* harmony export */   OrganizationStructuredData: () => (/* binding */ OrganizationStructuredData),\n/* harmony export */   WebsiteStructuredData: () => (/* binding */ WebsiteStructuredData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction OrganizationStructuredData({ name, description, url, logo, contactPoint }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'Organization',\n        name,\n        description,\n        url,\n        ...logo && {\n            logo\n        },\n        ...contactPoint && {\n            contactPoint\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\nfunction NewsStructuredData({ article, organizationName, organizationUrl }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'NewsArticle',\n        headline: article.title,\n        description: article.body.substring(0, 160),\n        datePublished: article.publishDate,\n        author: {\n            '@type': 'Organization',\n            name: organizationName,\n            url: organizationUrl\n        },\n        publisher: {\n            '@type': 'Organization',\n            name: organizationName,\n            url: organizationUrl\n        },\n        mainEntityOfPage: {\n            '@type': 'WebPage',\n            '@id': `${organizationUrl}/news/${article.slug}`\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\nfunction BreadcrumbStructuredData({ items }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'BreadcrumbList',\n        itemListElement: items.map((item, index)=>({\n                '@type': 'ListItem',\n                position: index + 1,\n                name: item.name,\n                item: item.url\n            }))\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\nfunction WebsiteStructuredData({ name, description, url }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'WebSite',\n        name,\n        description,\n        url,\n        potentialAction: {\n            '@type': 'SearchAction',\n            target: {\n                '@type': 'EntryPoint',\n                urlTemplate: `${url}/search?q={search_term_string}`\n            },\n            'query-input': 'required name=search_term_string'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/seo/StructuredData.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/skeleton.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/skeleton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9za2VsZXRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFFaEMsU0FBU0MsU0FBUyxFQUNoQkMsU0FBUyxFQUNULEdBQUdDLE9BQ2tDO0lBQ3JDLHFCQUNFLDhEQUFDQztRQUNDRixXQUFXRiw4Q0FBRUEsQ0FBQyxxQ0FBcUNFO1FBQ2xELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBRW1CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhhc2liXFxPbmVEcml2ZVxcRGVza3RvcFxcY2hhcml0eV9pbmZvXFxjaGFyaXR5X2luZm9cXGNoYXJpdHktbmV4dGpzXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxza2VsZXRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBTa2VsZXRvbih7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50Pikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT17Y24oXCJhbmltYXRlLXB1bHNlIHJvdW5kZWQtbWQgYmctbXV0ZWRcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IFNrZWxldG9uIH1cbiJdLCJuYW1lcyI6WyJjbiIsIlNrZWxldG9uIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJkaXYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/skeleton.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   logInfo: () => (/* binding */ logInfo),\n/* harmony export */   logWarning: () => (/* binding */ logWarning),\n/* harmony export */   parseNewsContent: () => (/* binding */ parseNewsContent),\n/* harmony export */   sanitizeData: () => (/* binding */ sanitizeData)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Parse and format tags in news content\n * Converts text in the format \"**Tags:** tag1, tag2, tag3\" to HTML tags\n * @param content - The HTML content to parse\n * @returns Formatted HTML content with tags\n */ function parseNewsContent(content) {\n    // Regular expression to match tag format: **Tags:** tag1, tag2, tag3\n    const tagRegex = /\\*\\*Tags:\\*\\*\\s*([^<]+)(?=<\\/p>|$)/g;\n    // Replace matched tag sections with formatted tags\n    return content.replace(tagRegex, (match, tagList)=>{\n        // Split the tag list by commas and trim whitespace\n        const tags = tagList.split(',').map((tag)=>tag.trim()).filter(Boolean);\n        if (tags.length === 0) {\n            return match; // No valid tags found, return original text\n        }\n        // Create HTML for tags\n        const tagsHtml = tags.map((tag)=>`<span class=\"inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2\">${tag}</span>`).join('');\n        return `<div class=\"mt-4\"><span class=\"font-semibold\">Tags:</span> <div class=\"flex flex-wrap mt-1\">${tagsHtml}</div></div>`;\n    });\n}\n/**\n * Sanitize sensitive data before logging\n * Removes passwords, tokens, etc.\n */ const sanitizeData = (data)=>{\n    if (!data || typeof data !== 'object') return data;\n    // Create a shallow copy to avoid modifying the original\n    const sanitized = {\n        ...data\n    };\n    // List of sensitive fields to mask\n    const sensitiveFields = [\n        'password',\n        'passwordHash',\n        'token',\n        'secret',\n        'apiKey',\n        'authorization',\n        'accessToken',\n        'refreshToken',\n        'csrf',\n        'cookie',\n        'session',\n        'key',\n        'credential',\n        'auth'\n    ];\n    // Mask sensitive fields\n    Object.keys(sanitized).forEach((key)=>{\n        const lowerKey = key.toLowerCase();\n        if (sensitiveFields.some((field)=>lowerKey.includes(field))) {\n            sanitized[key] = '[REDACTED]';\n        } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {\n            // Recursively sanitize nested objects\n            sanitized[key] = sanitizeData(sanitized[key]);\n        }\n    });\n    return sanitized;\n};\n/**\n * Log information to the console in development mode\n */ const logInfo = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.log(message, sanitizeData(data));\n        } else {\n            console.log(message);\n        }\n    }\n};\n/**\n * Log errors to the console\n * Always logs in production, but sanitizes sensitive data\n */ const logError = (message, error)=>{\n    if (error instanceof Error) {\n        // For Error objects, log the message and stack\n        console.error(message, {\n            message: error.message,\n            stack: error.stack\n        });\n    } else if (error) {\n        // For other data, sanitize before logging\n        console.error(message, sanitizeData(error));\n    } else {\n        console.error(message);\n    }\n};\n/**\n * Log warnings to the console in development mode\n */ const logWarning = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.warn(message, sanitizeData(data));\n        } else {\n            console.warn(message);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./app/about/AboutPageContent.tsx":
/*!****************************************!*\
  !*** ./app/about/AboutPageContent.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AboutPageContent: () => (/* binding */ AboutPageContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(ssr)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_about_TeamSection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/about/TeamSection */ \"(ssr)/./src/components/about/TeamSection.tsx\");\n/* __next_internal_client_entry_do_not_use__ AboutPageContent auto */ \n\n\n\n\n\n\n\nfunction AboutPageContent() {\n    const organizationName = 'Charity Welcome Hub';\n    // Fetch about page content from the backend\n    const { data, isLoading, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'aboutContent'\n        ],\n        queryFn: _lib_api__WEBPACK_IMPORTED_MODULE_4__.aboutApi.getContent,\n        staleTime: 5 * 60 * 1000\n    });\n    // Default content to use if backend data is not available\n    const aboutContent = data?.aboutContent || {\n        mission: 'Our mission is to provide support and resources to those in need within our community.',\n        vision: 'We believe in creating a welcoming environment where everyone can find the help they need, regardless of their background or circumstances.',\n        foundedYear: '2010',\n        volunteersCount: '50',\n        peopleHelpedCount: '10,000',\n        communitiesCount: '5'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-6\",\n                children: [\n                    \"About \",\n                    organizationName\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        className: \"col-span-1 md:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold mb-4 text-charity-primary\",\n                                    children: \"Our Mission\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this),\n                                isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                            className: \"h-4 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                            lineNumber: 42,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                            className: \"h-4 w-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                            className: \"h-4 w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-4\",\n                                            children: aboutContent.mission\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                            lineNumber: 48,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-4\",\n                                            children: aboutContent.vision\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold mb-4 text-charity-primary\",\n                                    children: \"Quick Facts\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"20 6 9 17 4 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 62,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Founded in \",\n                                                        aboutContent.foundedYear || '2010'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"20 6 9 17 4 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 70,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Over \",\n                                                        aboutContent.volunteersCount || '50',\n                                                        \" dedicated volunteers\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"20 6 9 17 4 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 78,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                        lineNumber: 77,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Helped over \",\n                                                        aboutContent.peopleHelpedCount || '10,000',\n                                                        \" individuals\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            className: \"flex items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"16\",\n                                                        height: \"16\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"20 6 9 17 4 12\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"Active in \",\n                                                        aboutContent.communitiesCount || '5',\n                                                        \" local communities\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 89,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold mb-6 text-charity-primary\",\n                        children: \"Our Values\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-charity-primary/10 p-4 rounded-full mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"text-charity-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M18 8a6 6 0 0 0-6-6 6 6 0 0 0-6 6c0 7 6 13 6 13s6-6 6-13z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"12\",\n                                                            cy: \"8\",\n                                                            r: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 105,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-2\",\n                                                children: \"Compassion\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"We approach every individual with empathy and understanding, recognizing their unique circumstances and needs.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-charity-primary/10 p-4 rounded-full mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"text-charity-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"m7 9 3 3-3 3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 120,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M14 9h3v6h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-2\",\n                                                children: \"Integrity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"We maintain the highest standards of honesty and transparency in all our operations and relationships.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-charity-primary/10 p-4 rounded-full mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"text-charity-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 135,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                            cx: \"9\",\n                                                            cy: \"7\",\n                                                            r: \"4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 136,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M23 21v-2a4 4 0 0 0-3-3.87\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 137,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M16 3.13a4 4 0 0 1 0 7.75\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                            lineNumber: 138,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-semibold mb-2\",\n                                                children: \"Community\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"We believe in the power of community and work together to create positive change and lasting impact.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold mb-6 text-charity-primary\",\n                        children: \"Get Involved\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-700 mb-4\",\n                                    children: \"There are many ways to support our mission and make a difference in our community. Whether you're interested in volunteering your time, making a donation, or partnering with us, we welcome your involvement.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-4 justify-center md:justify-start\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/contact\",\n                                        className: \"bg-white border border-charity-primary text-charity-primary hover:bg-charity-primary/10 font-medium py-2 px-6 rounded-md transition-colors duration-200\",\n                                        children: \"Contact Us\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                lineNumber: 149,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-16 bg-gray-50 -mx-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-4\",\n                                    children: \"Our Team\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg text-gray-600 max-w-2xl mx-auto\",\n                                    children: \"Meet the dedicated individuals who make our mission possible.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_about_TeamSection__WEBPACK_IMPORTED_MODULE_6__.TeamSection, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\about\\\\AboutPageContent.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/about/AboutPageContent.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cabout%5C%5CAboutPageContent.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cabout%5C%5CAboutPageContent.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/about/AboutPageContent.tsx */ \"(ssr)/./app/about/AboutPageContent.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hhc2liJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDY2hhcml0eV9pbmZvJTVDJTVDY2hhcml0eV9pbmZvJTVDJTVDY2hhcml0eS1uZXh0anMlNUMlNUNhcHAlNUMlNUNhYm91dCU1QyU1Q0Fib3V0UGFnZUNvbnRlbnQudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQWJvdXRQYWdlQ29udGVudCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEtBQTRMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJBYm91dFBhZ2VDb250ZW50XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcaGFzaWJcXFxcT25lRHJpdmVcXFxcRGVza3RvcFxcXFxjaGFyaXR5X2luZm9cXFxcY2hhcml0eV9pbmZvXFxcXGNoYXJpdHktbmV4dGpzXFxcXGFwcFxcXFxhYm91dFxcXFxBYm91dFBhZ2VDb250ZW50LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cabout%5C%5CAboutPageContent.tsx%22%2C%22ids%22%3A%5B%22AboutPageContent%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/WebVitals.tsx */ \"(ssr)/./src/components/performance/WebVitals.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/QueryProvider.tsx */ \"(ssr)/./src/components/providers/QueryProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/about/TeamSection.tsx":
/*!**********************************************!*\
  !*** ./src/components/about/TeamSection.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeamSection: () => (/* binding */ TeamSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(ssr)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,Phone,Twitter,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,Phone,Twitter,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,Phone,Twitter,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,Phone,Twitter,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,Phone,Twitter,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Linkedin,Mail,Phone,Twitter,UserCircle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _config_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/config/api */ \"(ssr)/./src/config/api.ts\");\n/* __next_internal_client_entry_do_not_use__ TeamSection auto */ \n\n\n\n\n\n\n\nfunction TeamSection() {\n    // Fetch team members\n    const { data, isLoading, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.useQuery)({\n        queryKey: [\n            'team-members-public'\n        ],\n        queryFn: {\n            \"TeamSection.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_2__.teamApi.getAll({\n                    active: true\n                })\n        }[\"TeamSection.useQuery\"],\n        staleTime: 5 * 60 * 1000\n    });\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n            children: [\n                ...Array(3)\n            ].map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    className: \"overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"aspect-square relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                className: \"h-full w-full absolute\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                    className: \"h-6 w-3/4 mb-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                    className: \"h-4 w-1/2 mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 29,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                    className: \"h-20 w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Unable to load team members. Please try again later.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n            lineNumber: 40,\n            columnNumber: 7\n        }, this);\n    }\n    const teamMembers = data?.teamMembers || [];\n    if (teamMembers.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8 text-gray-500\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"No team members to display.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n        children: teamMembers.map((member)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TeamMemberCard, {\n                member: member\n            }, member._id, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                lineNumber: 59,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\nfunction TeamMemberCard({ member }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"aspect-square relative bg-gray-100\",\n                children: member.photo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: `${_config_api__WEBPACK_IMPORTED_MODULE_5__[\"default\"].baseURL}${member.photo}`,\n                    alt: member.name,\n                    className: \"w-full h-full object-cover\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-24 w-24 text-gray-300\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                className: \"p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-gray-900\",\n                        children: member.name\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm font-medium text-teal-600 mb-3\",\n                        children: member.position\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-sm mb-4 line-clamp-4\",\n                        children: member.bio\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3 mt-4\",\n                        children: [\n                            member.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: `mailto:${member.email}`,\n                                className: \"text-gray-500 hover:text-teal-600 transition-colors\",\n                                \"aria-label\": `Email ${member.name}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            member.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: `tel:${member.phone}`,\n                                className: \"text-gray-500 hover:text-teal-600 transition-colors\",\n                                \"aria-label\": `Call ${member.name}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this),\n                            member.socialLinks?.linkedin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: member.socialLinks.linkedin,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-gray-500 hover:text-blue-600 transition-colors\",\n                                \"aria-label\": `${member.name}'s LinkedIn profile`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 13\n                            }, this),\n                            member.socialLinks?.twitter && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: member.socialLinks.twitter,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-gray-500 hover:text-blue-400 transition-colors\",\n                                \"aria-label\": `${member.name}'s Twitter profile`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this),\n                            member.socialLinks?.facebook && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: member.socialLinks.facebook,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-gray-500 hover:text-blue-800 transition-colors\",\n                                \"aria-label\": `${member.name}'s Facebook profile`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Facebook_Linkedin_Mail_Phone_Twitter_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\about\\\\TeamSection.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/about/TeamSection.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rss.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\n// import { locationsApi } from \"@/services/api\";\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    // Temporary: Comment out API call for migration\n    // const { data: locationsData, isLoading: isLoadingLocations } = useQuery({\n    //   queryKey: ['locations'],\n    //   queryFn: () => locationsApi.getAll({ active: true }),\n    //   staleTime: 5 * 60 * 1000, // 5 minutes\n    // });\n    // Temporary mock data for migration\n    const isLoadingLocations = false;\n    const locationsData = {\n        locations: [\n            {\n                isMainOffice: true,\n                phone: \"+****************\",\n                email: \"<EMAIL>\",\n                address: \"123 Charity Street, City, State 12345\"\n            }\n        ]\n    };\n    // Find the main office location\n    const mainOffice = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"Footer.useMemo[mainOffice]\": ()=>{\n            if (!locationsData?.locations) return null;\n            return locationsData.locations.find({\n                \"Footer.useMemo[mainOffice]\": (loc)=>loc.isMainOffice\n            }[\"Footer.useMemo[mainOffice]\"]) || locationsData.locations[0];\n        }\n    }[\"Footer.useMemo[mainOffice]\"], [\n        locationsData\n    ]);\n    // Show placeholder content while loading\n    if (isLoadingLocations) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n            className: \"bg-teal-50 border-t border-teal-100 py-10 mt-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 text-center text-teal-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading footer content...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-teal-50 border-t border-teal-100 py-10 mt-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold text-xl mb-4 text-teal-800\",\n                                    children: \"Charity Info\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-teal-700 mb-4\",\n                                    children: \"Supporting communities and making a difference\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        mainOffice?.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-700 hover:text-teal-900 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: mainOffice.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this),\n                                        mainOffice?.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-700 hover:text-teal-900 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: `mailto:${mainOffice.email}`,\n                                                    className: \"hover:text-teal-900\",\n                                                    children: mainOffice.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        mainOffice?.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-700 hover:text-teal-900 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: mainOffice.address\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-bold mb-4 text-teal-800\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/news\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"News\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/gallery\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Gallery\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/about\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"About Us\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Contact Us\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/search\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Search\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-bold mb-4 text-teal-800\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/faq\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"FAQ\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/donate\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"How to Donate\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-6 border-t border-teal-100 text-center text-teal-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"\\xa9 \",\n                                currentYear,\n                                \" Charity Info. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 flex items-center justify-center text-sm\",\n                            children: [\n                                \"Built with\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mx-1 text-pink-500 fill-pink-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                \" for charitable causes\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const { isAuthenticated, user, logout } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Temporary auth state for migration\n    const isAuthenticated = false;\n    const user = null;\n    const logout = ()=>{};\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (searchQuery.trim()) {\n            router.push(`/search?q=${encodeURIComponent(searchQuery)}`);\n            setSearchQuery(\"\");\n            setIsMenuOpen(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 bg-charity-muted border-b border-gray-200 shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-4 flex items-center justify-between bg-charity-muted\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"font-bold text-2xl text-primary mr-4\",\n                            children: \"Charity Info\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center space-x-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/news\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"News\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/gallery\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"Gallery\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/about\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"About Us\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        type: \"search\",\n                                        placeholder: \"Search...\",\n                                        className: \"w-64 pr-10\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        size: \"icon\",\n                                        variant: \"ghost\",\n                                        className: \"absolute right-0 top-0 h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/admin\",\n                                        className: \"text-gray-700 hover:text-primary font-medium\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>logout(),\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" Login\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        className: \"md:hidden\",\n                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 25\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 53\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-charity-muted border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"container mx-auto px-4 py-4 space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/news\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"News\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/gallery\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"Gallery\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/about\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"About Us\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/contact\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"Contact\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSearch,\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    type: \"search\",\n                                    placeholder: \"Search...\",\n                                    className: \"w-full pr-10\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    size: \"icon\",\n                                    variant: \"ghost\",\n                                    className: \"absolute right-0 top-0 h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this),\n                        isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin\",\n                                    className: \"block text-gray-700 hover:text-primary font-medium\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        logout();\n                                        setIsMenuOpen(false);\n                                    },\n                                    className: \"w-full\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            asChild: true,\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, this),\n                                    \" Login\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/performance/WebVitals.tsx":
/*!**************************************************!*\
  !*** ./src/components/performance/WebVitals.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageLoadMetrics: () => (/* binding */ PageLoadMetrics),\n/* harmony export */   WebVitals: () => (/* binding */ WebVitals),\n/* harmony export */   usePerformanceMetric: () => (/* binding */ usePerformanceMetric)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var web_vitals__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! web-vitals */ \"(ssr)/./node_modules/web-vitals/dist/web-vitals.js\");\n/* __next_internal_client_entry_do_not_use__ WebVitals,usePerformanceMetric,PageLoadMetrics auto */ \n\nfunction sendToAnalytics(metric) {\n    // In production, you would send this to your analytics service\n    // For now, we'll just log to console in development\n    if (true) {\n        console.log('Web Vital:', metric);\n    }\n// Example: Send to Google Analytics\n// gtag('event', metric.name, {\n//   event_category: 'Web Vitals',\n//   event_label: metric.id,\n//   value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),\n//   non_interaction: true,\n// });\n// Example: Send to custom analytics endpoint\n// fetch('/api/analytics/web-vitals', {\n//   method: 'POST',\n//   headers: { 'Content-Type': 'application/json' },\n//   body: JSON.stringify(metric),\n// });\n}\nfunction WebVitals() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"WebVitals.useEffect\": ()=>{\n            // Measure Core Web Vitals\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onCLS)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onINP)(sendToAnalytics); // INP replaced FID\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onFCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onLCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onTTFB)(sendToAnalytics);\n        }\n    }[\"WebVitals.useEffect\"], []);\n    return null; // This component doesn't render anything\n}\n// Hook for measuring custom metrics\nfunction usePerformanceMetric(name, value) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceMetric.useEffect\": ()=>{\n            const metric = {\n                name,\n                value,\n                id: `${name}-${Date.now()}`,\n                delta: value,\n                entries: []\n            };\n            sendToAnalytics(metric);\n        }\n    }[\"usePerformanceMetric.useEffect\"], [\n        name,\n        value\n    ]);\n}\n// Component for measuring page load performance\nfunction PageLoadMetrics({ pageName }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"PageLoadMetrics.useEffect\": ()=>{\n            const startTime = performance.now();\n            return ({\n                \"PageLoadMetrics.useEffect\": ()=>{\n                    const endTime = performance.now();\n                    const loadTime = endTime - startTime;\n                    const metric = {\n                        name: 'page_load_time',\n                        value: loadTime,\n                        id: `${pageName}-${Date.now()}`,\n                        delta: loadTime,\n                        entries: [],\n                        page: pageName\n                    };\n                    sendToAnalytics(metric);\n                }\n            })[\"PageLoadMetrics.useEffect\"];\n        }\n    }[\"PageLoadMetrics.useEffect\"], [\n        pageName\n    ]);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/performance/WebVitals.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n\n\nfunction QueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"QueryProvider.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        staleTime: 60 * 1000,\n                        retry: 1\n                    }\n                }\n            })\n    }[\"QueryProvider.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\providers\\\\QueryProvider.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvUXVlcnlQcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFeUU7QUFDeEM7QUFFMUIsU0FBU0csY0FBYyxFQUFFQyxRQUFRLEVBQWlDO0lBQ3ZFLE1BQU0sQ0FBQ0MsWUFBWSxHQUFHSCwrQ0FBUUE7a0NBQzVCLElBQ0UsSUFBSUYsOERBQVdBLENBQUM7Z0JBQ2RNLGdCQUFnQjtvQkFDZEMsU0FBUzt3QkFDUEMsV0FBVyxLQUFLO3dCQUNoQkMsT0FBTztvQkFDVDtnQkFDRjtZQUNGOztJQUdKLHFCQUNFLDhEQUFDUixzRUFBbUJBO1FBQUNTLFFBQVFMO2tCQUMxQkQ7Ozs7OztBQUdQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhhc2liXFxPbmVEcml2ZVxcRGVza3RvcFxcY2hhcml0eV9pbmZvXFxjaGFyaXR5X2luZm9cXGNoYXJpdHktbmV4dGpzXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVyc1xcUXVlcnlQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxuZXhwb3J0IGZ1bmN0aW9uIFF1ZXJ5UHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCBbcXVlcnlDbGllbnRdID0gdXNlU3RhdGUoXG4gICAgKCkgPT5cbiAgICAgIG5ldyBRdWVyeUNsaWVudCh7XG4gICAgICAgIGRlZmF1bHRPcHRpb25zOiB7XG4gICAgICAgICAgcXVlcmllczoge1xuICAgICAgICAgICAgc3RhbGVUaW1lOiA2MCAqIDEwMDAsIC8vIDEgbWludXRlXG4gICAgICAgICAgICByZXRyeTogMSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSlcbiAgKTtcblxuICByZXR1cm4gKFxuICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvUXVlcnlDbGllbnRQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJRdWVyeUNsaWVudCIsIlF1ZXJ5Q2xpZW50UHJvdmlkZXIiLCJ1c2VTdGF0ZSIsIlF1ZXJ5UHJvdmlkZXIiLCJjaGlsZHJlbiIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyaWVzIiwic3RhbGVUaW1lIiwicmV0cnkiLCJjbGllbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/QueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200\", {\n    variants: {\n        variant: {\n            default: \"bg-charity-primary text-white hover:bg-charity-secondary\",\n            destructive: \"bg-charity-destructive text-white hover:bg-charity-destructive/90\",\n            outline: \"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-charity-light text-charity-dark hover:bg-charity-light/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-charity-primary underline-offset-4 hover:underline\",\n            accent: \"bg-charity-accent text-charity-dark hover:bg-charity-accent/80\",\n            success: \"bg-charity-success text-white hover:bg-charity-success/90\",\n            warning: \"bg-charity-warning text-white hover:bg-charity-warning/90\",\n            info: \"bg-charity-info text-white hover:bg-charity-info/90\",\n            donate: \"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3 py-1.5 text-xs\",\n            lg: \"h-11 rounded-md px-8 py-2.5\",\n            xl: \"h-12 rounded-md px-10 py-3 text-base\",\n            icon: \"h-10 w-10 rounded-full\",\n            wide: \"h-10 px-8 py-2 w-full\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 53,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border-[1px] border-input bg-background px-3 py-2 text-base text-foreground ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground/60 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-charity-primary focus-visible:border-charity-primary focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200 md:text-sm shadow-none\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/skeleton.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/skeleton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9za2VsZXRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFFaEMsU0FBU0MsU0FBUyxFQUNoQkMsU0FBUyxFQUNULEdBQUdDLE9BQ2tDO0lBQ3JDLHFCQUNFLDhEQUFDQztRQUNDRixXQUFXRiw4Q0FBRUEsQ0FBQyxxQ0FBcUNFO1FBQ2xELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBRW1CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhhc2liXFxPbmVEcml2ZVxcRGVza3RvcFxcY2hhcml0eV9pbmZvXFxjaGFyaXR5X2luZm9cXGNoYXJpdHktbmV4dGpzXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxza2VsZXRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBTa2VsZXRvbih7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50Pikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT17Y24oXCJhbmltYXRlLXB1bHNlIHJvdW5kZWQtbWQgYmctbXV0ZWRcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IFNrZWxldG9uIH1cbiJdLCJuYW1lcyI6WyJjbiIsIlNrZWxldG9uIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJkaXYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/skeleton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/config/api.ts":
/*!***************************!*\
  !*** ./src/config/api.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// API configuration\nconst API_CONFIG = {\n    baseURL:  false ? 0 // In production, use relative path to Next.js API routes\n     : '/api',\n    backendURL:  false ? 0 : 'http://localhost:5000',\n    timeout: 15000,\n    withCredentials: true,\n    headers: {\n        'Cache-Control': 'max-age=300',\n        'Content-Type': 'application/json'\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (API_CONFIG);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29uZmlnL2FwaS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsb0JBQW9CO0FBQ3BCLE1BQU1BLGFBQWE7SUFDakJDLFNBQVNDLE1BQXFDLEdBQzFDLENBQU0sQ0FBQyx5REFBeUQ7T0FDaEU7SUFDSkMsWUFBWUQsTUFBcUMsR0FDN0NBLENBQThELEdBQzlEO0lBQ0pJLFNBQVM7SUFDVEMsaUJBQWlCO0lBQ2pCQyxTQUFTO1FBQ1AsaUJBQWlCO1FBQ2pCLGdCQUFnQjtJQUNsQjtBQUNGO0FBRUEsaUVBQWVSLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFzaWJcXE9uZURyaXZlXFxEZXNrdG9wXFxjaGFyaXR5X2luZm9cXGNoYXJpdHlfaW5mb1xcY2hhcml0eS1uZXh0anNcXHNyY1xcY29uZmlnXFxhcGkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQVBJIGNvbmZpZ3VyYXRpb25cbmNvbnN0IEFQSV9DT05GSUcgPSB7XG4gIGJhc2VVUkw6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbidcbiAgICA/ICcvYXBpJyAvLyBJbiBwcm9kdWN0aW9uLCB1c2UgcmVsYXRpdmUgcGF0aCB0byBOZXh0LmpzIEFQSSByb3V0ZXNcbiAgICA6ICcvYXBpJywgLy8gSW4gZGV2ZWxvcG1lbnQsIHVzZSBOZXh0LmpzIEFQSSByb3V0ZXNcbiAgYmFja2VuZFVSTDogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJ1xuICAgID8gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQkFDS0VORF9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6NTAwMCdcbiAgICA6ICdodHRwOi8vbG9jYWxob3N0OjUwMDAnLCAvLyBEaXJlY3QgYmFja2VuZCBVUkwgZm9yIHVwbG9hZHMvaW1hZ2VzXG4gIHRpbWVvdXQ6IDE1MDAwLCAvLyBJbmNyZWFzZWQgdGltZW91dCBmb3Igc2xvd2VyIGNvbm5lY3Rpb25zXG4gIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcbiAgaGVhZGVyczoge1xuICAgICdDYWNoZS1Db250cm9sJzogJ21heC1hZ2U9MzAwJywgLy8gQ2FjaGUgcmVzcG9uc2VzIGZvciA1IG1pbnV0ZXNcbiAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICB9LFxufTtcblxuZXhwb3J0IGRlZmF1bHQgQVBJX0NPTkZJRztcbiJdLCJuYW1lcyI6WyJBUElfQ09ORklHIiwiYmFzZVVSTCIsInByb2Nlc3MiLCJiYWNrZW5kVVJMIiwiZW52IiwiTkVYVF9QVUJMSUNfQkFDS0VORF9VUkwiLCJ0aW1lb3V0Iiwid2l0aENyZWRlbnRpYWxzIiwiaGVhZGVycyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/config/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aboutApi: () => (/* binding */ aboutApi),\n/* harmony export */   contactApi: () => (/* binding */ contactApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   faqApi: () => (/* binding */ faqApi),\n/* harmony export */   galleryApi: () => (/* binding */ galleryApi),\n/* harmony export */   locationsApi: () => (/* binding */ locationsApi),\n/* harmony export */   newsApi: () => (/* binding */ newsApi),\n/* harmony export */   teamApi: () => (/* binding */ teamApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/api */ \"(ssr)/./src/config/api.ts\");\n\n\n// Create axios instance with config\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: _config_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].baseURL,\n    timeout: _config_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].timeout,\n    withCredentials: _config_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].withCredentials\n});\n// Add request interceptor for authentication\napi.interceptors.request.use(async (config)=>{\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Add response interceptor for error handling\napi.interceptors.response.use((response)=>response, (error)=>{\n    if (error.response?.status === 401 && \"undefined\" !== 'undefined') {}\n    return Promise.reject(error);\n});\n// About API\nconst aboutApi = {\n    getContent: async ()=>{\n        const response = await api.get('/about');\n        return response.data;\n    },\n    updateContent: async (aboutData)=>{\n        const response = await api.post('/about', aboutData);\n        return response.data;\n    }\n};\n// Team API\nconst teamApi = {\n    getAll: async (params)=>{\n        const response = await api.get('/team', {\n            params\n        });\n        return {\n            teamMembers: response.data\n        };\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/team/${id}`);\n        return {\n            teamMember: response.data\n        };\n    },\n    create: async (formData)=>{\n        const response = await api.post('/team', formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return response.data;\n    },\n    update: async (id, formData)=>{\n        const response = await api.put(`/team/${id}`, formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return response.data;\n    },\n    delete: async (id)=>{\n        const response = await api.delete(`/team/${id}`);\n        return response.data;\n    }\n};\n// Contact API\nconst contactApi = {\n    submit: async (contactData)=>{\n        const response = await api.post('/contact', contactData);\n        return response.data;\n    },\n    getAll: async (page = 1, limit = 10, status)=>{\n        const params = {\n            page,\n            limit,\n            ...status ? {\n                status\n            } : {}\n        };\n        const response = await api.get('/contact', {\n            params\n        });\n        return response.data;\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/contact/${id}`);\n        return response.data;\n    },\n    updateStatus: async (id, status)=>{\n        const response = await api.put(`/contact/${id}/status`, {\n            status\n        });\n        return response.data;\n    },\n    delete: async (id)=>{\n        const response = await api.delete(`/contact/${id}`);\n        return response.data;\n    }\n};\n// Locations API\nconst locationsApi = {\n    getAll: async (params)=>{\n        const response = await api.get('/locations', {\n            params\n        });\n        return {\n            locations: response.data\n        };\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/locations/${id}`);\n        return {\n            location: response.data\n        };\n    },\n    create: async (locationData)=>{\n        const formattedData = {\n            ...locationData,\n            isMainOffice: locationData.isMainOffice !== undefined ? String(locationData.isMainOffice) : undefined,\n            active: locationData.active !== undefined ? String(locationData.active) : undefined\n        };\n        const response = await api.post('/locations', formattedData);\n        return response.data;\n    },\n    update: async (id, locationData)=>{\n        const formattedData = {\n            ...locationData,\n            isMainOffice: locationData.isMainOffice !== undefined ? String(locationData.isMainOffice) : undefined,\n            active: locationData.active !== undefined ? String(locationData.active) : undefined\n        };\n        const response = await api.put(`/locations/${id}`, formattedData);\n        return response.data;\n    },\n    delete: async (id)=>{\n        const response = await api.delete(`/locations/${id}`);\n        return response.data;\n    }\n};\n// FAQ API\nconst faqApi = {\n    getAll: async ()=>{\n        const response = await api.get('/faqs');\n        return response.data;\n    },\n    getAllAdmin: async ()=>{\n        const response = await api.get('/admin/faqs');\n        return response.data;\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/admin/faqs/${id}`);\n        return response.data;\n    },\n    create: async (faqData)=>{\n        const response = await api.post('/admin/faqs', faqData);\n        return response.data;\n    },\n    update: async (id, faqData, isPartialUpdate = false)=>{\n        let sanitizedData;\n        if (isPartialUpdate) {\n            // For partial updates, only include the isActive field\n            sanitizedData = {\n                isActive: faqData.isActive === undefined ? true : Boolean(faqData.isActive)\n            };\n        } else {\n            // For full updates, include all fields with proper formatting\n            sanitizedData = {\n                question: faqData.question?.trim(),\n                answer: faqData.answer?.trim(),\n                category: faqData.category?.trim() || 'General',\n                order: typeof faqData.order === 'number' ? faqData.order : 0,\n                isActive: faqData.isActive === undefined ? true : Boolean(faqData.isActive)\n            };\n        }\n        try {\n            const response = await api.put(`/admin/faqs/${id}`, sanitizedData);\n            return response.data;\n        } catch (error) {\n            console.error('FAQ update error:', error);\n            throw error;\n        }\n    },\n    delete: async (id)=>{\n        const response = await api.delete(`/admin/faqs/${id}`);\n        return response.data;\n    }\n};\n// News API\nconst newsApi = {\n    getAll: async (page = 1, limit = 10)=>{\n        const response = await api.get(`/news?page=${page}&limit=${limit}&includeAttachments=true`);\n        return response.data;\n    },\n    getBySlug: async (slug)=>{\n        const response = await api.get(`/news/${slug}?includeAttachments=true`);\n        return response.data.news;\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/admin/news/${id}`);\n        return response.data.news;\n    },\n    create: async (newsData)=>{\n        const response = await api.post('/admin/news', newsData);\n        return response.data.news;\n    },\n    update: async (id, newsData)=>{\n        const response = await api.put(`/admin/news/${id}`, newsData);\n        return response.data.news;\n    },\n    delete: async (id)=>{\n        const response = await api.delete(`/admin/news/${id}`);\n        return response.data;\n    }\n};\n// Gallery API\nconst galleryApi = {\n    getAllAlbums: async ()=>{\n        const response = await api.get('/gallery/albums');\n        return response.data;\n    },\n    getAlbumBySlug: async (slug)=>{\n        try {\n            const response = await api.get(`/gallery/albums/${slug}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching album by slug:', error);\n            throw error;\n        }\n    },\n    getAlbumById: async (id)=>{\n        try {\n            const response = await api.get(`/admin/gallery/albums/${id}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching album by ID:', error);\n            throw error;\n        }\n    },\n    createAlbum: async (albumData)=>{\n        const response = await api.post('/admin/gallery/albums', albumData);\n        return response.data.album;\n    },\n    updateAlbum: async (id, albumData)=>{\n        const response = await api.put(`/admin/gallery/albums/${id}`, albumData);\n        return response.data.album;\n    },\n    deleteAlbum: async (id)=>{\n        await api.delete(`/admin/gallery/albums/${id}`);\n    },\n    uploadImage: async (albumId, formData)=>{\n        // Ensure CSRF token is included\n        const csrfToken = document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content');\n        if (csrfToken) {\n            formData.append('_csrf', csrfToken);\n        }\n        const response = await api.post(`/admin/gallery/albums/${albumId}/images`, formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return response.data;\n    },\n    deleteImage: async (imageId)=>{\n        await api.delete(`/admin/gallery/images/${imageId}`);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   logInfo: () => (/* binding */ logInfo),\n/* harmony export */   logWarning: () => (/* binding */ logWarning),\n/* harmony export */   parseNewsContent: () => (/* binding */ parseNewsContent),\n/* harmony export */   sanitizeData: () => (/* binding */ sanitizeData)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Parse and format tags in news content\n * Converts text in the format \"**Tags:** tag1, tag2, tag3\" to HTML tags\n * @param content - The HTML content to parse\n * @returns Formatted HTML content with tags\n */ function parseNewsContent(content) {\n    // Regular expression to match tag format: **Tags:** tag1, tag2, tag3\n    const tagRegex = /\\*\\*Tags:\\*\\*\\s*([^<]+)(?=<\\/p>|$)/g;\n    // Replace matched tag sections with formatted tags\n    return content.replace(tagRegex, (match, tagList)=>{\n        // Split the tag list by commas and trim whitespace\n        const tags = tagList.split(',').map((tag)=>tag.trim()).filter(Boolean);\n        if (tags.length === 0) {\n            return match; // No valid tags found, return original text\n        }\n        // Create HTML for tags\n        const tagsHtml = tags.map((tag)=>`<span class=\"inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2\">${tag}</span>`).join('');\n        return `<div class=\"mt-4\"><span class=\"font-semibold\">Tags:</span> <div class=\"flex flex-wrap mt-1\">${tagsHtml}</div></div>`;\n    });\n}\n/**\n * Sanitize sensitive data before logging\n * Removes passwords, tokens, etc.\n */ const sanitizeData = (data)=>{\n    if (!data || typeof data !== 'object') return data;\n    // Create a shallow copy to avoid modifying the original\n    const sanitized = {\n        ...data\n    };\n    // List of sensitive fields to mask\n    const sensitiveFields = [\n        'password',\n        'passwordHash',\n        'token',\n        'secret',\n        'apiKey',\n        'authorization',\n        'accessToken',\n        'refreshToken',\n        'csrf',\n        'cookie',\n        'session',\n        'key',\n        'credential',\n        'auth'\n    ];\n    // Mask sensitive fields\n    Object.keys(sanitized).forEach((key)=>{\n        const lowerKey = key.toLowerCase();\n        if (sensitiveFields.some((field)=>lowerKey.includes(field))) {\n            sanitized[key] = '[REDACTED]';\n        } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {\n            // Recursively sanitize nested objects\n            sanitized[key] = sanitizeData(sanitized[key]);\n        }\n    });\n    return sanitized;\n};\n/**\n * Log information to the console in development mode\n */ const logInfo = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.log(message, sanitizeData(data));\n        } else {\n            console.log(message);\n        }\n    }\n};\n/**\n * Log errors to the console\n * Always logs in production, but sanitizes sensitive data\n */ const logError = (message, error)=>{\n    if (error instanceof Error) {\n        // For Error objects, log the message and stack\n        console.error(message, {\n            message: error.message,\n            stack: error.stack\n        });\n    } else if (error) {\n        // For other data, sanitize before logging\n        console.error(message, sanitizeData(error));\n    } else {\n        console.error(message);\n    }\n};\n/**\n * Log warnings to the console in development mode\n */ const logWarning = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.warn(message, sanitizeData(data));\n        } else {\n            console.warn(message);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/@tanstack","vendor-chunks/lucide-react","vendor-chunks/web-vitals","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fabout%2Fpage&page=%2Fabout%2Fpage&appPaths=%2Fabout%2Fpage&pagePath=private-next-app-dir%2Fabout%2Fpage.tsx&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();