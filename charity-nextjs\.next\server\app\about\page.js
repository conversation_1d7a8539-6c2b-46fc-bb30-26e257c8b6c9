(()=>{var e={};e.id=220,e.ids=[220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3820:(e,t,a)=>{"use strict";a.d(t,{AboutPageContent:()=>w});var s=a(60687);a(43210);var r=a(85814),i=a.n(r),n=a(44493),o=a(51423),l=a(62185),c=a(4780);function d({className:e,...t}){return(0,s.jsx)("div",{className:(0,c.cn)("animate-pulse rounded-md bg-muted",e),...t})}var m=a(62688);let u=(0,m.A)("circle-user",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}],["path",{d:"M7 20.662V19a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v1.662",key:"154egf"}]]);var h=a(41550),p=a(48340);let x=(0,m.A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),y=(0,m.A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),g=(0,m.A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);var v=a(70216);function f(){let{data:e,isLoading:t,error:a}=(0,o.I)({queryKey:["team-members-public"],queryFn:()=>l.YV.getAll({active:!0}),staleTime:3e5});if(t)return(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[void 0,void 0,void 0].map((e,t)=>(0,s.jsxs)(n.Zp,{className:"overflow-hidden",children:[(0,s.jsx)("div",{className:"aspect-square relative",children:(0,s.jsx)(d,{className:"h-full w-full absolute"})}),(0,s.jsxs)(n.Wu,{className:"p-4",children:[(0,s.jsx)(d,{className:"h-6 w-3/4 mb-2"}),(0,s.jsx)(d,{className:"h-4 w-1/2 mb-4"}),(0,s.jsx)(d,{className:"h-20 w-full"})]})]},t))});if(a)return(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,s.jsx)("p",{children:"Unable to load team members. Please try again later."})});let r=e?.teamMembers||[];return 0===r.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,s.jsx)("p",{children:"No team members to display."})}):(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:r.map(e=>(0,s.jsx)(b,{member:e},e._id))})}function b({member:e}){return(0,s.jsxs)(n.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow duration-300",children:[(0,s.jsx)("div",{className:"aspect-square relative bg-gray-100",children:e.photo?(0,s.jsx)("img",{src:`${v.A.baseURL}${e.photo}`,alt:e.name,className:"w-full h-full object-cover"}):(0,s.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,s.jsx)(u,{className:"h-24 w-24 text-gray-300"})})}),(0,s.jsxs)(n.Wu,{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-900",children:e.name}),(0,s.jsx)("p",{className:"text-sm font-medium text-teal-600 mb-3",children:e.position}),(0,s.jsx)("p",{className:"text-gray-600 text-sm mb-4 line-clamp-4",children:e.bio}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-3 mt-4",children:[e.email&&(0,s.jsx)("a",{href:`mailto:${e.email}`,className:"text-gray-500 hover:text-teal-600 transition-colors","aria-label":`Email ${e.name}`,children:(0,s.jsx)(h.A,{className:"h-5 w-5"})}),e.phone&&(0,s.jsx)("a",{href:`tel:${e.phone}`,className:"text-gray-500 hover:text-teal-600 transition-colors","aria-label":`Call ${e.name}`,children:(0,s.jsx)(p.A,{className:"h-5 w-5"})}),e.socialLinks?.linkedin&&(0,s.jsx)("a",{href:e.socialLinks.linkedin,target:"_blank",rel:"noopener noreferrer",className:"text-gray-500 hover:text-blue-600 transition-colors","aria-label":`${e.name}'s LinkedIn profile`,children:(0,s.jsx)(x,{className:"h-5 w-5"})}),e.socialLinks?.twitter&&(0,s.jsx)("a",{href:e.socialLinks.twitter,target:"_blank",rel:"noopener noreferrer",className:"text-gray-500 hover:text-blue-400 transition-colors","aria-label":`${e.name}'s Twitter profile`,children:(0,s.jsx)(y,{className:"h-5 w-5"})}),e.socialLinks?.facebook&&(0,s.jsx)("a",{href:e.socialLinks.facebook,target:"_blank",rel:"noopener noreferrer",className:"text-gray-500 hover:text-blue-800 transition-colors","aria-label":`${e.name}'s Facebook profile`,children:(0,s.jsx)(g,{className:"h-5 w-5"})})]})]})]})}function w(){let{data:e,isLoading:t}=(0,o.I)({queryKey:["aboutContent"],queryFn:l.EO.getContent,staleTime:3e5}),a=e?.aboutContent||{mission:"Our mission is to provide support and resources to those in need within our community.",vision:"We believe in creating a welcoming environment where everyone can find the help they need, regardless of their background or circumstances.",foundedYear:"2010",volunteersCount:"50",peopleHelpedCount:"10,000",communitiesCount:"5"};return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold mb-6",children:["About ","Charity Welcome Hub"]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[(0,s.jsx)(n.Zp,{className:"col-span-1 md:col-span-2",children:(0,s.jsxs)(n.Wu,{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-charity-primary",children:"Our Mission"}),t?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(d,{className:"h-4 w-full"}),(0,s.jsx)(d,{className:"h-4 w-full"}),(0,s.jsx)(d,{className:"h-4 w-3/4"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:a.mission}),(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:a.vision})]})]})}),(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"p-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-4 text-charity-primary",children:"Quick Facts"}),(0,s.jsxs)("ul",{className:"space-y-3",children:[(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("polyline",{points:"20 6 9 17 4 12"})})}),(0,s.jsxs)("span",{children:["Founded in ",a.foundedYear||"2010"]})]}),(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("polyline",{points:"20 6 9 17 4 12"})})}),(0,s.jsxs)("span",{children:["Over ",a.volunteersCount||"50"," dedicated volunteers"]})]}),(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("polyline",{points:"20 6 9 17 4 12"})})}),(0,s.jsxs)("span",{children:["Helped over ",a.peopleHelpedCount||"10,000"," individuals"]})]}),(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"bg-charity-primary/10 text-charity-primary rounded-full p-1 mr-2 mt-0.5",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:(0,s.jsx)("polyline",{points:"20 6 9 17 4 12"})})}),(0,s.jsxs)("span",{children:["Active in ",a.communitiesCount||"5"," local communities"]})]})]})]})})]}),(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-6 text-charity-primary",children:"Our Values"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"bg-charity-primary/10 p-4 rounded-full mb-4",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-charity-primary",children:[(0,s.jsx)("path",{d:"M18 8a6 6 0 0 0-6-6 6 6 0 0 0-6 6c0 7 6 13 6 13s6-6 6-13z"}),(0,s.jsx)("circle",{cx:"12",cy:"8",r:"2"})]})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Compassion"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We approach every individual with empathy and understanding, recognizing their unique circumstances and needs."})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"bg-charity-primary/10 p-4 rounded-full mb-4",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-charity-primary",children:[(0,s.jsx)("path",{d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"}),(0,s.jsx)("path",{d:"m7 9 3 3-3 3"}),(0,s.jsx)("path",{d:"M14 9h3v6h-3"})]})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Integrity"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We maintain the highest standards of honesty and transparency in all our operations and relationships."})]})})}),(0,s.jsx)(n.Zp,{children:(0,s.jsx)(n.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"bg-charity-primary/10 p-4 rounded-full mb-4",children:(0,s.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-charity-primary",children:[(0,s.jsx)("path",{d:"M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"}),(0,s.jsx)("circle",{cx:"9",cy:"7",r:"4"}),(0,s.jsx)("path",{d:"M23 21v-2a4 4 0 0 0-3-3.87"}),(0,s.jsx)("path",{d:"M16 3.13a4 4 0 0 1 0 7.75"})]})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2",children:"Community"}),(0,s.jsx)("p",{className:"text-gray-600",children:"We believe in the power of community and work together to create positive change and lasting impact."})]})})})]})]}),(0,s.jsxs)("div",{className:"mb-12",children:[(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-6 text-charity-primary",children:"Get Involved"}),(0,s.jsx)(n.Zp,{children:(0,s.jsxs)(n.Wu,{className:"p-6",children:[(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:"There are many ways to support our mission and make a difference in our community. Whether you're interested in volunteering your time, making a donation, or partnering with us, we welcome your involvement."}),(0,s.jsx)("div",{className:"flex flex-wrap gap-4 justify-center md:justify-start",children:(0,s.jsx)(i(),{href:"/contact",className:"bg-white border border-charity-primary text-charity-primary hover:bg-charity-primary/10 font-medium py-2 px-6 rounded-md transition-colors duration-200",children:"Contact Us"})})]})})]}),(0,s.jsx)("div",{className:"py-16 bg-gray-50 -mx-4",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Our Team"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"Meet the dedicated individuals who make our mission possible."})]}),(0,s.jsx)(f,{})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28415:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n,metadata:()=>i});var s=a(37413),r=a(54787);let i={title:"About Us - Charity Welcome Hub",description:"Learn about our mission, vision, values, and the dedicated team working to make a positive impact in our community.",keywords:["charity","about us","mission","vision","team","community","volunteers"],openGraph:{title:"About Us - Charity Welcome Hub",description:"Learn about our mission, vision, values, and the dedicated team working to make a positive impact in our community.",type:"website"},twitter:{card:"summary_large_image",title:"About Us - Charity Welcome Hub",description:"Learn about our mission, vision, values, and the dedicated team working to make a positive impact in our community."}};function n(){return(0,s.jsx)(r.AboutPageContent,{})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,a)=>{"use strict";a.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>m});var s=a(60687),r=a(43210),i=a(4780);let n=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let o=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent";let m=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));m.displayName="CardFooter"},45069:(e,t,a)=>{Promise.resolve().then(a.bind(a,54787))},54787:(e,t,a)=>{"use strict";a.d(t,{AboutPageContent:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call AboutPageContent() from the server but AboutPageContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\about\\AboutPageContent.tsx","AboutPageContent")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62185:(e,t,a)=>{"use strict";a.d(t,{AY:()=>m,EO:()=>n,TP:()=>l,YV:()=>o,jE:()=>u,l4:()=>d,lM:()=>c});var s=a(51060),r=a(70216);let i=s.A.create({baseURL:r.A.baseURL,timeout:r.A.timeout,withCredentials:r.A.withCredentials});i.interceptors.request.use(async e=>e,e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response?.status,Promise.reject(e)));let n={getContent:async()=>(await i.get("/about")).data,updateContent:async e=>(await i.post("/about",e)).data},o={getAll:async e=>({teamMembers:(await i.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await i.get(`/team/${e}`)).data}),create:async e=>(await i.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,t)=>(await i.put(`/team/${e}`,t,{headers:{"Content-Type":"multipart/form-data"}})).data,patch:async(e,t)=>(await i.patch(`/team/${e}`,t)).data,delete:async e=>(await i.delete(`/team/${e}`)).data},l={submit:async e=>(await i.post("/contact",e)).data,getAll:async(e=1,t=10,a)=>{let s={page:e,limit:t,...a?{status:a}:{}};return(await i.get("/contact",{params:s})).data},getById:async e=>(await i.get(`/contact/${e}`)).data,updateStatus:async(e,t)=>(await i.put(`/contact/${e}/status`,{status:t})).data,delete:async e=>(await i.delete(`/contact/${e}`)).data},c={getAll:async e=>({locations:(await i.get("/locations",{params:e})).data}),getById:async e=>({location:(await i.get(`/locations/${e}`)).data}),create:async e=>{let t={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await i.post("/locations",t)).data},update:async(e,t)=>{let a={...t,isMainOffice:void 0!==t.isMainOffice?String(t.isMainOffice):void 0,active:void 0!==t.active?String(t.active):void 0};return(await i.put(`/locations/${e}`,a)).data},delete:async e=>(await i.delete(`/locations/${e}`)).data},d={getAll:async()=>(await i.get("/faqs")).data,getAllAdmin:async()=>(await i.get("/admin/faqs")).data,getById:async e=>(await i.get(`/admin/faqs/${e}`)).data,create:async e=>(await i.post("/admin/faqs",e)).data,update:async(e,t,a=!1)=>{let s;s=a?{isActive:void 0===t.isActive||!!t.isActive}:{question:t.question?.trim(),answer:t.answer?.trim(),category:t.category?.trim()||"General",order:"number"==typeof t.order?t.order:0,isActive:void 0===t.isActive||!!t.isActive};try{return(await i.put(`/admin/faqs/${e}`,s)).data}catch(e){throw e}},delete:async e=>(await i.delete(`/admin/faqs/${e}`)).data},m={getAll:async(e=1,t=10)=>(await i.get(`/news?page=${e}&limit=${t}&includeAttachments=true`)).data,getBySlug:async e=>(await i.get(`/news/${e}?includeAttachments=true`)).data.news,getById:async e=>(await i.get(`/admin/news/${e}`)).data.news,create:async e=>(await i.post("/admin/news",e)).data.news,update:async(e,t)=>(await i.put(`/admin/news/${e}`,t)).data.news,delete:async e=>(await i.delete(`/admin/news/${e}`)).data},u={getAllAlbums:async()=>(await i.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await i.get(`/gallery/albums/${e}`)).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await i.get(`/admin/gallery/albums/${e}`)).data}catch(e){throw e}},createAlbum:async e=>(await i.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,t)=>(await i.put(`/admin/gallery/albums/${e}`,t)).data.album,deleteAlbum:async e=>{await i.delete(`/admin/gallery/albums/${e}`)},uploadImage:async(e,t)=>{let a=document.querySelector('meta[name="csrf-token"]')?.getAttribute("content");return a&&t.append("_csrf",a),(await i.post(`/admin/gallery/albums/${e}/images`,t,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await i.delete(`/admin/gallery/images/${e}`)}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70216:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},88193:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=a(65239),r=a(48088),i=a(88170),n=a.n(i),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let c={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,28415)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\about\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,68178)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,99766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\about\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},92333:(e,t,a)=>{Promise.resolve().then(a.bind(a,3820))},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[447,699,62,775],()=>a(88193));module.exports=s})();