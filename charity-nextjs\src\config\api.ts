// API configuration
const API_CONFIG = {
  baseURL: process.env.NODE_ENV === 'production'
    ? '/api' // In production, use relative path to Next.js API routes
    : '/api', // In development, use Next.js API routes
  backendURL: process.env.NODE_ENV === 'production'
    ? process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5000'
    : 'http://localhost:5000', // Direct backend URL for uploads/images
  timeout: 15000, // Increased timeout for slower connections
  withCredentials: true,
  headers: {
    'Cache-Control': 'max-age=300', // Cache responses for 5 minutes
    'Content-Type': 'application/json',
  },
};

export default API_CONFIG;
