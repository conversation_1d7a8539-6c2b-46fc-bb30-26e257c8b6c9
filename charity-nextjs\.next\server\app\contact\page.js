(()=>{var e={};e.id=977,e.ids=[977],e.modules={1226:(e,t,r)=>{Promise.resolve().then(r.bind(r,31051))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7710:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,metadata:()=>n});var i=r(37413),a=r(31051);let n={title:"Contact Us - Charity Welcome Hub",description:"Get in touch with us. Send us a message, find our location, and learn how to get involved with our charitable work.",keywords:["contact","charity","get in touch","location","volunteer","support"],openGraph:{title:"Contact Us - Charity Welcome Hub",description:"Get in touch with us. Send us a message, find our location, and learn how to get involved with our charitable work.",type:"website"},twitter:{card:"summary_large_image",title:"Contact Us - Charity Welcome Hub",description:"Get in touch with us. Send us a message, find our location, and learn how to get involved with our charitable work."}};function s(){return(0,i.jsx)(a.ContactPageContent,{})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14163:(e,t,r)=>{"use strict";r.d(t,{sG:()=>s});var i=r(43210);r(51215);var a=r(8730),n=r(60687),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),s=i.forwardRef((e,i)=>{let{asChild:a,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:t,{...s,ref:i})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31051:(e,t,r)=>{"use strict";r.d(t,{ContactPageContent:()=>a});var i=r(12907);let a=(0,i.registerClientReference)(function(){throw Error("Attempted to call ContactPageContent() from the server but ContactPageContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\contact\\ContactPageContent.tsx","ContactPageContent");(0,i.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\contact\\\\ContactPageContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\contact\\ContactPageContent.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},40978:(e,t,r)=>{Promise.resolve().then(r.bind(r,95491))},43125:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>u,Wu:()=>d,ZB:()=>l,Zp:()=>s,aR:()=>o,wL:()=>c});var i=r(60687),a=r(43210),n=r(4780);let s=a.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));s.displayName="Card";let o=a.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>(0,i.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let u=a.forwardRef(({className:e,...t},r)=>(0,i.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));u.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent";let c=a.forwardRef(({className:e,...t},r)=>(0,i.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t}));c.displayName="CardFooter"},51683:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>f,tree:()=>u});var i=r(65239),a=r(48088),n=r(88170),s=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let u={children:["",{children:["contact",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,7710)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\contact\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,68178)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,99766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\contact\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},f=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/contact/page",pathname:"/contact",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62185:(e,t,r)=>{"use strict";r.d(t,{AY:()=>c,EO:()=>s,TP:()=>l,YV:()=>o,jE:()=>f,l4:()=>d,lM:()=>u});var i=r(51060),a=r(70216);let n=i.A.create({baseURL:a.A.baseURL,timeout:a.A.timeout,withCredentials:a.A.withCredentials});n.interceptors.request.use(async e=>e,e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>(e.response?.status,Promise.reject(e)));let s={getContent:async()=>(await n.get("/about")).data,updateContent:async e=>(await n.post("/about",e)).data},o={getAll:async e=>({teamMembers:(await n.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await n.get(`/team/${e}`)).data}),create:async e=>(await n.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,t)=>(await n.put(`/team/${e}`,t,{headers:{"Content-Type":"multipart/form-data"}})).data,patch:async(e,t)=>(await n.patch(`/team/${e}`,t)).data,delete:async e=>(await n.delete(`/team/${e}`)).data},l={submit:async e=>(await n.post("/contact",e)).data,getAll:async(e=1,t=10,r)=>{let i={page:e,limit:t,...r?{status:r}:{}};return(await n.get("/contact",{params:i})).data},getById:async e=>(await n.get(`/contact/${e}`)).data,updateStatus:async(e,t)=>(await n.put(`/contact/${e}/status`,{status:t})).data,delete:async e=>(await n.delete(`/contact/${e}`)).data},u={getAll:async e=>({locations:(await n.get("/locations",{params:e})).data}),getById:async e=>({location:(await n.get(`/locations/${e}`)).data}),create:async e=>{let t={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await n.post("/locations",t)).data},update:async(e,t)=>{let r={...t,isMainOffice:void 0!==t.isMainOffice?String(t.isMainOffice):void 0,active:void 0!==t.active?String(t.active):void 0};return(await n.put(`/locations/${e}`,r)).data},delete:async e=>(await n.delete(`/locations/${e}`)).data},d={getAll:async()=>(await n.get("/faqs")).data,getAllAdmin:async()=>(await n.get("/admin/faqs")).data,getById:async e=>(await n.get(`/admin/faqs/${e}`)).data,create:async e=>(await n.post("/admin/faqs",e)).data,update:async(e,t,r=!1)=>{let i;i=r?{isActive:void 0===t.isActive||!!t.isActive}:{question:t.question?.trim(),answer:t.answer?.trim(),category:t.category?.trim()||"General",order:"number"==typeof t.order?t.order:0,isActive:void 0===t.isActive||!!t.isActive};try{return(await n.put(`/admin/faqs/${e}`,i)).data}catch(e){throw e}},delete:async e=>(await n.delete(`/admin/faqs/${e}`)).data},c={getAll:async(e=1,t=10)=>(await n.get(`/news?page=${e}&limit=${t}&includeAttachments=true`)).data,getBySlug:async e=>(await n.get(`/news/${e}?includeAttachments=true`)).data.news,getById:async e=>(await n.get(`/admin/news/${e}`)).data.news,create:async e=>(await n.post("/admin/news",e)).data.news,update:async(e,t)=>(await n.put(`/admin/news/${e}`,t)).data.news,delete:async e=>(await n.delete(`/admin/news/${e}`)).data},f={getAllAlbums:async()=>(await n.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await n.get(`/gallery/albums/${e}`)).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await n.get(`/admin/gallery/albums/${e}`)).data}catch(e){throw e}},createAlbum:async e=>(await n.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,t)=>(await n.put(`/admin/gallery/albums/${e}`,t)).data.album,deleteAlbum:async e=>{await n.delete(`/admin/gallery/albums/${e}`)},uploadImage:async(e,t)=>{let r=document.querySelector('meta[name="csrf-token"]')?.getAttribute("content");return r&&t.append("_csrf",r),(await n.post(`/admin/gallery/albums/${e}/images`,t,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await n.delete(`/admin/gallery/images/${e}`)}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70216:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94735:e=>{"use strict";e.exports=require("events")},95491:(e,t,r)=>{"use strict";r.d(t,{ContactPageContent:()=>i2});var i,a=r(60687),n=r(43210),s=r.n(n),o=e=>"checkbox"===e.type,l=e=>e instanceof Date,u=e=>null==e;let d=e=>"object"==typeof e;var c=e=>!u(e)&&!Array.isArray(e)&&d(e)&&!l(e),f=e=>c(e)&&e.target?o(e.target)?e.target.checked:e.target.value:e,p=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,m=(e,t)=>e.has(p(t)),h=e=>{let t=e.constructor&&e.constructor.prototype;return c(t)&&t.hasOwnProperty("isPrototypeOf")},y="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function v(e){let t,r=Array.isArray(e),i="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(!(!(y&&(e instanceof Blob||i))&&(r||c(e))))return e;else if(t=r?[]:{},r||h(e))for(let r in e)e.hasOwnProperty(r)&&(t[r]=v(e[r]));else t=e;return t}var g=e=>/^\w*$/.test(e),_=e=>void 0===e,b=e=>Array.isArray(e)?e.filter(Boolean):[],w=e=>b(e.replace(/["|']|\]/g,"").split(/\.|\[/)),x=(e,t,r)=>{if(!t||!c(e))return r;let i=(g(t)?[t]:w(t)).reduce((e,t)=>u(e)?e:e[t],e);return _(i)||i===e?_(e[t])?r:e[t]:i},k=e=>"boolean"==typeof e,z=(e,t,r)=>{let i=-1,a=g(t)?[t]:w(t),n=a.length,s=n-1;for(;++i<n;){let t=a[i],n=r;if(i!==s){let r=e[t];n=c(r)||Array.isArray(r)?r:isNaN(+a[i+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=n,e=e[t]}};let j={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},A={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},$={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},S=n.createContext(null);S.displayName="HookFormContext";let C=()=>n.useContext(S);var F=(e,t,r,i=!0)=>{let a={defaultValues:t._defaultValues};for(let n in e)Object.defineProperty(a,n,{get:()=>(t._proxyFormState[n]!==A.all&&(t._proxyFormState[n]=!i||A.all),r&&(r[n]=!0),e[n])});return a};let E="undefined"!=typeof window?n.useLayoutEffect:n.useEffect;var V=e=>"string"==typeof e,P=(e,t,r,i,a)=>V(e)?(i&&t.watch.add(e),x(r,e,a)):Array.isArray(e)?e.map(e=>(i&&t.watch.add(e),x(r,e))):(i&&(t.watchAll=!0),r);let Z=e=>e.render(function(e){let t=C(),{name:r,disabled:i,control:a=t.control,shouldUnregister:s}=e,o=m(a._names.array,r),l=function(e){let t=C(),{control:r=t.control,name:i,defaultValue:a,disabled:s,exact:o}=e||{},l=n.useRef(a),[u,d]=n.useState(r._getWatch(i,l.current));return E(()=>r._subscribe({name:i,formState:{values:!0},exact:o,callback:e=>!s&&d(P(i,r._names,e.values||r._formValues,!1,l.current))}),[i,r,s,o]),n.useEffect(()=>r._removeUnmounted()),u}({control:a,name:r,defaultValue:x(a._formValues,r,x(a._defaultValues,r,e.defaultValue)),exact:!0}),u=function(e){let t=C(),{control:r=t.control,disabled:i,name:a,exact:s}=e||{},[o,l]=n.useState(r._formState),u=n.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1});return E(()=>r._subscribe({name:a,formState:u.current,exact:s,callback:e=>{i||l({...r._formState,...e})}}),[a,i,s]),n.useEffect(()=>{u.current.isValid&&r._setValid(!0)},[r]),n.useMemo(()=>F(o,r,u.current,!1),[o,r])}({control:a,name:r,exact:!0}),d=n.useRef(e),c=n.useRef(a.register(r,{...e.rules,value:l,...k(e.disabled)?{disabled:e.disabled}:{}})),p=n.useMemo(()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!x(u.errors,r)},isDirty:{enumerable:!0,get:()=>!!x(u.dirtyFields,r)},isTouched:{enumerable:!0,get:()=>!!x(u.touchedFields,r)},isValidating:{enumerable:!0,get:()=>!!x(u.validatingFields,r)},error:{enumerable:!0,get:()=>x(u.errors,r)}}),[u,r]),h=n.useCallback(e=>c.current.onChange({target:{value:f(e),name:r},type:j.CHANGE}),[r]),y=n.useCallback(()=>c.current.onBlur({target:{value:x(a._formValues,r),name:r},type:j.BLUR}),[r,a._formValues]),g=n.useCallback(e=>{let t=x(a._fields,r);t&&e&&(t._f.ref={focus:()=>e.focus&&e.focus(),select:()=>e.select&&e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})},[a._fields,r]),b=n.useMemo(()=>({name:r,value:l,...k(i)||u.disabled?{disabled:u.disabled||i}:{},onChange:h,onBlur:y,ref:g}),[r,i,u.disabled,h,y,g,l]);return n.useEffect(()=>{let e=a._options.shouldUnregister||s;a.register(r,{...d.current.rules,...k(d.current.disabled)?{disabled:d.current.disabled}:{}});let t=(e,t)=>{let r=x(a._fields,e);r&&r._f&&(r._f.mount=t)};if(t(r,!0),e){let e=v(x(a._options.defaultValues,r));z(a._defaultValues,r,e),_(x(a._formValues,r))&&z(a._formValues,r,e)}return o||a.register(r),()=>{(o?e&&!a._state.action:e)?a.unregister(r):t(r,!1)}},[r,a,o,s]),n.useEffect(()=>{a._setDisabledField({disabled:i,name:r})},[i,r,a]),n.useMemo(()=>({field:b,formState:u,fieldState:p}),[b,u,p])}(e));var N=(e,t,r,i,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[i]:a||!0}}:{},D=e=>Array.isArray(e)?e:[e],O=()=>{let e=[];return{get observers(){return e},next:t=>{for(let r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},T=e=>u(e)||!d(e);function I(e,t,r=new WeakSet){if(T(e)||T(t))return e===t;if(l(e)&&l(t))return e.getTime()===t.getTime();let i=Object.keys(e),a=Object.keys(t);if(i.length!==a.length)return!1;if(r.has(e)||r.has(t))return!0;for(let n of(r.add(e),r.add(t),i)){let i=e[n];if(!a.includes(n))return!1;if("ref"!==n){let e=t[n];if(l(i)&&l(e)||c(i)&&c(e)||Array.isArray(i)&&Array.isArray(e)?!I(i,e,r):i!==e)return!1}}return!0}var R=e=>c(e)&&!Object.keys(e).length,U=e=>"file"===e.type,M=e=>"function"==typeof e,L=e=>{if(!y)return!1;let t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},q=e=>"select-multiple"===e.type,B=e=>"radio"===e.type,W=e=>B(e)||o(e),G=e=>L(e)&&e.isConnected;function H(e,t){let r=Array.isArray(t)?t:g(t)?[t]:w(t),i=1===r.length?e:function(e,t){let r=t.slice(0,-1).length,i=0;for(;i<r;)e=_(e)?i++:e[t[i++]];return e}(e,r),a=r.length-1,n=r[a];return i&&delete i[n],0!==a&&(c(i)&&R(i)||Array.isArray(i)&&function(e){for(let t in e)if(e.hasOwnProperty(t)&&!_(e[t]))return!1;return!0}(i))&&H(e,r.slice(0,-1)),e}var K=e=>{for(let t in e)if(M(e[t]))return!0;return!1};function J(e,t={}){let r=Array.isArray(e);if(c(e)||r)for(let r in e)Array.isArray(e[r])||c(e[r])&&!K(e[r])?(t[r]=Array.isArray(e[r])?[]:{},J(e[r],t[r])):u(e[r])||(t[r]=!0);return t}var X=(e,t)=>(function e(t,r,i){let a=Array.isArray(t);if(c(t)||a)for(let a in t)Array.isArray(t[a])||c(t[a])&&!K(t[a])?_(r)||T(i[a])?i[a]=Array.isArray(t[a])?J(t[a],[]):{...J(t[a])}:e(t[a],u(r)?{}:r[a],i[a]):i[a]=!I(t[a],r[a]);return i})(e,t,J(t));let Y={value:!1,isValid:!1},Q={value:!0,isValid:!0};var ee=e=>{if(Array.isArray(e)){if(e.length>1){let t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!_(e[0].attributes.value)?_(e[0].value)||""===e[0].value?Q:{value:e[0].value,isValid:!0}:Q:Y}return Y},et=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:i})=>_(e)?e:t?""===e?NaN:e?+e:e:r&&V(e)?new Date(e):i?i(e):e;let er={isValid:!1,value:null};var ei=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,er):er;function ea(e){let t=e.ref;return U(t)?t.files:B(t)?ei(e.refs).value:q(t)?[...t.selectedOptions].map(({value:e})=>e):o(t)?ee(e.refs).value:et(_(t.value)?e.ref.value:t.value,e)}var en=(e,t,r,i)=>{let a={};for(let r of e){let e=x(t,r);e&&z(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:i}},es=e=>e instanceof RegExp,eo=e=>_(e)?e:es(e)?e.source:c(e)?es(e.value)?e.value.source:e.value:e,el=e=>({isOnSubmit:!e||e===A.onSubmit,isOnBlur:e===A.onBlur,isOnChange:e===A.onChange,isOnAll:e===A.all,isOnTouch:e===A.onTouched});let eu="AsyncFunction";var ed=e=>!!e&&!!e.validate&&!!(M(e.validate)&&e.validate.constructor.name===eu||c(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===eu)),ec=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),ef=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));let ep=(e,t,r,i)=>{for(let a of r||Object.keys(e)){let r=x(e,a);if(r){let{_f:e,...n}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!i)return!0;else if(e.ref&&t(e.ref,e.name)&&!i)return!0;else if(ep(n,t))break}else if(c(n)&&ep(n,t))break}}};function em(e,t,r){let i=x(e,r);if(i||g(r))return{error:i,name:r};let a=r.split(".");for(;a.length;){let i=a.join("."),n=x(t,i),s=x(e,i);if(n&&!Array.isArray(n)&&r!==i)break;if(s&&s.type)return{name:i,error:s};if(s&&s.root&&s.root.type)return{name:`${i}.root`,error:s.root};a.pop()}return{name:r}}var eh=(e,t,r,i)=>{r(e);let{name:a,...n}=e;return R(n)||Object.keys(n).length>=Object.keys(t).length||Object.keys(n).find(e=>t[e]===(!i||A.all))},ey=(e,t,r)=>!e||!t||e===t||D(e).some(e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))),ev=(e,t,r,i,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?i.isOnBlur:a.isOnBlur)?!e:(r?!i.isOnChange:!a.isOnChange)||e),eg=(e,t)=>!b(x(e,t)).length&&H(e,t),e_=(e,t,r)=>{let i=D(x(e,r));return z(i,"root",t[r]),z(e,r,i),e},eb=e=>V(e);function ew(e,t,r="validate"){if(eb(e)||Array.isArray(e)&&e.every(eb)||k(e)&&!e)return{type:r,message:eb(e)?e:"",ref:t}}var ex=e=>c(e)&&!es(e)?e:{value:e,message:""},ek=async(e,t,r,i,a,n)=>{let{ref:s,refs:l,required:d,maxLength:f,minLength:p,min:m,max:h,pattern:y,validate:v,name:g,valueAsNumber:b,mount:w}=e._f,z=x(r,g);if(!w||t.has(g))return{};let j=l?l[0]:s,A=e=>{a&&j.reportValidity&&(j.setCustomValidity(k(e)?"":e||""),j.reportValidity())},S={},C=B(s),F=o(s),E=(b||U(s))&&_(s.value)&&_(z)||L(s)&&""===s.value||""===z||Array.isArray(z)&&!z.length,P=N.bind(null,g,i,S),Z=(e,t,r,i=$.maxLength,a=$.minLength)=>{let n=e?t:r;S[g]={type:e?i:a,message:n,ref:s,...P(e?i:a,n)}};if(n?!Array.isArray(z)||!z.length:d&&(!(C||F)&&(E||u(z))||k(z)&&!z||F&&!ee(l).isValid||C&&!ei(l).isValid)){let{value:e,message:t}=eb(d)?{value:!!d,message:d}:ex(d);if(e&&(S[g]={type:$.required,message:t,ref:j,...P($.required,t)},!i))return A(t),S}if(!E&&(!u(m)||!u(h))){let e,t,r=ex(h),a=ex(m);if(u(z)||isNaN(z)){let i=s.valueAsDate||new Date(z),n=e=>new Date(new Date().toDateString()+" "+e),o="time"==s.type,l="week"==s.type;V(r.value)&&z&&(e=o?n(z)>n(r.value):l?z>r.value:i>new Date(r.value)),V(a.value)&&z&&(t=o?n(z)<n(a.value):l?z<a.value:i<new Date(a.value))}else{let i=s.valueAsNumber||(z?+z:z);u(r.value)||(e=i>r.value),u(a.value)||(t=i<a.value)}if((e||t)&&(Z(!!e,r.message,a.message,$.max,$.min),!i))return A(S[g].message),S}if((f||p)&&!E&&(V(z)||n&&Array.isArray(z))){let e=ex(f),t=ex(p),r=!u(e.value)&&z.length>+e.value,a=!u(t.value)&&z.length<+t.value;if((r||a)&&(Z(r,e.message,t.message),!i))return A(S[g].message),S}if(y&&!E&&V(z)){let{value:e,message:t}=ex(y);if(es(e)&&!z.match(e)&&(S[g]={type:$.pattern,message:t,ref:s,...P($.pattern,t)},!i))return A(t),S}if(v){if(M(v)){let e=ew(await v(z,r),j);if(e&&(S[g]={...e,...P($.validate,e.message)},!i))return A(e.message),S}else if(c(v)){let e={};for(let t in v){if(!R(e)&&!i)break;let a=ew(await v[t](z,r),j,t);a&&(e={...a,...P(t,a.message)},A(a.message),i&&(S[g]=e))}if(!R(e)&&(S[g]={ref:j,...e},!i))return S}}return A(!0),S};let ez={mode:A.onSubmit,reValidateMode:A.onChange,shouldFocusError:!0},ej=(e,t,r)=>{if(e&&"reportValidity"in e){let i=x(r,t);e.setCustomValidity(i&&i.message||""),e.reportValidity()}},eA=(e,t)=>{for(let r in t.fields){let i=t.fields[r];i&&i.ref&&"reportValidity"in i.ref?ej(i.ref,r,e):i&&i.refs&&i.refs.forEach(t=>ej(t,r,e))}},e$=(e,t)=>{t.shouldUseNativeValidation&&eA(e,t);let r={};for(let i in e){let a=x(t.fields,i),n=Object.assign(e[i]||{},{ref:a&&a.ref});if(eS(t.names||Object.keys(e),i)){let e=Object.assign({},x(r,i));z(e,"root",n),z(r,i,e)}else z(r,i,n)}return r},eS=(e,t)=>{let r=eC(t);return e.some(e=>eC(e).match(`^${r}\\.\\d+`))};function eC(e){return e.replace(/\]|\[/g,"")}function eF(e,t,r){function i(r,i){var a;for(let n in Object.defineProperty(r,"_zod",{value:r._zod??{},enumerable:!1}),(a=r._zod).traits??(a.traits=new Set),r._zod.traits.add(e),t(r,i),s.prototype)n in r||Object.defineProperty(r,n,{value:s.prototype[n].bind(r)});r._zod.constr=s,r._zod.def=i}let a=r?.Parent??Object;class n extends a{}function s(e){var t;let a=r?.Parent?new n:this;for(let r of(i(a,e),(t=a._zod).deferred??(t.deferred=[]),a._zod.deferred))r();return a}return Object.defineProperty(n,"name",{value:e}),Object.defineProperty(s,"init",{value:i}),Object.defineProperty(s,Symbol.hasInstance,{value:t=>!!r?.Parent&&t instanceof r.Parent||t?._zod?.traits?.has(e)}),Object.defineProperty(s,"name",{value:e}),s}Object.freeze({status:"aborted"}),Symbol("zod_brand");class eE extends Error{constructor(){super("Encountered Promise during synchronous parse. Use .parseAsync() instead.")}}let eV={};function eP(e){return e&&Object.assign(eV,e),eV}function eZ(e,t){return"bigint"==typeof t?t.toString():t}function eN(e){return{get value(){{let t=e();return Object.defineProperty(this,"value",{value:t}),t}}}}function eD(e){let t=+!!e.startsWith("^"),r=e.endsWith("$")?e.length-1:e.length;return e.slice(t,r)}function eO(e,t,r){Object.defineProperty(e,t,{get(){{let i=r();return e[t]=i,i}},set(r){Object.defineProperty(e,t,{value:r})},configurable:!0})}function eT(e,t,r){Object.defineProperty(e,t,{value:r,writable:!0,enumerable:!0,configurable:!0})}function eI(e){return JSON.stringify(e)}let eR=Error.captureStackTrace?Error.captureStackTrace:(...e)=>{};function eU(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}let eM=eN(()=>{if("undefined"!=typeof navigator&&navigator?.userAgent?.includes("Cloudflare"))return!1;try{return Function(""),!0}catch(e){return!1}});function eL(e){if(!1===eU(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!1!==eU(r)&&!1!==Object.prototype.hasOwnProperty.call(r,"isPrototypeOf")}let eq=new Set(["string","number","symbol"]);function eB(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function eW(e,t,r){let i=new e._zod.constr(t??e._zod.def);return(!t||r?.parent)&&(i._zod.parent=e),i}function eG(e){if(!e)return{};if("string"==typeof e)return{error:()=>e};if(e?.message!==void 0){if(e?.error!==void 0)throw Error("Cannot specify both `message` and `error` params");e.error=e.message}return(delete e.message,"string"==typeof e.error)?{...e,error:()=>e.error}:e}function eH(e,t=0){for(let r=t;r<e.issues.length;r++)if(e.issues[r]?.continue!==!0)return!0;return!1}function eK(e,t){return t.map(t=>(t.path??(t.path=[]),t.path.unshift(e),t))}function eJ(e){return"string"==typeof e?e:e?.message}function eX(e,t,r){let i={...e,path:e.path??[]};return e.message||(i.message=eJ(e.inst?._zod.def?.error?.(e))??eJ(t?.error?.(e))??eJ(r.customError?.(e))??eJ(r.localeError?.(e))??"Invalid input"),delete i.inst,delete i.continue,t?.reportInput||delete i.input,i}function eY(e){return Array.isArray(e)?"array":"string"==typeof e?"string":"unknown"}function eQ(...e){let[t,r,i]=e;return"string"==typeof t?{message:t,code:"custom",input:r,inst:i}:{...t}}Number.MIN_SAFE_INTEGER,Number.MAX_SAFE_INTEGER,Number.MAX_VALUE,Number.MAX_VALUE;let e0=(e,t)=>{e.name="$ZodError",Object.defineProperty(e,"_zod",{value:e._zod,enumerable:!1}),Object.defineProperty(e,"issues",{value:t,enumerable:!1}),Object.defineProperty(e,"message",{get:()=>JSON.stringify(t,eZ,2),enumerable:!0}),Object.defineProperty(e,"toString",{value:()=>e.message,enumerable:!1})},e1=eF("$ZodError",e0),e2=eF("$ZodError",e0,{Parent:Error}),e9=e=>(t,r,i,a)=>{let n=i?Object.assign(i,{async:!1}):{async:!1},s=t._zod.run({value:r,issues:[]},n);if(s instanceof Promise)throw new eE;if(s.issues.length){let t=new(a?.Err??e)(s.issues.map(e=>eX(e,n,eP())));throw eR(t,a?.callee),t}return s.value},e4=e9(e2),e6=e=>async(t,r,i,a)=>{let n=i?Object.assign(i,{async:!0}):{async:!0},s=t._zod.run({value:r,issues:[]},n);if(s instanceof Promise&&(s=await s),s.issues.length){let t=new(a?.Err??e)(s.issues.map(e=>eX(e,n,eP())));throw eR(t,a?.callee),t}return s.value},e3=e6(e2),e5=e=>(t,r,i)=>{let a=i?{...i,async:!1}:{async:!1},n=t._zod.run({value:r,issues:[]},a);if(n instanceof Promise)throw new eE;return n.issues.length?{success:!1,error:new(e??e1)(n.issues.map(e=>eX(e,a,eP())))}:{success:!0,data:n.value}},e8=e5(e2),e7=e=>async(t,r,i)=>{let a=i?Object.assign(i,{async:!0}):{async:!0},n=t._zod.run({value:r,issues:[]},a);return n instanceof Promise&&(n=await n),n.issues.length?{success:!1,error:new e(n.issues.map(e=>eX(e,a,eP())))}:{success:!0,data:n.value}},te=e7(e2);function tt(e,t){try{var r=e()}catch(e){return t(e)}return r&&r.then?r.then(void 0,t):r}let tr=/^[cC][^\s-]{8,}$/,ti=/^[0-9a-z]+$/,ta=/^[0-9A-HJKMNP-TV-Za-hjkmnp-tv-z]{26}$/,tn=/^[0-9a-vA-V]{20}$/,ts=/^[A-Za-z0-9]{27}$/,to=/^[a-zA-Z0-9_-]{21}$/,tl=/^P(?:(\d+W)|(?!.*W)(?=\d|T\d)(\d+Y)?(\d+M)?(\d+D)?(T(?=\d)(\d+H)?(\d+M)?(\d+([.,]\d+)?S)?)?)$/,tu=/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})$/,td=e=>e?RegExp(`^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-${e}[0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12})$`):/^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$/,tc=/^(?!\.)(?!.*\.\.)([A-Za-z0-9_'+\-\.]*)[A-Za-z0-9_+-]@([A-Za-z0-9][A-Za-z0-9\-]*\.)+[A-Za-z]{2,}$/,tf=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,tp=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})$/,tm=/^((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/([0-9]|[1-2][0-9]|3[0-2])$/,th=/^(([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}|::|([0-9a-fA-F]{1,4})?::([0-9a-fA-F]{1,4}:?){0,6})\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,ty=/^$|^(?:[0-9a-zA-Z+/]{4})*(?:(?:[0-9a-zA-Z+/]{2}==)|(?:[0-9a-zA-Z+/]{3}=))?$/,tv=/^[A-Za-z0-9_-]*$/,tg=/^([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+$/,t_=/^\+(?:[0-9]){6,14}[0-9]$/,tb="(?:(?:\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-(?:(?:0[13578]|1[02])-(?:0[1-9]|[12]\\d|3[01])|(?:0[469]|11)-(?:0[1-9]|[12]\\d|30)|(?:02)-(?:0[1-9]|1\\d|2[0-8])))",tw=RegExp(`^${tb}$`);function tx(e){let t="(?:[01]\\d|2[0-3]):[0-5]\\d";return"number"==typeof e.precision?-1===e.precision?`${t}`:0===e.precision?`${t}:[0-5]\\d`:`${t}:[0-5]\\d\\.\\d{${e.precision}}`:`${t}(?::[0-5]\\d(?:\\.\\d+)?)?`}let tk=e=>{let t=e?`[\\s\\S]{${e?.minimum??0},${e?.maximum??""}}`:"[\\s\\S]*";return RegExp(`^${t}$`)},tz=/^[^A-Z]*$/,tj=/^[^a-z]*$/,tA=eF("$ZodCheck",(e,t)=>{var r;e._zod??(e._zod={}),e._zod.def=t,(r=e._zod).onattach??(r.onattach=[])}),t$=eF("$ZodCheckMaxLength",(e,t)=>{var r;tA.init(e,t),(r=e._zod.def).when??(r.when=e=>{let t=e.value;return null!=t&&void 0!==t.length}),e._zod.onattach.push(e=>{let r=e._zod.bag.maximum??Number.POSITIVE_INFINITY;t.maximum<r&&(e._zod.bag.maximum=t.maximum)}),e._zod.check=r=>{let i=r.value;if(i.length<=t.maximum)return;let a=eY(i);r.issues.push({origin:a,code:"too_big",maximum:t.maximum,inclusive:!0,input:i,inst:e,continue:!t.abort})}}),tS=eF("$ZodCheckMinLength",(e,t)=>{var r;tA.init(e,t),(r=e._zod.def).when??(r.when=e=>{let t=e.value;return null!=t&&void 0!==t.length}),e._zod.onattach.push(e=>{let r=e._zod.bag.minimum??Number.NEGATIVE_INFINITY;t.minimum>r&&(e._zod.bag.minimum=t.minimum)}),e._zod.check=r=>{let i=r.value;if(i.length>=t.minimum)return;let a=eY(i);r.issues.push({origin:a,code:"too_small",minimum:t.minimum,inclusive:!0,input:i,inst:e,continue:!t.abort})}}),tC=eF("$ZodCheckLengthEquals",(e,t)=>{var r;tA.init(e,t),(r=e._zod.def).when??(r.when=e=>{let t=e.value;return null!=t&&void 0!==t.length}),e._zod.onattach.push(e=>{let r=e._zod.bag;r.minimum=t.length,r.maximum=t.length,r.length=t.length}),e._zod.check=r=>{let i=r.value,a=i.length;if(a===t.length)return;let n=eY(i),s=a>t.length;r.issues.push({origin:n,...s?{code:"too_big",maximum:t.length}:{code:"too_small",minimum:t.length},inclusive:!0,exact:!0,input:r.value,inst:e,continue:!t.abort})}}),tF=eF("$ZodCheckStringFormat",(e,t)=>{var r,i;tA.init(e,t),e._zod.onattach.push(e=>{let r=e._zod.bag;r.format=t.format,t.pattern&&(r.patterns??(r.patterns=new Set),r.patterns.add(t.pattern))}),t.pattern?(r=e._zod).check??(r.check=r=>{t.pattern.lastIndex=0,t.pattern.test(r.value)||r.issues.push({origin:"string",code:"invalid_format",format:t.format,input:r.value,...t.pattern?{pattern:t.pattern.toString()}:{},inst:e,continue:!t.abort})}):(i=e._zod).check??(i.check=()=>{})}),tE=eF("$ZodCheckRegex",(e,t)=>{tF.init(e,t),e._zod.check=r=>{t.pattern.lastIndex=0,t.pattern.test(r.value)||r.issues.push({origin:"string",code:"invalid_format",format:"regex",input:r.value,pattern:t.pattern.toString(),inst:e,continue:!t.abort})}}),tV=eF("$ZodCheckLowerCase",(e,t)=>{t.pattern??(t.pattern=tz),tF.init(e,t)}),tP=eF("$ZodCheckUpperCase",(e,t)=>{t.pattern??(t.pattern=tj),tF.init(e,t)}),tZ=eF("$ZodCheckIncludes",(e,t)=>{tA.init(e,t);let r=eB(t.includes),i=new RegExp("number"==typeof t.position?`^.{${t.position}}${r}`:r);t.pattern=i,e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(i)}),e._zod.check=r=>{r.value.includes(t.includes,t.position)||r.issues.push({origin:"string",code:"invalid_format",format:"includes",includes:t.includes,input:r.value,inst:e,continue:!t.abort})}}),tN=eF("$ZodCheckStartsWith",(e,t)=>{tA.init(e,t);let r=RegExp(`^${eB(t.prefix)}.*`);t.pattern??(t.pattern=r),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(r)}),e._zod.check=r=>{r.value.startsWith(t.prefix)||r.issues.push({origin:"string",code:"invalid_format",format:"starts_with",prefix:t.prefix,input:r.value,inst:e,continue:!t.abort})}}),tD=eF("$ZodCheckEndsWith",(e,t)=>{tA.init(e,t);let r=RegExp(`.*${eB(t.suffix)}$`);t.pattern??(t.pattern=r),e._zod.onattach.push(e=>{let t=e._zod.bag;t.patterns??(t.patterns=new Set),t.patterns.add(r)}),e._zod.check=r=>{r.value.endsWith(t.suffix)||r.issues.push({origin:"string",code:"invalid_format",format:"ends_with",suffix:t.suffix,input:r.value,inst:e,continue:!t.abort})}}),tO=eF("$ZodCheckOverwrite",(e,t)=>{tA.init(e,t),e._zod.check=e=>{e.value=t.tx(e.value)}});class tT{constructor(e=[]){this.content=[],this.indent=0,this&&(this.args=e)}indented(e){this.indent+=1,e(this),this.indent-=1}write(e){if("function"==typeof e){e(this,{execution:"sync"}),e(this,{execution:"async"});return}let t=e.split("\n").filter(e=>e),r=Math.min(...t.map(e=>e.length-e.trimStart().length));for(let e of t.map(e=>e.slice(r)).map(e=>" ".repeat(2*this.indent)+e))this.content.push(e)}compile(){return Function(...this?.args,[...(this?.content??[""]).map(e=>`  ${e}`)].join("\n"))}}let tI={major:4,minor:0,patch:5},tR=eF("$ZodType",(e,t)=>{var r;e??(e={}),e._zod.def=t,e._zod.bag=e._zod.bag||{},e._zod.version=tI;let i=[...e._zod.def.checks??[]];for(let t of(e._zod.traits.has("$ZodCheck")&&i.unshift(e),i))for(let r of t._zod.onattach)r(e);if(0===i.length)(r=e._zod).deferred??(r.deferred=[]),e._zod.deferred?.push(()=>{e._zod.run=e._zod.parse});else{let t=(e,t,r)=>{let i,a=eH(e);for(let n of t){if(n._zod.def.when){if(!n._zod.def.when(e))continue}else if(a)continue;let t=e.issues.length,s=n._zod.check(e);if(s instanceof Promise&&r?.async===!1)throw new eE;if(i||s instanceof Promise)i=(i??Promise.resolve()).then(async()=>{await s,e.issues.length!==t&&(a||(a=eH(e,t)))});else{if(e.issues.length===t)continue;a||(a=eH(e,t))}}return i?i.then(()=>e):e};e._zod.run=(r,a)=>{let n=e._zod.parse(r,a);if(n instanceof Promise){if(!1===a.async)throw new eE;return n.then(e=>t(e,i,a))}return t(n,i,a)}}e["~standard"]={validate:t=>{try{let r=e8(e,t);return r.success?{value:r.data}:{issues:r.error?.issues}}catch(r){return te(e,t).then(e=>e.success?{value:e.data}:{issues:e.error?.issues})}},vendor:"zod",version:1}}),tU=eF("$ZodString",(e,t)=>{tR.init(e,t),e._zod.pattern=[...e?._zod.bag?.patterns??[]].pop()??tk(e._zod.bag),e._zod.parse=(r,i)=>{if(t.coerce)try{r.value=String(r.value)}catch(e){}return"string"==typeof r.value||r.issues.push({expected:"string",code:"invalid_type",input:r.value,inst:e}),r}}),tM=eF("$ZodStringFormat",(e,t)=>{tF.init(e,t),tU.init(e,t)}),tL=eF("$ZodGUID",(e,t)=>{t.pattern??(t.pattern=tu),tM.init(e,t)}),tq=eF("$ZodUUID",(e,t)=>{if(t.version){let e={v1:1,v2:2,v3:3,v4:4,v5:5,v6:6,v7:7,v8:8}[t.version];if(void 0===e)throw Error(`Invalid UUID version: "${t.version}"`);t.pattern??(t.pattern=td(e))}else t.pattern??(t.pattern=td());tM.init(e,t)}),tB=eF("$ZodEmail",(e,t)=>{t.pattern??(t.pattern=tc),tM.init(e,t)}),tW=eF("$ZodURL",(e,t)=>{tM.init(e,t),e._zod.check=r=>{try{let i=r.value,a=new URL(i),n=a.href;t.hostname&&(t.hostname.lastIndex=0,t.hostname.test(a.hostname)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid hostname",pattern:tg.source,input:r.value,inst:e,continue:!t.abort})),t.protocol&&(t.protocol.lastIndex=0,t.protocol.test(a.protocol.endsWith(":")?a.protocol.slice(0,-1):a.protocol)||r.issues.push({code:"invalid_format",format:"url",note:"Invalid protocol",pattern:t.protocol.source,input:r.value,inst:e,continue:!t.abort})),!i.endsWith("/")&&n.endsWith("/")?r.value=n.slice(0,-1):r.value=n;return}catch(i){r.issues.push({code:"invalid_format",format:"url",input:r.value,inst:e,continue:!t.abort})}}}),tG=eF("$ZodEmoji",(e,t)=>{t.pattern??(t.pattern=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),tM.init(e,t)}),tH=eF("$ZodNanoID",(e,t)=>{t.pattern??(t.pattern=to),tM.init(e,t)}),tK=eF("$ZodCUID",(e,t)=>{t.pattern??(t.pattern=tr),tM.init(e,t)}),tJ=eF("$ZodCUID2",(e,t)=>{t.pattern??(t.pattern=ti),tM.init(e,t)}),tX=eF("$ZodULID",(e,t)=>{t.pattern??(t.pattern=ta),tM.init(e,t)}),tY=eF("$ZodXID",(e,t)=>{t.pattern??(t.pattern=tn),tM.init(e,t)}),tQ=eF("$ZodKSUID",(e,t)=>{t.pattern??(t.pattern=ts),tM.init(e,t)}),t0=eF("$ZodISODateTime",(e,t)=>{t.pattern??(t.pattern=function(e){let t=tx({precision:e.precision}),r=["Z"];e.local&&r.push(""),e.offset&&r.push("([+-]\\d{2}:\\d{2})");let i=`${t}(?:${r.join("|")})`;return RegExp(`^${tb}T(?:${i})$`)}(t)),tM.init(e,t)}),t1=eF("$ZodISODate",(e,t)=>{t.pattern??(t.pattern=tw),tM.init(e,t)}),t2=eF("$ZodISOTime",(e,t)=>{t.pattern??(t.pattern=RegExp(`^${tx(t)}$`)),tM.init(e,t)}),t9=eF("$ZodISODuration",(e,t)=>{t.pattern??(t.pattern=tl),tM.init(e,t)}),t4=eF("$ZodIPv4",(e,t)=>{t.pattern??(t.pattern=tf),tM.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv4"})}),t6=eF("$ZodIPv6",(e,t)=>{t.pattern??(t.pattern=tp),tM.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.format="ipv6"}),e._zod.check=r=>{try{new URL(`http://[${r.value}]`)}catch{r.issues.push({code:"invalid_format",format:"ipv6",input:r.value,inst:e,continue:!t.abort})}}}),t3=eF("$ZodCIDRv4",(e,t)=>{t.pattern??(t.pattern=tm),tM.init(e,t)}),t5=eF("$ZodCIDRv6",(e,t)=>{t.pattern??(t.pattern=th),tM.init(e,t),e._zod.check=r=>{let[i,a]=r.value.split("/");try{if(!a)throw Error();let e=Number(a);if(`${e}`!==a||e<0||e>128)throw Error();new URL(`http://[${i}]`)}catch{r.issues.push({code:"invalid_format",format:"cidrv6",input:r.value,inst:e,continue:!t.abort})}}});function t8(e){if(""===e)return!0;if(e.length%4!=0)return!1;try{return atob(e),!0}catch{return!1}}let t7=eF("$ZodBase64",(e,t)=>{t.pattern??(t.pattern=ty),tM.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64"}),e._zod.check=r=>{t8(r.value)||r.issues.push({code:"invalid_format",format:"base64",input:r.value,inst:e,continue:!t.abort})}}),re=eF("$ZodBase64URL",(e,t)=>{t.pattern??(t.pattern=tv),tM.init(e,t),e._zod.onattach.push(e=>{e._zod.bag.contentEncoding="base64url"}),e._zod.check=r=>{!function(e){if(!tv.test(e))return!1;let t=e.replace(/[-_]/g,e=>"-"===e?"+":"/");return t8(t.padEnd(4*Math.ceil(t.length/4),"="))}(r.value)&&r.issues.push({code:"invalid_format",format:"base64url",input:r.value,inst:e,continue:!t.abort})}}),rt=eF("$ZodE164",(e,t)=>{t.pattern??(t.pattern=t_),tM.init(e,t)}),rr=eF("$ZodJWT",(e,t)=>{tM.init(e,t),e._zod.check=r=>{!function(e,t=null){try{let r=e.split(".");if(3!==r.length)return!1;let[i]=r;if(!i)return!1;let a=JSON.parse(atob(i));if("typ"in a&&a?.typ!=="JWT"||!a.alg||t&&(!("alg"in a)||a.alg!==t))return!1;return!0}catch{return!1}}(r.value,t.alg)&&r.issues.push({code:"invalid_format",format:"jwt",input:r.value,inst:e,continue:!t.abort})}}),ri=eF("$ZodUnknown",(e,t)=>{tR.init(e,t),e._zod.parse=e=>e}),ra=eF("$ZodNever",(e,t)=>{tR.init(e,t),e._zod.parse=(t,r)=>(t.issues.push({expected:"never",code:"invalid_type",input:t.value,inst:e}),t)});function rn(e,t,r){e.issues.length&&t.issues.push(...eK(r,e.issues)),t.value[r]=e.value}let rs=eF("$ZodArray",(e,t)=>{tR.init(e,t),e._zod.parse=(r,i)=>{let a=r.value;if(!Array.isArray(a))return r.issues.push({expected:"array",code:"invalid_type",input:a,inst:e}),r;r.value=Array(a.length);let n=[];for(let e=0;e<a.length;e++){let s=a[e],o=t.element._zod.run({value:s,issues:[]},i);o instanceof Promise?n.push(o.then(t=>rn(t,r,e))):rn(o,r,e)}return n.length?Promise.all(n).then(()=>r):r}});function ro(e,t,r){e.issues.length&&t.issues.push(...eK(r,e.issues)),t.value[r]=e.value}function rl(e,t,r,i){e.issues.length?void 0===i[r]?r in i?t.value[r]=void 0:t.value[r]=e.value:t.issues.push(...eK(r,e.issues)):void 0===e.value?r in i&&(t.value[r]=void 0):t.value[r]=e.value}let ru=eF("$ZodObject",(e,t)=>{let r,i;tR.init(e,t);let a=eN(()=>{let e=Object.keys(t.shape);for(let r of e)if(!(t.shape[r]instanceof tR))throw Error(`Invalid element at key "${r}": expected a Zod schema`);let r=function(e){return Object.keys(e).filter(t=>"optional"===e[t]._zod.optin&&"optional"===e[t]._zod.optout)}(t.shape);return{shape:t.shape,keys:e,keySet:new Set(e),numKeys:e.length,optionalKeys:new Set(r)}});eO(e._zod,"propValues",()=>{let e=t.shape,r={};for(let t in e){let i=e[t]._zod;if(i.values)for(let e of(r[t]??(r[t]=new Set),i.values))r[t].add(e)}return r});let n=e=>{let t=new tT(["shape","payload","ctx"]),r=a.value,i=e=>{let t=eI(e);return`shape[${t}]._zod.run({ value: input[${t}], issues: [] }, ctx)`};t.write("const input = payload.value;");let n=Object.create(null),s=0;for(let e of r.keys)n[e]=`key_${s++}`;for(let e of(t.write("const newResult = {}"),r.keys))if(r.optionalKeys.has(e)){let r=n[e];t.write(`const ${r} = ${i(e)};`);let a=eI(e);t.write(`
        if (${r}.issues.length) {
          if (input[${a}] === undefined) {
            if (${a} in input) {
              newResult[${a}] = undefined;
            }
          } else {
            payload.issues = payload.issues.concat(
              ${r}.issues.map((iss) => ({
                ...iss,
                path: iss.path ? [${a}, ...iss.path] : [${a}],
              }))
            );
          }
        } else if (${r}.value === undefined) {
          if (${a} in input) newResult[${a}] = undefined;
        } else {
          newResult[${a}] = ${r}.value;
        }
        `)}else{let r=n[e];t.write(`const ${r} = ${i(e)};`),t.write(`
          if (${r}.issues.length) payload.issues = payload.issues.concat(${r}.issues.map(iss => ({
            ...iss,
            path: iss.path ? [${eI(e)}, ...iss.path] : [${eI(e)}]
          })));`),t.write(`newResult[${eI(e)}] = ${r}.value`)}t.write("payload.value = newResult;"),t.write("return payload;");let o=t.compile();return(t,r)=>o(e,t,r)},s=!eV.jitless,o=s&&eM.value,l=t.catchall;e._zod.parse=(u,d)=>{i??(i=a.value);let c=u.value;if(!eU(c))return u.issues.push({expected:"object",code:"invalid_type",input:c,inst:e}),u;let f=[];if(s&&o&&d?.async===!1&&!0!==d.jitless)r||(r=n(t.shape)),u=r(u,d);else{u.value={};let e=i.shape;for(let t of i.keys){let r=e[t],i=r._zod.run({value:c[t],issues:[]},d),a="optional"===r._zod.optin&&"optional"===r._zod.optout;i instanceof Promise?f.push(i.then(e=>a?rl(e,u,t,c):ro(e,u,t))):a?rl(i,u,t,c):ro(i,u,t)}}if(!l)return f.length?Promise.all(f).then(()=>u):u;let p=[],m=i.keySet,h=l._zod,y=h.def.type;for(let e of Object.keys(c)){if(m.has(e))continue;if("never"===y){p.push(e);continue}let t=h.run({value:c[e],issues:[]},d);t instanceof Promise?f.push(t.then(t=>ro(t,u,e))):ro(t,u,e)}return(p.length&&u.issues.push({code:"unrecognized_keys",keys:p,input:c,inst:e}),f.length)?Promise.all(f).then(()=>u):u}});function rd(e,t,r,i){for(let r of e)if(0===r.issues.length)return t.value=r.value,t;return t.issues.push({code:"invalid_union",input:t.value,inst:r,errors:e.map(e=>e.issues.map(e=>eX(e,i,eP())))}),t}let rc=eF("$ZodUnion",(e,t)=>{tR.init(e,t),eO(e._zod,"optin",()=>t.options.some(e=>"optional"===e._zod.optin)?"optional":void 0),eO(e._zod,"optout",()=>t.options.some(e=>"optional"===e._zod.optout)?"optional":void 0),eO(e._zod,"values",()=>{if(t.options.every(e=>e._zod.values))return new Set(t.options.flatMap(e=>Array.from(e._zod.values)))}),eO(e._zod,"pattern",()=>{if(t.options.every(e=>e._zod.pattern)){let e=t.options.map(e=>e._zod.pattern);return RegExp(`^(${e.map(e=>eD(e.source)).join("|")})$`)}}),e._zod.parse=(r,i)=>{let a=!1,n=[];for(let e of t.options){let t=e._zod.run({value:r.value,issues:[]},i);if(t instanceof Promise)n.push(t),a=!0;else{if(0===t.issues.length)return t;n.push(t)}}return a?Promise.all(n).then(t=>rd(t,r,e,i)):rd(n,r,e,i)}}),rf=eF("$ZodIntersection",(e,t)=>{tR.init(e,t),e._zod.parse=(e,r)=>{let i=e.value,a=t.left._zod.run({value:i,issues:[]},r),n=t.right._zod.run({value:i,issues:[]},r);return a instanceof Promise||n instanceof Promise?Promise.all([a,n]).then(([t,r])=>rp(e,t,r)):rp(e,a,n)}});function rp(e,t,r){if(t.issues.length&&e.issues.push(...t.issues),r.issues.length&&e.issues.push(...r.issues),eH(e))return e;let i=function e(t,r){if(t===r||t instanceof Date&&r instanceof Date&&+t==+r)return{valid:!0,data:t};if(eL(t)&&eL(r)){let i=Object.keys(r),a=Object.keys(t).filter(e=>-1!==i.indexOf(e)),n={...t,...r};for(let i of a){let a=e(t[i],r[i]);if(!a.valid)return{valid:!1,mergeErrorPath:[i,...a.mergeErrorPath]};n[i]=a.data}return{valid:!0,data:n}}if(Array.isArray(t)&&Array.isArray(r)){if(t.length!==r.length)return{valid:!1,mergeErrorPath:[]};let i=[];for(let a=0;a<t.length;a++){let n=e(t[a],r[a]);if(!n.valid)return{valid:!1,mergeErrorPath:[a,...n.mergeErrorPath]};i.push(n.data)}return{valid:!0,data:i}}return{valid:!1,mergeErrorPath:[]}}(t.value,r.value);if(!i.valid)throw Error(`Unmergable intersection. Error path: ${JSON.stringify(i.mergeErrorPath)}`);return e.value=i.data,e}let rm=eF("$ZodEnum",(e,t)=>{tR.init(e,t);let r=function(e){let t=Object.values(e).filter(e=>"number"==typeof e);return Object.entries(e).filter(([e,r])=>-1===t.indexOf(+e)).map(([e,t])=>t)}(t.entries);e._zod.values=new Set(r),e._zod.pattern=RegExp(`^(${r.filter(e=>eq.has(typeof e)).map(e=>"string"==typeof e?eB(e):e.toString()).join("|")})$`),e._zod.parse=(t,i)=>{let a=t.value;return e._zod.values.has(a)||t.issues.push({code:"invalid_value",values:r,input:a,inst:e}),t}}),rh=eF("$ZodTransform",(e,t)=>{tR.init(e,t),e._zod.parse=(e,r)=>{let i=t.transform(e.value,e);if(r.async)return(i instanceof Promise?i:Promise.resolve(i)).then(t=>(e.value=t,e));if(i instanceof Promise)throw new eE;return e.value=i,e}}),ry=eF("$ZodOptional",(e,t)=>{tR.init(e,t),e._zod.optin="optional",e._zod.optout="optional",eO(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,void 0]):void 0),eO(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${eD(e.source)})?$`):void 0}),e._zod.parse=(e,r)=>"optional"===t.innerType._zod.optin?t.innerType._zod.run(e,r):void 0===e.value?e:t.innerType._zod.run(e,r)}),rv=eF("$ZodNullable",(e,t)=>{tR.init(e,t),eO(e._zod,"optin",()=>t.innerType._zod.optin),eO(e._zod,"optout",()=>t.innerType._zod.optout),eO(e._zod,"pattern",()=>{let e=t.innerType._zod.pattern;return e?RegExp(`^(${eD(e.source)}|null)$`):void 0}),eO(e._zod,"values",()=>t.innerType._zod.values?new Set([...t.innerType._zod.values,null]):void 0),e._zod.parse=(e,r)=>null===e.value?e:t.innerType._zod.run(e,r)}),rg=eF("$ZodDefault",(e,t)=>{tR.init(e,t),e._zod.optin="optional",eO(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,r)=>{if(void 0===e.value)return e.value=t.defaultValue,e;let i=t.innerType._zod.run(e,r);return i instanceof Promise?i.then(e=>r_(e,t)):r_(i,t)}});function r_(e,t){return void 0===e.value&&(e.value=t.defaultValue),e}let rb=eF("$ZodPrefault",(e,t)=>{tR.init(e,t),e._zod.optin="optional",eO(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,r)=>(void 0===e.value&&(e.value=t.defaultValue),t.innerType._zod.run(e,r))}),rw=eF("$ZodNonOptional",(e,t)=>{tR.init(e,t),eO(e._zod,"values",()=>{let e=t.innerType._zod.values;return e?new Set([...e].filter(e=>void 0!==e)):void 0}),e._zod.parse=(r,i)=>{let a=t.innerType._zod.run(r,i);return a instanceof Promise?a.then(t=>rx(t,e)):rx(a,e)}});function rx(e,t){return e.issues.length||void 0!==e.value||e.issues.push({code:"invalid_type",expected:"nonoptional",input:e.value,inst:t}),e}let rk=eF("$ZodCatch",(e,t)=>{tR.init(e,t),e._zod.optin="optional",eO(e._zod,"optout",()=>t.innerType._zod.optout),eO(e._zod,"values",()=>t.innerType._zod.values),e._zod.parse=(e,r)=>{let i=t.innerType._zod.run(e,r);return i instanceof Promise?i.then(i=>(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>eX(e,r,eP()))},input:e.value}),e.issues=[]),e)):(e.value=i.value,i.issues.length&&(e.value=t.catchValue({...e,error:{issues:i.issues.map(e=>eX(e,r,eP()))},input:e.value}),e.issues=[]),e)}}),rz=eF("$ZodPipe",(e,t)=>{tR.init(e,t),eO(e._zod,"values",()=>t.in._zod.values),eO(e._zod,"optin",()=>t.in._zod.optin),eO(e._zod,"optout",()=>t.out._zod.optout),eO(e._zod,"propValues",()=>t.in._zod.propValues),e._zod.parse=(e,r)=>{let i=t.in._zod.run(e,r);return i instanceof Promise?i.then(e=>rj(e,t,r)):rj(i,t,r)}});function rj(e,t,r){return eH(e)?e:t.out._zod.run({value:e.value,issues:e.issues},r)}let rA=eF("$ZodReadonly",(e,t)=>{tR.init(e,t),eO(e._zod,"propValues",()=>t.innerType._zod.propValues),eO(e._zod,"values",()=>t.innerType._zod.values),eO(e._zod,"optin",()=>t.innerType._zod.optin),eO(e._zod,"optout",()=>t.innerType._zod.optout),e._zod.parse=(e,r)=>{let i=t.innerType._zod.run(e,r);return i instanceof Promise?i.then(r$):r$(i)}});function r$(e){return e.value=Object.freeze(e.value),e}let rS=eF("$ZodCustom",(e,t)=>{tA.init(e,t),tR.init(e,t),e._zod.parse=(e,t)=>e,e._zod.check=r=>{let i=r.value,a=t.fn(i);if(a instanceof Promise)return a.then(t=>rC(t,r,i,e));rC(a,r,i,e)}});function rC(e,t,r,i){if(!e){let e={code:"custom",input:r,inst:i,path:[...i._zod.def.path??[]],continue:!i._zod.def.abort};i._zod.def.params&&(e.params=i._zod.def.params),t.issues.push(eQ(e))}}Symbol("ZodOutput"),Symbol("ZodInput");class rF{constructor(){this._map=new Map,this._idmap=new Map}add(e,...t){let r=t[0];if(this._map.set(e,r),r&&"object"==typeof r&&"id"in r){if(this._idmap.has(r.id))throw Error(`ID ${r.id} already exists in the registry`);this._idmap.set(r.id,e)}return this}clear(){return this._map=new Map,this._idmap=new Map,this}remove(e){let t=this._map.get(e);return t&&"object"==typeof t&&"id"in t&&this._idmap.delete(t.id),this._map.delete(e),this}get(e){let t=e._zod.parent;if(t){let r={...this.get(t)??{}};return delete r.id,{...r,...this._map.get(e)}}return this._map.get(e)}has(e){return this._map.has(e)}}let rE=new rF;function rV(e,t){return new e({type:"string",format:"guid",check:"string_format",abort:!1,...eG(t)})}function rP(e,t){return new t$({check:"max_length",...eG(t),maximum:e})}function rZ(e,t){return new tS({check:"min_length",...eG(t),minimum:e})}function rN(e,t){return new tC({check:"length_equals",...eG(t),length:e})}function rD(e){return new tO({check:"overwrite",tx:e})}let rO=eF("ZodISODateTime",(e,t)=>{t0.init(e,t),rX.init(e,t)}),rT=eF("ZodISODate",(e,t)=>{t1.init(e,t),rX.init(e,t)}),rI=eF("ZodISOTime",(e,t)=>{t2.init(e,t),rX.init(e,t)}),rR=eF("ZodISODuration",(e,t)=>{t9.init(e,t),rX.init(e,t)}),rU=(e,t)=>{e1.init(e,t),e.name="ZodError",Object.defineProperties(e,{format:{value:t=>(function(e,t){let r=t||function(e){return e.message},i={_errors:[]},a=e=>{for(let t of e.issues)if("invalid_union"===t.code&&t.errors.length)t.errors.map(e=>a({issues:e}));else if("invalid_key"===t.code)a({issues:t.issues});else if("invalid_element"===t.code)a({issues:t.issues});else if(0===t.path.length)i._errors.push(r(t));else{let e=i,a=0;for(;a<t.path.length;){let i=t.path[a];a===t.path.length-1?(e[i]=e[i]||{_errors:[]},e[i]._errors.push(r(t))):e[i]=e[i]||{_errors:[]},e=e[i],a++}}};return a(e),i})(e,t)},flatten:{value:t=>(function(e,t=e=>e.message){let r={},i=[];for(let a of e.issues)a.path.length>0?(r[a.path[0]]=r[a.path[0]]||[],r[a.path[0]].push(t(a))):i.push(t(a));return{formErrors:i,fieldErrors:r}})(e,t)},addIssue:{value:t=>e.issues.push(t)},addIssues:{value:t=>e.issues.push(...t)},isEmpty:{get:()=>0===e.issues.length}})};eF("ZodError",rU);let rM=eF("ZodError",rU,{Parent:Error}),rL=e9(rM),rq=e6(rM),rB=e5(rM),rW=e7(rM),rG=eF("ZodType",(e,t)=>(tR.init(e,t),e.def=t,Object.defineProperty(e,"_def",{value:t}),e.check=(...r)=>e.clone({...t,checks:[...t.checks??[],...r.map(e=>"function"==typeof e?{_zod:{check:e,def:{check:"custom"},onattach:[]}}:e)]}),e.clone=(t,r)=>eW(e,t,r),e.brand=()=>e,e.register=(t,r)=>(t.add(e,r),e),e.parse=(t,r)=>rL(e,t,r,{callee:e.parse}),e.safeParse=(t,r)=>rB(e,t,r),e.parseAsync=async(t,r)=>rq(e,t,r,{callee:e.parseAsync}),e.safeParseAsync=async(t,r)=>rW(e,t,r),e.spa=e.safeParseAsync,e.refine=(t,r)=>e.check(function(e,t={}){return new iC({type:"custom",check:"custom",fn:e,...eG(t)})}(t,r)),e.superRefine=t=>e.check(function(e){let t=function(e){let t=new tA({check:"custom"});return t._zod.check=e,t}(r=>(r.addIssue=e=>{"string"==typeof e?r.issues.push(eQ(e,r.value,t._zod.def)):(e.fatal&&(e.continue=!1),e.code??(e.code="custom"),e.input??(e.input=r.value),e.inst??(e.inst=t),e.continue??(e.continue=!t._zod.def.abort),r.issues.push(eQ(e)))},e(r.value,r)));return t}(t)),e.overwrite=t=>e.check(rD(t)),e.optional=()=>i_(e),e.nullable=()=>iw(e),e.nullish=()=>i_(iw(e)),e.nonoptional=t=>new iz({type:"nonoptional",innerType:e,...eG(t)}),e.array=()=>(function(e,t){return new ic({type:"array",element:e,...eG(t)})})(e),e.or=t=>(function(e,t){return new im({type:"union",options:e,...eG(t)})})([e,t]),e.and=t=>new ih({type:"intersection",left:e,right:t}),e.transform=t=>i$(e,function(e){return new iv({type:"transform",transform:e})}(t)),e.default=t=>(function(e,t){return new ix({type:"default",innerType:e,get defaultValue(){return"function"==typeof t?t():t}})})(e,t),e.prefault=t=>(function(e,t){return new ik({type:"prefault",innerType:e,get defaultValue(){return"function"==typeof t?t():t}})})(e,t),e.catch=t=>(function(e,t){return new ij({type:"catch",innerType:e,catchValue:"function"==typeof t?t:()=>t})})(e,t),e.pipe=t=>i$(e,t),e.readonly=()=>new iS({type:"readonly",innerType:e}),e.describe=t=>{let r=e.clone();return rE.add(r,{description:t}),r},Object.defineProperty(e,"description",{get:()=>rE.get(e)?.description,configurable:!0}),e.meta=(...t)=>{if(0===t.length)return rE.get(e);let r=e.clone();return rE.add(r,t[0]),r},e.isOptional=()=>e.safeParse(void 0).success,e.isNullable=()=>e.safeParse(null).success,e)),rH=eF("_ZodString",(e,t)=>{tU.init(e,t),rG.init(e,t);let r=e._zod.bag;e.format=r.format??null,e.minLength=r.minimum??null,e.maxLength=r.maximum??null,e.regex=(...t)=>e.check(function(e,t){return new tE({check:"string_format",format:"regex",...eG(t),pattern:e})}(...t)),e.includes=(...t)=>e.check(function(e,t){return new tZ({check:"string_format",format:"includes",...eG(t),includes:e})}(...t)),e.startsWith=(...t)=>e.check(function(e,t){return new tN({check:"string_format",format:"starts_with",...eG(t),prefix:e})}(...t)),e.endsWith=(...t)=>e.check(function(e,t){return new tD({check:"string_format",format:"ends_with",...eG(t),suffix:e})}(...t)),e.min=(...t)=>e.check(rZ(...t)),e.max=(...t)=>e.check(rP(...t)),e.length=(...t)=>e.check(rN(...t)),e.nonempty=(...t)=>e.check(rZ(1,...t)),e.lowercase=t=>e.check(new tV({check:"string_format",format:"lowercase",...eG(t)})),e.uppercase=t=>e.check(new tP({check:"string_format",format:"uppercase",...eG(t)})),e.trim=()=>e.check(rD(e=>e.trim())),e.normalize=(...t)=>e.check(function(e){return rD(t=>t.normalize(e))}(...t)),e.toLowerCase=()=>e.check(rD(e=>e.toLowerCase())),e.toUpperCase=()=>e.check(rD(e=>e.toUpperCase()))}),rK=eF("ZodString",(e,t)=>{tU.init(e,t),rH.init(e,t),e.email=t=>e.check(new rY({type:"string",format:"email",check:"string_format",abort:!1,...eG(t)})),e.url=t=>e.check(new r1({type:"string",format:"url",check:"string_format",abort:!1,...eG(t)})),e.jwt=t=>e.check(new io({type:"string",format:"jwt",check:"string_format",abort:!1,...eG(t)})),e.emoji=t=>e.check(new r2({type:"string",format:"emoji",check:"string_format",abort:!1,...eG(t)})),e.guid=t=>e.check(rV(rQ,t)),e.uuid=t=>e.check(new r0({type:"string",format:"uuid",check:"string_format",abort:!1,...eG(t)})),e.uuidv4=t=>e.check(new r0({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v4",...eG(t)})),e.uuidv6=t=>e.check(new r0({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v6",...eG(t)})),e.uuidv7=t=>e.check(new r0({type:"string",format:"uuid",check:"string_format",abort:!1,version:"v7",...eG(t)})),e.nanoid=t=>e.check(new r9({type:"string",format:"nanoid",check:"string_format",abort:!1,...eG(t)})),e.guid=t=>e.check(rV(rQ,t)),e.cuid=t=>e.check(new r4({type:"string",format:"cuid",check:"string_format",abort:!1,...eG(t)})),e.cuid2=t=>e.check(new r6({type:"string",format:"cuid2",check:"string_format",abort:!1,...eG(t)})),e.ulid=t=>e.check(new r3({type:"string",format:"ulid",check:"string_format",abort:!1,...eG(t)})),e.base64=t=>e.check(new ii({type:"string",format:"base64",check:"string_format",abort:!1,...eG(t)})),e.base64url=t=>e.check(new ia({type:"string",format:"base64url",check:"string_format",abort:!1,...eG(t)})),e.xid=t=>e.check(new r5({type:"string",format:"xid",check:"string_format",abort:!1,...eG(t)})),e.ksuid=t=>e.check(new r8({type:"string",format:"ksuid",check:"string_format",abort:!1,...eG(t)})),e.ipv4=t=>e.check(new r7({type:"string",format:"ipv4",check:"string_format",abort:!1,...eG(t)})),e.ipv6=t=>e.check(new ie({type:"string",format:"ipv6",check:"string_format",abort:!1,...eG(t)})),e.cidrv4=t=>e.check(new it({type:"string",format:"cidrv4",check:"string_format",abort:!1,...eG(t)})),e.cidrv6=t=>e.check(new ir({type:"string",format:"cidrv6",check:"string_format",abort:!1,...eG(t)})),e.e164=t=>e.check(new is({type:"string",format:"e164",check:"string_format",abort:!1,...eG(t)})),e.datetime=t=>e.check(new rO({type:"string",format:"datetime",check:"string_format",offset:!1,local:!1,precision:null,...eG(t)})),e.date=t=>e.check(new rT({type:"string",format:"date",check:"string_format",...eG(t)})),e.time=t=>e.check(new rI({type:"string",format:"time",check:"string_format",precision:null,...eG(t)})),e.duration=t=>e.check(new rR({type:"string",format:"duration",check:"string_format",...eG(t)}))});function rJ(e){return new rK({type:"string",...eG(e)})}let rX=eF("ZodStringFormat",(e,t)=>{tM.init(e,t),rH.init(e,t)}),rY=eF("ZodEmail",(e,t)=>{tB.init(e,t),rX.init(e,t)}),rQ=eF("ZodGUID",(e,t)=>{tL.init(e,t),rX.init(e,t)}),r0=eF("ZodUUID",(e,t)=>{tq.init(e,t),rX.init(e,t)}),r1=eF("ZodURL",(e,t)=>{tW.init(e,t),rX.init(e,t)}),r2=eF("ZodEmoji",(e,t)=>{tG.init(e,t),rX.init(e,t)}),r9=eF("ZodNanoID",(e,t)=>{tH.init(e,t),rX.init(e,t)}),r4=eF("ZodCUID",(e,t)=>{tK.init(e,t),rX.init(e,t)}),r6=eF("ZodCUID2",(e,t)=>{tJ.init(e,t),rX.init(e,t)}),r3=eF("ZodULID",(e,t)=>{tX.init(e,t),rX.init(e,t)}),r5=eF("ZodXID",(e,t)=>{tY.init(e,t),rX.init(e,t)}),r8=eF("ZodKSUID",(e,t)=>{tQ.init(e,t),rX.init(e,t)}),r7=eF("ZodIPv4",(e,t)=>{t4.init(e,t),rX.init(e,t)}),ie=eF("ZodIPv6",(e,t)=>{t6.init(e,t),rX.init(e,t)}),it=eF("ZodCIDRv4",(e,t)=>{t3.init(e,t),rX.init(e,t)}),ir=eF("ZodCIDRv6",(e,t)=>{t5.init(e,t),rX.init(e,t)}),ii=eF("ZodBase64",(e,t)=>{t7.init(e,t),rX.init(e,t)}),ia=eF("ZodBase64URL",(e,t)=>{re.init(e,t),rX.init(e,t)}),is=eF("ZodE164",(e,t)=>{rt.init(e,t),rX.init(e,t)}),io=eF("ZodJWT",(e,t)=>{rr.init(e,t),rX.init(e,t)}),il=eF("ZodUnknown",(e,t)=>{ri.init(e,t),rG.init(e,t)});function iu(){return new il({type:"unknown"})}let id=eF("ZodNever",(e,t)=>{ra.init(e,t),rG.init(e,t)}),ic=eF("ZodArray",(e,t)=>{rs.init(e,t),rG.init(e,t),e.element=t.element,e.min=(t,r)=>e.check(rZ(t,r)),e.nonempty=t=>e.check(rZ(1,t)),e.max=(t,r)=>e.check(rP(t,r)),e.length=(t,r)=>e.check(rN(t,r)),e.unwrap=()=>e.element}),ip=eF("ZodObject",(e,t)=>{ru.init(e,t),rG.init(e,t),eO(e,"shape",()=>t.shape),e.keyof=()=>(function(e,t){return new iy({type:"enum",entries:Array.isArray(e)?Object.fromEntries(e.map(e=>[e,e])):e,...eG(void 0)})})(Object.keys(e._zod.def.shape)),e.catchall=t=>e.clone({...e._zod.def,catchall:t}),e.passthrough=()=>e.clone({...e._zod.def,catchall:iu()}),e.loose=()=>e.clone({...e._zod.def,catchall:iu()}),e.strict=()=>{var t;return e.clone({...e._zod.def,catchall:new id({type:"never",...eG(void 0)})})},e.strip=()=>e.clone({...e._zod.def,catchall:void 0}),e.extend=t=>(function(e,t){if(!eL(t))throw Error("Invalid input to extend: expected a plain object");let r={...e._zod.def,get shape(){let r={...e._zod.def.shape,...t};return eT(this,"shape",r),r},checks:[]};return eW(e,r)})(e,t),e.merge=t=>(function(e,t){return eW(e,{...e._zod.def,get shape(){let r={...e._zod.def.shape,...t._zod.def.shape};return eT(this,"shape",r),r},catchall:t._zod.def.catchall,checks:[]})})(e,t),e.pick=t=>(function(e,t){let r={},i=e._zod.def;for(let e in t){if(!(e in i.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&(r[e]=i.shape[e])}return eW(e,{...e._zod.def,shape:r,checks:[]})})(e,t),e.omit=t=>(function(e,t){let r={...e._zod.def.shape},i=e._zod.def;for(let e in t){if(!(e in i.shape))throw Error(`Unrecognized key: "${e}"`);t[e]&&delete r[e]}return eW(e,{...e._zod.def,shape:r,checks:[]})})(e,t),e.partial=(...t)=>(function(e,t,r){let i=t._zod.def.shape,a={...i};if(r)for(let t in r){if(!(t in i))throw Error(`Unrecognized key: "${t}"`);r[t]&&(a[t]=e?new e({type:"optional",innerType:i[t]}):i[t])}else for(let t in i)a[t]=e?new e({type:"optional",innerType:i[t]}):i[t];return eW(t,{...t._zod.def,shape:a,checks:[]})})(ig,e,t[0]),e.required=(...t)=>(function(e,t,r){let i=t._zod.def.shape,a={...i};if(r)for(let t in r){if(!(t in a))throw Error(`Unrecognized key: "${t}"`);r[t]&&(a[t]=new e({type:"nonoptional",innerType:i[t]}))}else for(let t in i)a[t]=new e({type:"nonoptional",innerType:i[t]});return eW(t,{...t._zod.def,shape:a,checks:[]})})(iz,e,t[0])}),im=eF("ZodUnion",(e,t)=>{rc.init(e,t),rG.init(e,t),e.options=t.options}),ih=eF("ZodIntersection",(e,t)=>{rf.init(e,t),rG.init(e,t)}),iy=eF("ZodEnum",(e,t)=>{rm.init(e,t),rG.init(e,t),e.enum=t.entries,e.options=Object.values(t.entries);let r=new Set(Object.keys(t.entries));e.extract=(e,i)=>{let a={};for(let i of e)if(r.has(i))a[i]=t.entries[i];else throw Error(`Key ${i} not found in enum`);return new iy({...t,checks:[],...eG(i),entries:a})},e.exclude=(e,i)=>{let a={...t.entries};for(let t of e)if(r.has(t))delete a[t];else throw Error(`Key ${t} not found in enum`);return new iy({...t,checks:[],...eG(i),entries:a})}}),iv=eF("ZodTransform",(e,t)=>{rh.init(e,t),rG.init(e,t),e._zod.parse=(r,i)=>{r.addIssue=i=>{"string"==typeof i?r.issues.push(eQ(i,r.value,t)):(i.fatal&&(i.continue=!1),i.code??(i.code="custom"),i.input??(i.input=r.value),i.inst??(i.inst=e),i.continue??(i.continue=!0),r.issues.push(eQ(i)))};let a=t.transform(r.value,r);return a instanceof Promise?a.then(e=>(r.value=e,r)):(r.value=a,r)}}),ig=eF("ZodOptional",(e,t)=>{ry.init(e,t),rG.init(e,t),e.unwrap=()=>e._zod.def.innerType});function i_(e){return new ig({type:"optional",innerType:e})}let ib=eF("ZodNullable",(e,t)=>{rv.init(e,t),rG.init(e,t),e.unwrap=()=>e._zod.def.innerType});function iw(e){return new ib({type:"nullable",innerType:e})}let ix=eF("ZodDefault",(e,t)=>{rg.init(e,t),rG.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeDefault=e.unwrap}),ik=eF("ZodPrefault",(e,t)=>{rb.init(e,t),rG.init(e,t),e.unwrap=()=>e._zod.def.innerType}),iz=eF("ZodNonOptional",(e,t)=>{rw.init(e,t),rG.init(e,t),e.unwrap=()=>e._zod.def.innerType}),ij=eF("ZodCatch",(e,t)=>{rk.init(e,t),rG.init(e,t),e.unwrap=()=>e._zod.def.innerType,e.removeCatch=e.unwrap}),iA=eF("ZodPipe",(e,t)=>{rz.init(e,t),rG.init(e,t),e.in=t.in,e.out=t.out});function i$(e,t){return new iA({type:"pipe",in:e,out:t})}let iS=eF("ZodReadonly",(e,t)=>{rA.init(e,t),rG.init(e,t)}),iC=eF("ZodCustom",(e,t)=>{rS.init(e,t),rG.init(e,t)});var iF=r(44493),iE=r(29523),iV=r(8730),iP=r(4780),iZ=r(14163),iN=n.forwardRef((e,t)=>(0,a.jsx)(iZ.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));iN.displayName="Label";let iD=(0,r(24224).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),iO=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)(iN,{ref:r,className:(0,iP.cn)(iD(),e),...t}));iO.displayName=iN.displayName;let iT=e=>{let{children:t,...r}=e;return n.createElement(S.Provider,{value:r},t)},iI=n.createContext({}),iR=({...e})=>(0,a.jsx)(iI.Provider,{value:{name:e.name},children:(0,a.jsx)(Z,{...e})}),iU=()=>{let e=n.useContext(iI),t=n.useContext(iM),{getFieldState:r,formState:i}=C(),a=r(e.name,i);if(!e)throw Error("useFormField should be used within <FormField>");let{id:s}=t;return{id:s,name:e.name,formItemId:`${s}-form-item`,formDescriptionId:`${s}-form-item-description`,formMessageId:`${s}-form-item-message`,...a}},iM=n.createContext({}),iL=n.forwardRef(({className:e,...t},r)=>{let i=n.useId();return(0,a.jsx)(iM.Provider,{value:{id:i},children:(0,a.jsx)("div",{ref:r,className:(0,iP.cn)("space-y-2",e),...t})})});iL.displayName="FormItem";let iq=n.forwardRef(({className:e,...t},r)=>{let{error:i,formItemId:n}=iU();return(0,a.jsx)(iO,{ref:r,className:(0,iP.cn)(i&&"text-destructive",e),htmlFor:n,...t})});iq.displayName="FormLabel";let iB=n.forwardRef(({...e},t)=>{let{error:r,formItemId:i,formDescriptionId:n,formMessageId:s}=iU();return(0,a.jsx)(iV.DX,{ref:t,id:i,"aria-describedby":r?`${n} ${s}`:`${n}`,"aria-invalid":!!r,...e})});iB.displayName="FormControl",n.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:i}=iU();return(0,a.jsx)("p",{ref:r,id:i,className:(0,iP.cn)("text-sm text-muted-foreground",e),...t})}).displayName="FormDescription";let iW=n.forwardRef(({className:e,children:t,...r},i)=>{let{error:n,formMessageId:s}=iU(),o=n?String(n?.message):t;return o?(0,a.jsx)("p",{ref:i,id:s,className:(0,iP.cn)("text-sm font-medium text-destructive",e),...r,children:o}):null});iW.displayName="FormMessage";var iG=r(89667);let iH=n.forwardRef(({className:e,...t},r)=>(0,a.jsx)("textarea",{className:(0,iP.cn)("flex min-h-[80px] w-full rounded-md border-[1px] border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-charity-primary focus-visible:border-charity-primary focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 shadow-none",e),ref:r,...t}));iH.displayName="Textarea";var iK=r(62185),iJ=r(43125),iX=r(97992),iY=r(48340),iQ=r(41550),i0=r(51423);let i1=(i={name:rJ().min(2,{message:"Name must be at least 2 characters."}),email:rJ().email({message:"Please enter a valid email address."}),subject:rJ().min(5,{message:"Subject must be at least 5 characters."}),message:rJ().min(10,{message:"Message must be at least 10 characters."})},new ip({type:"object",get shape(){return eT(this,"shape",{...i}),this.shape},...eG(void 0)}));function i2(){let{toast:e}={toast:({title:e,description:t})=>{alert(`${e}: ${t}`)}},[t,r]=s().useState(!1),i="Charity Welcome Hub",{data:d,isLoading:p}=(0,i0.I)({queryKey:["locations"],queryFn:()=>iK.lM.getAll({active:!0})}),h=s().useMemo(()=>d?.locations?d.locations.find(e=>e.isMainOffice)||d.locations[0]:null,[d]),g=function(e={}){let t=n.useRef(void 0),r=n.useRef(void 0),[i,a]=n.useState({isDirty:!1,isValidating:!1,isLoading:M(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:M(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:i},e.defaultValues&&!M(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{let{formControl:r,...a}=function(e={}){let t,r={...ez,...e},i={submitCount:0,isDirty:!1,isReady:!1,isLoading:M(r.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:r.errors||{},disabled:r.disabled||!1},a={},n=(c(r.defaultValues)||c(r.values))&&v(r.defaultValues||r.values)||{},s=r.shouldUnregister?{}:v(n),d={action:!1,mount:!1,watch:!1},p={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},h=0,g={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},w={...g},$={array:O(),state:O()},S=r.criteriaMode===A.all,C=e=>t=>{clearTimeout(h),h=setTimeout(e,t)},F=async e=>{if(!r.disabled&&(g.isValid||w.isValid||e)){let e=r.resolver?R((await K()).errors):await Y(a,!0);e!==i.isValid&&$.state.next({isValid:e})}},E=(e,t)=>{!r.disabled&&(g.isValidating||g.validatingFields||w.isValidating||w.validatingFields)&&((e||Array.from(p.mount)).forEach(e=>{e&&(t?z(i.validatingFields,e,t):H(i.validatingFields,e))}),$.state.next({validatingFields:i.validatingFields,isValidating:!R(i.validatingFields)}))},Z=(e,t)=>{z(i.errors,e,t),$.state.next({errors:i.errors})},N=(e,t,r,i)=>{let o=x(a,e);if(o){let a=x(s,e,_(r)?x(n,e):r);_(a)||i&&i.defaultChecked||t?z(s,e,t?a:ea(o._f)):er(e,a),d.mount&&F()}},T=(e,t,a,s,o)=>{let l=!1,u=!1,d={name:e};if(!r.disabled){if(!a||s){(g.isDirty||w.isDirty)&&(u=i.isDirty,i.isDirty=d.isDirty=Q(),l=u!==d.isDirty);let r=I(x(n,e),t);u=!!x(i.dirtyFields,e),r?H(i.dirtyFields,e):z(i.dirtyFields,e,!0),d.dirtyFields=i.dirtyFields,l=l||(g.dirtyFields||w.dirtyFields)&&!r!==u}if(a){let t=x(i.touchedFields,e);t||(z(i.touchedFields,e,a),d.touchedFields=i.touchedFields,l=l||(g.touchedFields||w.touchedFields)&&t!==a)}l&&o&&$.state.next(d)}return l?d:{}},B=(e,a,n,s)=>{let o=x(i.errors,e),l=(g.isValid||w.isValid)&&k(a)&&i.isValid!==a;if(r.delayError&&n?(t=C(()=>Z(e,n)))(r.delayError):(clearTimeout(h),t=null,n?z(i.errors,e,n):H(i.errors,e)),(n?!I(o,n):o)||!R(s)||l){let t={...s,...l&&k(a)?{isValid:a}:{},errors:i.errors,name:e};i={...i,...t},$.state.next(t)}},K=async e=>{E(e,!0);let t=await r.resolver(s,r.context,en(e||p.mount,a,r.criteriaMode,r.shouldUseNativeValidation));return E(e),t},J=async e=>{let{errors:t}=await K(e);if(e)for(let r of e){let e=x(t,r);e?z(i.errors,r,e):H(i.errors,r)}else i.errors=t;return t},Y=async(e,t,a={valid:!0})=>{for(let n in e){let o=e[n];if(o){let{_f:e,...l}=o;if(e){let l=p.array.has(e.name),u=o._f&&ed(o._f);u&&g.validatingFields&&E([n],!0);let d=await ek(o,p.disabled,s,S,r.shouldUseNativeValidation&&!t,l);if(u&&g.validatingFields&&E([n]),d[e.name]&&(a.valid=!1,t))break;t||(x(d,e.name)?l?e_(i.errors,d,e.name):z(i.errors,e.name,d[e.name]):H(i.errors,e.name))}R(l)||await Y(l,t,a)}}return a.valid},Q=(e,t)=>!r.disabled&&(e&&t&&z(s,e,t),!I(ex(),n)),ee=(e,t,r)=>P(e,p,{...d.mount?s:_(t)?n:V(e)?{[e]:t}:t},r,t),er=(e,t,r={})=>{let i=x(a,e),n=t;if(i){let r=i._f;r&&(r.disabled||z(s,e,et(t,r)),n=L(r.ref)&&u(t)?"":t,q(r.ref)?[...r.ref.options].forEach(e=>e.selected=n.includes(e.value)):r.refs?o(r.ref)?r.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(n)?e.checked=!!n.find(t=>t===e.value):e.checked=n===e.value||!!n)}):r.refs.forEach(e=>e.checked=e.value===n):U(r.ref)?r.ref.value="":(r.ref.value=n,r.ref.type||$.state.next({name:e,values:v(s)})))}(r.shouldDirty||r.shouldTouch)&&T(e,n,r.shouldTouch,r.shouldDirty,!0),r.shouldValidate&&ew(e)},ei=(e,t,r)=>{for(let i in t){if(!t.hasOwnProperty(i))return;let n=t[i],s=e+"."+i,o=x(a,s);(p.array.has(e)||c(n)||o&&!o._f)&&!l(n)?ei(s,n,r):er(s,n,r)}},es=(e,t,r={})=>{let o=x(a,e),l=p.array.has(e),c=v(t);z(s,e,c),l?($.array.next({name:e,values:v(s)}),(g.isDirty||g.dirtyFields||w.isDirty||w.dirtyFields)&&r.shouldDirty&&$.state.next({name:e,dirtyFields:X(n,s),isDirty:Q(e,c)})):!o||o._f||u(c)?er(e,c,r):ei(e,c,r),ef(e,p)&&$.state.next({...i}),$.state.next({name:d.mount?e:void 0,values:v(s)})},eu=async e=>{d.mount=!0;let n=e.target,o=n.name,u=!0,c=x(a,o),m=e=>{u=Number.isNaN(e)||l(e)&&isNaN(e.getTime())||I(e,x(s,o,e))},h=el(r.mode),y=el(r.reValidateMode);if(c){let l,d,_=n.type?ea(c._f):f(e),b=e.type===j.BLUR||e.type===j.FOCUS_OUT,k=!ec(c._f)&&!r.resolver&&!x(i.errors,o)&&!c._f.deps||ev(b,x(i.touchedFields,o),i.isSubmitted,y,h),A=ef(o,p,b);z(s,o,_),b?(c._f.onBlur&&c._f.onBlur(e),t&&t(0)):c._f.onChange&&c._f.onChange(e);let C=T(o,_,b),V=!R(C)||A;if(b||$.state.next({name:o,type:e.type,values:v(s)}),k)return(g.isValid||w.isValid)&&("onBlur"===r.mode?b&&F():b||F()),V&&$.state.next({name:o,...A?{}:C});if(!b&&A&&$.state.next({...i}),r.resolver){let{errors:e}=await K([o]);if(m(_),u){let t=em(i.errors,a,o),r=em(e,a,t.name||o);l=r.error,o=r.name,d=R(e)}}else E([o],!0),l=(await ek(c,p.disabled,s,S,r.shouldUseNativeValidation))[o],E([o]),m(_),u&&(l?d=!1:(g.isValid||w.isValid)&&(d=await Y(a,!0)));u&&(c._f.deps&&ew(c._f.deps),B(o,d,l,C))}},eb=(e,t)=>{if(x(i.errors,t)&&e.focus)return e.focus(),1},ew=async(e,t={})=>{let n,s,o=D(e);if(r.resolver){let t=await J(_(e)?e:o);n=R(t),s=e?!o.some(e=>x(t,e)):n}else e?((s=(await Promise.all(o.map(async e=>{let t=x(a,e);return await Y(t&&t._f?{[e]:t}:t)}))).every(Boolean))||i.isValid)&&F():s=n=await Y(a);return $.state.next({...!V(e)||(g.isValid||w.isValid)&&n!==i.isValid?{}:{name:e},...r.resolver||!e?{isValid:n}:{},errors:i.errors}),t.shouldFocus&&!s&&ep(a,eb,e?o:p.mount),s},ex=e=>{let t={...d.mount?s:n};return _(e)?t:V(e)?x(t,e):e.map(e=>x(t,e))},ej=(e,t)=>({invalid:!!x((t||i).errors,e),isDirty:!!x((t||i).dirtyFields,e),error:x((t||i).errors,e),isValidating:!!x(i.validatingFields,e),isTouched:!!x((t||i).touchedFields,e)}),eA=(e,t,r)=>{let n=(x(a,e,{_f:{}})._f||{}).ref,{ref:s,message:o,type:l,...u}=x(i.errors,e)||{};z(i.errors,e,{...u,...t,ref:n}),$.state.next({name:e,errors:i.errors,isValid:!1}),r&&r.shouldFocus&&n&&n.focus&&n.focus()},e$=e=>$.state.subscribe({next:t=>{ey(e.name,t.name,e.exact)&&eh(t,e.formState||g,eN,e.reRenderRoot)&&e.callback({values:{...s},...i,...t})}}).unsubscribe,eS=(e,t={})=>{for(let o of e?D(e):p.mount)p.mount.delete(o),p.array.delete(o),t.keepValue||(H(a,o),H(s,o)),t.keepError||H(i.errors,o),t.keepDirty||H(i.dirtyFields,o),t.keepTouched||H(i.touchedFields,o),t.keepIsValidating||H(i.validatingFields,o),r.shouldUnregister||t.keepDefaultValue||H(n,o);$.state.next({values:v(s)}),$.state.next({...i,...!t.keepDirty?{}:{isDirty:Q()}}),t.keepIsValid||F()},eC=({disabled:e,name:t})=>{(k(e)&&d.mount||e||p.disabled.has(t))&&(e?p.disabled.add(t):p.disabled.delete(t))},eF=(e,t={})=>{let i=x(a,e),s=k(t.disabled)||k(r.disabled);return z(a,e,{...i||{},_f:{...i&&i._f?i._f:{ref:{name:e}},name:e,mount:!0,...t}}),p.mount.add(e),i?eC({disabled:k(t.disabled)?t.disabled:r.disabled,name:e}):N(e,!0,t.value),{...s?{disabled:t.disabled||r.disabled}:{},...r.progressive?{required:!!t.required,min:eo(t.min),max:eo(t.max),minLength:eo(t.minLength),maxLength:eo(t.maxLength),pattern:eo(t.pattern)}:{},name:e,onChange:eu,onBlur:eu,ref:s=>{if(s){eF(e,t),i=x(a,e);let r=_(s.value)&&s.querySelectorAll&&s.querySelectorAll("input,select,textarea")[0]||s,o=W(r),l=i._f.refs||[];(o?l.find(e=>e===r):r===i._f.ref)||(z(a,e,{_f:{...i._f,...o?{refs:[...l.filter(G),r,...Array.isArray(x(n,e))?[{}]:[]],ref:{type:r.type,name:e}}:{ref:r}}}),N(e,!1,void 0,r))}else(i=x(a,e,{}))._f&&(i._f.mount=!1),(r.shouldUnregister||t.shouldUnregister)&&!(m(p.array,e)&&d.action)&&p.unMount.add(e)}}},eE=()=>r.shouldFocusError&&ep(a,eb,p.mount),eV=(e,t)=>async n=>{let o;n&&(n.preventDefault&&n.preventDefault(),n.persist&&n.persist());let l=v(s);if($.state.next({isSubmitting:!0}),r.resolver){let{errors:e,values:t}=await K();i.errors=e,l=v(t)}else await Y(a);if(p.disabled.size)for(let e of p.disabled)H(l,e);if(H(i.errors,"root"),R(i.errors)){$.state.next({errors:{}});try{await e(l,n)}catch(e){o=e}}else t&&await t({...i.errors},n),eE(),setTimeout(eE);if($.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:R(i.errors)&&!o,submitCount:i.submitCount+1,errors:i.errors}),o)throw o},eP=(e,t={})=>{let o=e?v(e):n,l=v(o),u=R(e),c=u?n:l;if(t.keepDefaultValues||(n=o),!t.keepValues){if(t.keepDirtyValues)for(let e of Array.from(new Set([...p.mount,...Object.keys(X(n,s))])))x(i.dirtyFields,e)?z(c,e,x(s,e)):es(e,x(c,e));else{if(y&&_(e))for(let e of p.mount){let t=x(a,e);if(t&&t._f){let e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(L(e)){let t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(let e of p.mount)es(e,x(c,e));else a={}}s=r.shouldUnregister?t.keepDefaultValues?v(n):{}:v(c),$.array.next({values:{...c}}),$.state.next({values:{...c}})}p={mount:t.keepDirtyValues?p.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},d.mount=!g.isValid||!!t.keepIsValid||!!t.keepDirtyValues,d.watch=!!r.shouldUnregister,$.state.next({submitCount:t.keepSubmitCount?i.submitCount:0,isDirty:!u&&(t.keepDirty?i.isDirty:!!(t.keepDefaultValues&&!I(e,n))),isSubmitted:!!t.keepIsSubmitted&&i.isSubmitted,dirtyFields:u?{}:t.keepDirtyValues?t.keepDefaultValues&&s?X(n,s):i.dirtyFields:t.keepDefaultValues&&e?X(n,e):t.keepDirty?i.dirtyFields:{},touchedFields:t.keepTouched?i.touchedFields:{},errors:t.keepErrors?i.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&i.isSubmitSuccessful,isSubmitting:!1})},eZ=(e,t)=>eP(M(e)?e(s):e,t),eN=e=>{i={...i,...e}},eD={control:{register:eF,unregister:eS,getFieldState:ej,handleSubmit:eV,setError:eA,_subscribe:e$,_runSchema:K,_focusError:eE,_getWatch:ee,_getDirty:Q,_setValid:F,_setFieldArray:(e,t=[],o,l,u=!0,c=!0)=>{if(l&&o&&!r.disabled){if(d.action=!0,c&&Array.isArray(x(a,e))){let t=o(x(a,e),l.argA,l.argB);u&&z(a,e,t)}if(c&&Array.isArray(x(i.errors,e))){let t=o(x(i.errors,e),l.argA,l.argB);u&&z(i.errors,e,t),eg(i.errors,e)}if((g.touchedFields||w.touchedFields)&&c&&Array.isArray(x(i.touchedFields,e))){let t=o(x(i.touchedFields,e),l.argA,l.argB);u&&z(i.touchedFields,e,t)}(g.dirtyFields||w.dirtyFields)&&(i.dirtyFields=X(n,s)),$.state.next({name:e,isDirty:Q(e,t),dirtyFields:i.dirtyFields,errors:i.errors,isValid:i.isValid})}else z(s,e,t)},_setDisabledField:eC,_setErrors:e=>{i.errors=e,$.state.next({errors:i.errors,isValid:!1})},_getFieldArray:e=>b(x(d.mount?s:n,e,r.shouldUnregister?x(n,e,[]):[])),_reset:eP,_resetDefaultValues:()=>M(r.defaultValues)&&r.defaultValues().then(e=>{eZ(e,r.resetOptions),$.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(let e of p.unMount){let t=x(a,e);t&&(t._f.refs?t._f.refs.every(e=>!G(e)):!G(t._f.ref))&&eS(e)}p.unMount=new Set},_disableForm:e=>{k(e)&&($.state.next({disabled:e}),ep(a,(t,r)=>{let i=x(a,r);i&&(t.disabled=i._f.disabled||e,Array.isArray(i._f.refs)&&i._f.refs.forEach(t=>{t.disabled=i._f.disabled||e}))},0,!1))},_subjects:$,_proxyFormState:g,get _fields(){return a},get _formValues(){return s},get _state(){return d},set _state(value){d=value},get _defaultValues(){return n},get _names(){return p},set _names(value){p=value},get _formState(){return i},get _options(){return r},set _options(value){r={...r,...value}}},subscribe:e=>(d.mount=!0,w={...w,...e.formState},e$({...e,formState:w})),trigger:ew,register:eF,handleSubmit:eV,watch:(e,t)=>M(e)?$.state.subscribe({next:r=>e(ee(void 0,t),r)}):ee(e,t,!0),setValue:es,getValues:ex,reset:eZ,resetField:(e,t={})=>{x(a,e)&&(_(t.defaultValue)?es(e,v(x(n,e))):(es(e,t.defaultValue),z(n,e,v(t.defaultValue))),t.keepTouched||H(i.touchedFields,e),t.keepDirty||(H(i.dirtyFields,e),i.isDirty=t.defaultValue?Q(e,v(x(n,e))):Q()),!t.keepError&&(H(i.errors,e),g.isValid&&F()),$.state.next({...i}))},clearErrors:e=>{e&&D(e).forEach(e=>H(i.errors,e)),$.state.next({errors:e?i.errors:{}})},unregister:eS,setError:eA,setFocus:(e,t={})=>{let r=x(a,e),i=r&&r._f;if(i){let e=i.refs?i.refs[0]:i.ref;e.focus&&(e.focus(),t.shouldSelect&&M(e.select)&&e.select())}},getFieldState:ej};return{...eD,formControl:eD}}(e);t.current={...a,formState:i}}let s=t.current.control;return s._options=e,E(()=>{let e=s._subscribe({formState:s._proxyFormState,callback:()=>a({...s._formState}),reRenderRoot:!0});return a(e=>({...e,isReady:!0})),s._formState.isReady=!0,e},[s]),n.useEffect(()=>s._disableForm(e.disabled),[s,e.disabled]),n.useEffect(()=>{e.mode&&(s._options.mode=e.mode),e.reValidateMode&&(s._options.reValidateMode=e.reValidateMode)},[s,e.mode,e.reValidateMode]),n.useEffect(()=>{e.errors&&(s._setErrors(e.errors),s._focusError())},[s,e.errors]),n.useEffect(()=>{e.shouldUnregister&&s._subjects.state.next({values:s._getWatch()})},[s,e.shouldUnregister]),n.useEffect(()=>{if(s._proxyFormState.isDirty){let e=s._getDirty();e!==i.isDirty&&s._subjects.state.next({isDirty:e})}},[s,i.isDirty]),n.useEffect(()=>{e.values&&!I(e.values,r.current)?(s._reset(e.values,{keepFieldsRef:!0,...s._options.resetOptions}),r.current=e.values,a(e=>({...e}))):s._resetDefaultValues()},[s,e.values]),n.useEffect(()=>{s._state.mount||(s._setValid(),s._state.mount=!0),s._state.watch&&(s._state.watch=!1,s._subjects.state.next({...s._formState})),s._removeUnmounted()}),t.current.formState=F(i,s),t.current}({resolver:function(e,t,r){if(void 0===r&&(r={}),"_def"in e&&"object"==typeof e._def&&"typeName"in e._def)return function(t,i,a){try{return Promise.resolve(tt(function(){return Promise.resolve(e["sync"===r.mode?"parse":"parseAsync"](t,void 0)).then(function(e){return a.shouldUseNativeValidation&&eA({},a),{errors:{},values:r.raw?Object.assign({},t):e}})},function(e){if(Array.isArray(null==e?void 0:e.issues))return{values:{},errors:e$(function(e,t){for(var r={};e.length;){var i=e[0],a=i.code,n=i.message,s=i.path.join(".");if(!r[s])if("unionErrors"in i){var o=i.unionErrors[0].errors[0];r[s]={message:o.message,type:o.code}}else r[s]={message:n,type:a};if("unionErrors"in i&&i.unionErrors.forEach(function(t){return t.errors.forEach(function(t){return e.push(t)})}),t){var l=r[s].types,u=l&&l[i.code];r[s]=N(s,t,r,a,u?[].concat(u,i.message):i.message)}e.shift()}return r}(e.errors,!a.shouldUseNativeValidation&&"all"===a.criteriaMode),a)};throw e}))}catch(e){return Promise.reject(e)}};if("_zod"in e&&"object"==typeof e._zod)return function(t,i,a){try{return Promise.resolve(tt(function(){return Promise.resolve(("sync"===r.mode?e4:e3)(e,t,void 0)).then(function(e){return a.shouldUseNativeValidation&&eA({},a),{errors:{},values:r.raw?Object.assign({},t):e}})},function(e){if(e instanceof e1)return{values:{},errors:e$(function(e,t){for(var r={};e.length;){var i=e[0],a=i.code,n=i.message,s=i.path.join(".");if(!r[s])if("invalid_union"===i.code){var o=i.errors[0][0];r[s]={message:o.message,type:o.code}}else r[s]={message:n,type:a};if("invalid_union"===i.code&&i.errors.forEach(function(t){return t.forEach(function(t){return e.push(t)})}),t){var l=r[s].types,u=l&&l[i.code];r[s]=N(s,t,r,a,u?[].concat(u,i.message):i.message)}e.shift()}return r}(e.issues,!a.shouldUseNativeValidation&&"all"===a.criteriaMode),a)};throw e}))}catch(e){return Promise.reject(e)}};throw Error("Invalid input: not a Zod schema")}(i1),defaultValues:{name:"",email:"",subject:"",message:""}}),w=async t=>{r(!0);try{await iK.TP.submit(t),e({title:"Message Sent",description:"Thank you for your message. We will get back to you soon.",variant:"success"}),g.reset()}catch(t){e({title:"Error",description:"There was a problem sending your message. Please try again later.",variant:"destructive"})}finally{r(!1)}};return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold mb-6",children:["Contact ",i]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-12",children:[(0,a.jsxs)(iF.Zp,{className:"col-span-1 md:col-span-2",children:[(0,a.jsxs)(iF.aR,{children:[(0,a.jsx)(iF.ZB,{children:"Send Us a Message"}),(0,a.jsx)(iF.BT,{children:"Fill out the form below and we'll get back to you as soon as possible."})]}),(0,a.jsx)(iF.Wu,{children:(0,a.jsx)(iT,{...g,children:(0,a.jsxs)("form",{onSubmit:g.handleSubmit(w),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(iR,{control:g.control,name:"name",render:({field:e})=>(0,a.jsxs)(iL,{children:[(0,a.jsx)(iq,{children:"Name"}),(0,a.jsx)(iB,{children:(0,a.jsx)(iG.p,{placeholder:"Your name",...e,disabled:t})}),(0,a.jsx)(iW,{})]})}),(0,a.jsx)(iR,{control:g.control,name:"email",render:({field:e})=>(0,a.jsxs)(iL,{children:[(0,a.jsx)(iq,{children:"Email"}),(0,a.jsx)(iB,{children:(0,a.jsx)(iG.p,{type:"email",placeholder:"<EMAIL>",...e,disabled:t})}),(0,a.jsx)(iW,{})]})})]}),(0,a.jsx)(iR,{control:g.control,name:"subject",render:({field:e})=>(0,a.jsxs)(iL,{children:[(0,a.jsx)(iq,{children:"Subject"}),(0,a.jsx)(iB,{children:(0,a.jsx)(iG.p,{placeholder:"Subject of your message",...e,disabled:t})}),(0,a.jsx)(iW,{})]})}),(0,a.jsx)(iR,{control:g.control,name:"message",render:({field:e})=>(0,a.jsxs)(iL,{children:[(0,a.jsx)(iq,{children:"Message"}),(0,a.jsx)(iB,{children:(0,a.jsx)(iH,{placeholder:"Your message",className:"min-h-[150px]",...e,disabled:t})}),(0,a.jsx)(iW,{})]})}),(0,a.jsx)(iE.$,{type:"submit",className:"w-full md:w-auto",disabled:t,children:t?"Sending...":"Send Message"})]})})})]}),(0,a.jsxs)(iF.Zp,{children:[(0,a.jsxs)(iF.aR,{children:[(0,a.jsx)(iF.ZB,{children:"Contact Information"}),(0,a.jsx)(iF.BT,{children:"Get in touch with us directly using the information below."})]}),(0,a.jsxs)(iF.Wu,{className:"space-y-6",children:[p?(0,a.jsx)("div",{className:"flex justify-center py-4",children:(0,a.jsx)(iJ.A,{className:"h-6 w-6 animate-spin text-teal-600"})}):h?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:h.name||i}),h?.address&&(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(iX.A,{className:"h-5 w-5 text-charity-primary mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Address"}),(0,a.jsx)("p",{className:"text-gray-600",children:h.address})]})]}),h?.phone&&(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(iY.A,{className:"h-5 w-5 text-charity-primary mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Phone"}),(0,a.jsx)("p",{className:"text-gray-600",children:h.phone})]})]}),h?.email&&(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(iQ.A,{className:"h-5 w-5 text-charity-primary mt-0.5"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Email"}),(0,a.jsx)("a",{href:`mailto:${h.email}`,className:"text-charity-primary hover:underline",children:h.email})]})]})]}):(0,a.jsx)("div",{className:"text-center py-4 text-gray-500",children:(0,a.jsx)("p",{children:"No office location information available."})}),(0,a.jsxs)("div",{className:"pt-4 border-t",children:[(0,a.jsx)("h4",{className:"font-semibold mb-2",children:"Office Hours"}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("p",{children:"Monday - Friday: 9:00 AM - 5:00 PM"}),(0,a.jsx)("p",{children:"Saturday: 10:00 AM - 2:00 PM"}),(0,a.jsx)("p",{children:"Sunday: Closed"})]})]})]})]})]}),(0,a.jsx)(iF.Zp,{className:"mb-12",children:(0,a.jsx)(iF.Wu,{className:"p-6",children:(0,a.jsx)("div",{className:"aspect-w-16 aspect-h-9 w-full relative",children:p?(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100",children:(0,a.jsx)(iJ.A,{className:"h-8 w-8 animate-spin text-teal-600"})}):h?(0,a.jsx)("iframe",{src:`https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d1000!2d${h.longitude}!3d${h.latitude}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2z${h.latitude}N_${h.longitude}E!5e0!3m2!1sen!2sus!4v1644345114693!5m2!1sen!2sus`,width:"100%",height:"450",style:{border:0},allowFullScreen:!0,loading:"lazy",title:"Location Map"}):(0,a.jsx)("iframe",{src:"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3024.123456789!2d-74.0059413!3d40.7128!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNDDCsDQyJzQ2LjEiTiA3NMKwMDAnMjEuNCJX!5e0!3m2!1sen!2sus!4v1644345114693!5m2!1sen!2sus",width:"100%",height:"450",style:{border:0},allowFullScreen:!0,loading:"lazy",title:"Default Location Map"})})})}),(0,a.jsxs)(iF.Zp,{children:[(0,a.jsxs)(iF.aR,{children:[(0,a.jsx)(iF.ZB,{children:"Frequently Asked Questions"}),(0,a.jsx)(iF.BT,{children:"Find answers to common questions about our services and organization."})]}),(0,a.jsxs)(iF.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"How can I volunteer with your organization?"}),(0,a.jsx)("p",{className:"text-gray-600",children:"We welcome volunteers who want to contribute their time and skills. Please fill out the contact form above with your interest in volunteering, and our volunteer coordinator will get in touch with you."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"How are donations used?"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Donations directly support our programs and services. We ensure that funds are used efficiently to maximize impact in our community. For detailed information, please visit our Donate page."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-lg mb-2",children:"Do you offer internship opportunities?"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Yes, we offer internship opportunities for students and recent graduates. Please contact us with your resume and area of interest for more information."})]}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsxs)("p",{className:"text-gray-600",children:["For more questions and answers, please visit our ",(0,a.jsx)("a",{href:"/faq",className:"text-charity-primary hover:underline",children:"FAQ page"}),"."]})})]})]})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[447,699,62,775],()=>r(51683));module.exports=i})();