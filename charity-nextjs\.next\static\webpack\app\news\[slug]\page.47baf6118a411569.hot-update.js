"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/news/[slug]/page",{

/***/ "(app-pages-browser)/./src/components/ui/api-error.tsx":
/*!*****************************************!*\
  !*** ./src/components/ui/api-error.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n\n\n\n\n\nfunction getErrorMessage(error) {\n    var _error_response_data, _error_response;\n    if (typeof error === 'string') return error;\n    if (error === null || error === void 0 ? void 0 : error.message) return error.message;\n    if (error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) return error.response.data.message;\n    return 'An unexpected error occurred';\n}\nfunction ApiError(param) {\n    let { title = \"An error occurred\", description, error, onRetry, resetErrorBoundary, variant = 'alert' } = param;\n    const errorMessage = getErrorMessage(error);\n    const displayDescription = description || errorMessage;\n    const handleRetry = ()=>{\n        if (onRetry) {\n            onRetry();\n        }\n        if (resetErrorBoundary) {\n            resetErrorBoundary();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n        className: \"border-red-200 bg-red-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                        className: \"text-red-800 flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-5 w-5 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\api-error.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            title\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\api-error.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                        className: \"text-red-700\",\n                        children: displayDescription\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\api-error.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\api-error.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this),\n            (onRetry || resetErrorBoundary) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"outline\",\n                    size: \"sm\",\n                    onClick: handleRetry,\n                    className: \"border-red-300 text-red-700 hover:bg-red-100\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\api-error.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this),\n                        \"Try Again\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\api-error.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\api-error.tsx\",\n                lineNumber: 54,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\api-error.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_c = ApiError;\nvar _c;\n$RefreshReg$(_c, \"ApiError\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/api-error.tsx\n"));

/***/ })

});