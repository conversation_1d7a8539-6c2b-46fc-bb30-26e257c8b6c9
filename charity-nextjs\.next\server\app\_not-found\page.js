/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"548041302bb1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFzaWJcXE9uZURyaXZlXFxEZXNrdG9wXFxjaGFyaXR5X2luZm9cXGNoYXJpdHlfaW5mb1xcY2hhcml0eS1uZXh0anNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1NDgwNDEzMDJiYjFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/QueryProvider */ \"(rsc)/./src/components/providers/QueryProvider.tsx\");\n/* harmony import */ var _components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/seo/StructuredData */ \"(rsc)/./src/components/seo/StructuredData.tsx\");\n/* harmony import */ var _components_performance_WebVitals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/performance/WebVitals */ \"(rsc)/./src/components/performance/WebVitals.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Charity Welcome Hub - Supporting Communities Through Action\",\n        template: \"%s | Charity Welcome Hub\"\n    },\n    description: \"Join our mission to support communities through charitable work, transparency, and meaningful impact. Discover our programs, news, and ways to get involved.\",\n    keywords: [\n        \"charity\",\n        \"community\",\n        \"support\",\n        \"donations\",\n        \"volunteer\",\n        \"nonprofit\",\n        \"humanitarian\",\n        \"social impact\",\n        \"community service\",\n        \"charitable organization\",\n        \"helping others\",\n        \"social good\"\n    ],\n    authors: [\n        {\n            name: \"Charity Welcome Hub\"\n        }\n    ],\n    creator: \"Charity Welcome Hub\",\n    publisher: \"Charity Welcome Hub\",\n    metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),\n    alternates: {\n        canonical: '/'\n    },\n    openGraph: {\n        title: \"Charity Welcome Hub - Supporting Communities Through Action\",\n        description: \"Join our mission to support communities through charitable work, transparency, and meaningful impact.\",\n        type: \"website\",\n        locale: \"en_US\",\n        url: '/',\n        siteName: \"Charity Welcome Hub\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Charity Welcome Hub - Supporting Communities Through Action\",\n        description: \"Join our mission to support communities through charitable work, transparency, and meaningful impact.\",\n        creator: \"@CharityWelcomeHub\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_5__.OrganizationStructuredData, {\n                        name: \"Charity Welcome Hub\",\n                        description: \"Supporting communities through charitable work, transparency, and meaningful impact\",\n                        url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',\n                        contactPoint: {\n                            telephone: \"******-0123\",\n                            contactType: \"customer service\",\n                            email: \"<EMAIL>\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_5__.WebsiteStructuredData, {\n                        name: \"Charity Welcome Hub\",\n                        description: \"Supporting communities through charitable work, transparency, and meaningful impact\",\n                        url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-background font-sans antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_WebVitals__WEBPACK_IMPORTED_MODULE_6__.WebVitals, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_4__.QueryProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col min-h-screen\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"flex-1\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(rsc)/./src/components/ui/skeleton.tsx\");\n\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-8 w-64 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-4 w-96\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: [\n                    ...Array(6)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                className: \"h-48 w-full rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-3/4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\"],\n'loading': [module2, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\"],\n'not-found': [module3, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(rsc)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(rsc)/./src/components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/WebVitals.tsx */ \"(rsc)/./src/components/performance/WebVitals.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/QueryProvider.tsx */ \"(rsc)/./src/components/providers/QueryProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ Footer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Footer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Footer.tsx",
"Footer",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./src/components/performance/WebVitals.tsx":
/*!**************************************************!*\
  !*** ./src/components/performance/WebVitals.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PageLoadMetrics: () => (/* binding */ PageLoadMetrics),
/* harmony export */   WebVitals: () => (/* binding */ WebVitals),
/* harmony export */   usePerformanceMetric: () => (/* binding */ usePerformanceMetric)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const WebVitals = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx",
"WebVitals",
);const usePerformanceMetric = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call usePerformanceMetric() from the server but usePerformanceMetric is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx",
"usePerformanceMetric",
);const PageLoadMetrics = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PageLoadMetrics() from the server but PageLoadMetrics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx",
"PageLoadMetrics",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const QueryProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\providers\\QueryProvider.tsx",
"QueryProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/seo/StructuredData.tsx":
/*!***********************************************!*\
  !*** ./src/components/seo/StructuredData.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BreadcrumbStructuredData: () => (/* binding */ BreadcrumbStructuredData),\n/* harmony export */   NewsStructuredData: () => (/* binding */ NewsStructuredData),\n/* harmony export */   OrganizationStructuredData: () => (/* binding */ OrganizationStructuredData),\n/* harmony export */   WebsiteStructuredData: () => (/* binding */ WebsiteStructuredData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction OrganizationStructuredData({ name, description, url, logo, contactPoint }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'Organization',\n        name,\n        description,\n        url,\n        ...logo && {\n            logo\n        },\n        ...contactPoint && {\n            contactPoint\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\nfunction NewsStructuredData({ article, organizationName, organizationUrl }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'NewsArticle',\n        headline: article.title,\n        description: article.body.substring(0, 160),\n        datePublished: article.publishDate,\n        author: {\n            '@type': 'Organization',\n            name: organizationName,\n            url: organizationUrl\n        },\n        publisher: {\n            '@type': 'Organization',\n            name: organizationName,\n            url: organizationUrl\n        },\n        mainEntityOfPage: {\n            '@type': 'WebPage',\n            '@id': `${organizationUrl}/news/${article.slug}`\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\nfunction BreadcrumbStructuredData({ items }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'BreadcrumbList',\n        itemListElement: items.map((item, index)=>({\n                '@type': 'ListItem',\n                position: index + 1,\n                name: item.name,\n                item: item.url\n            }))\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\nfunction WebsiteStructuredData({ name, description, url }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'WebSite',\n        name,\n        description,\n        url,\n        potentialAction: {\n            '@type': 'SearchAction',\n            target: {\n                '@type': 'EntryPoint',\n                urlTemplate: `${url}/search?q={search_term_string}`\n            },\n            'query-input': 'required name=search_term_string'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/seo/StructuredData.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/skeleton.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/skeleton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9za2VsZXRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFFaEMsU0FBU0MsU0FBUyxFQUNoQkMsU0FBUyxFQUNULEdBQUdDLE9BQ2tDO0lBQ3JDLHFCQUNFLDhEQUFDQztRQUNDRixXQUFXRiw4Q0FBRUEsQ0FBQyxxQ0FBcUNFO1FBQ2xELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBRW1CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhhc2liXFxPbmVEcml2ZVxcRGVza3RvcFxcY2hhcml0eV9pbmZvXFxjaGFyaXR5X2luZm9cXGNoYXJpdHktbmV4dGpzXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxza2VsZXRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBTa2VsZXRvbih7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50Pikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT17Y24oXCJhbmltYXRlLXB1bHNlIHJvdW5kZWQtbWQgYmctbXV0ZWRcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IFNrZWxldG9uIH1cbiJdLCJuYW1lcyI6WyJjbiIsIlNrZWxldG9uIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJkaXYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/skeleton.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   logInfo: () => (/* binding */ logInfo),\n/* harmony export */   logWarning: () => (/* binding */ logWarning),\n/* harmony export */   parseNewsContent: () => (/* binding */ parseNewsContent),\n/* harmony export */   sanitizeData: () => (/* binding */ sanitizeData)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Parse and format tags in news content\n * Converts text in the format \"**Tags:** tag1, tag2, tag3\" to HTML tags\n * @param content - The HTML content to parse\n * @returns Formatted HTML content with tags\n */ function parseNewsContent(content) {\n    // Regular expression to match tag format: **Tags:** tag1, tag2, tag3\n    const tagRegex = /\\*\\*Tags:\\*\\*\\s*([^<]+)(?=<\\/p>|$)/g;\n    // Replace matched tag sections with formatted tags\n    return content.replace(tagRegex, (match, tagList)=>{\n        // Split the tag list by commas and trim whitespace\n        const tags = tagList.split(',').map((tag)=>tag.trim()).filter(Boolean);\n        if (tags.length === 0) {\n            return match; // No valid tags found, return original text\n        }\n        // Create HTML for tags\n        const tagsHtml = tags.map((tag)=>`<span class=\"inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2\">${tag}</span>`).join('');\n        return `<div class=\"mt-4\"><span class=\"font-semibold\">Tags:</span> <div class=\"flex flex-wrap mt-1\">${tagsHtml}</div></div>`;\n    });\n}\n/**\n * Sanitize sensitive data before logging\n * Removes passwords, tokens, etc.\n */ const sanitizeData = (data)=>{\n    if (!data || typeof data !== 'object') return data;\n    // Create a shallow copy to avoid modifying the original\n    const sanitized = {\n        ...data\n    };\n    // List of sensitive fields to mask\n    const sensitiveFields = [\n        'password',\n        'passwordHash',\n        'token',\n        'secret',\n        'apiKey',\n        'authorization',\n        'accessToken',\n        'refreshToken',\n        'csrf',\n        'cookie',\n        'session',\n        'key',\n        'credential',\n        'auth'\n    ];\n    // Mask sensitive fields\n    Object.keys(sanitized).forEach((key)=>{\n        const lowerKey = key.toLowerCase();\n        if (sensitiveFields.some((field)=>lowerKey.includes(field))) {\n            sanitized[key] = '[REDACTED]';\n        } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {\n            // Recursively sanitize nested objects\n            sanitized[key] = sanitizeData(sanitized[key]);\n        }\n    });\n    return sanitized;\n};\n/**\n * Log information to the console in development mode\n */ const logInfo = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.log(message, sanitizeData(data));\n        } else {\n            console.log(message);\n        }\n    }\n};\n/**\n * Log errors to the console\n * Always logs in production, but sanitizes sensitive data\n */ const logError = (message, error)=>{\n    if (error instanceof Error) {\n        // For Error objects, log the message and stack\n        console.error(message, {\n            message: error.message,\n            stack: error.stack\n        });\n    } else if (error) {\n        // For other data, sanitize before logging\n        console.error(message, sanitizeData(error));\n    } else {\n        console.error(message);\n    }\n};\n/**\n * Log warnings to the console in development mode\n */ const logWarning = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.warn(message, sanitizeData(data));\n        } else {\n            console.warn(message);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/WebVitals.tsx */ \"(ssr)/./src/components/performance/WebVitals.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/QueryProvider.tsx */ \"(ssr)/./src/components/providers/QueryProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rss.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\n// import { locationsApi } from \"@/services/api\";\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    // Temporary: Comment out API call for migration\n    // const { data: locationsData, isLoading: isLoadingLocations } = useQuery({\n    //   queryKey: ['locations'],\n    //   queryFn: () => locationsApi.getAll({ active: true }),\n    //   staleTime: 5 * 60 * 1000, // 5 minutes\n    // });\n    // Temporary mock data for migration\n    const isLoadingLocations = false;\n    const locationsData = {\n        locations: [\n            {\n                isMainOffice: true,\n                phone: \"+****************\",\n                email: \"<EMAIL>\",\n                address: \"123 Charity Street, City, State 12345\"\n            }\n        ]\n    };\n    // Find the main office location\n    const mainOffice = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"Footer.useMemo[mainOffice]\": ()=>{\n            if (!locationsData?.locations) return null;\n            return locationsData.locations.find({\n                \"Footer.useMemo[mainOffice]\": (loc)=>loc.isMainOffice\n            }[\"Footer.useMemo[mainOffice]\"]) || locationsData.locations[0];\n        }\n    }[\"Footer.useMemo[mainOffice]\"], [\n        locationsData\n    ]);\n    // Show placeholder content while loading\n    if (isLoadingLocations) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n            className: \"bg-teal-50 border-t border-teal-100 py-10 mt-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 text-center text-teal-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading footer content...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-teal-50 border-t border-teal-100 py-10 mt-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold text-xl mb-4 text-teal-800\",\n                                    children: \"Charity Info\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-teal-700 mb-4\",\n                                    children: \"Supporting communities and making a difference\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        mainOffice?.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-700 hover:text-teal-900 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: mainOffice.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this),\n                                        mainOffice?.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-700 hover:text-teal-900 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: `mailto:${mainOffice.email}`,\n                                                    className: \"hover:text-teal-900\",\n                                                    children: mainOffice.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        mainOffice?.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-700 hover:text-teal-900 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: mainOffice.address\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-bold mb-4 text-teal-800\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/news\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"News\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/gallery\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Gallery\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/about\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"About Us\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Contact Us\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/search\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Search\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-bold mb-4 text-teal-800\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/faq\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"FAQ\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/donate\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"How to Donate\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-6 border-t border-teal-100 text-center text-teal-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"\\xa9 \",\n                                currentYear,\n                                \" Charity Info. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 flex items-center justify-center text-sm\",\n                            children: [\n                                \"Built with\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mx-1 text-pink-500 fill-pink-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                \" for charitable causes\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const { isAuthenticated, user, logout } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Temporary auth state for migration\n    const isAuthenticated = false;\n    const user = null;\n    const logout = ()=>{};\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (searchQuery.trim()) {\n            router.push(`/search?q=${encodeURIComponent(searchQuery)}`);\n            setSearchQuery(\"\");\n            setIsMenuOpen(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 bg-charity-muted border-b border-gray-200 shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-4 flex items-center justify-between bg-charity-muted\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"font-bold text-2xl text-primary mr-4\",\n                            children: \"Charity Info\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center space-x-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/news\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"News\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/gallery\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"Gallery\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/about\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"About Us\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        type: \"search\",\n                                        placeholder: \"Search...\",\n                                        className: \"w-64 pr-10\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        size: \"icon\",\n                                        variant: \"ghost\",\n                                        className: \"absolute right-0 top-0 h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/admin\",\n                                        className: \"text-gray-700 hover:text-primary font-medium\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>logout(),\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" Login\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        className: \"md:hidden\",\n                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 25\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 53\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-charity-muted border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"container mx-auto px-4 py-4 space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/news\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"News\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/gallery\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"Gallery\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/about\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"About Us\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/contact\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"Contact\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSearch,\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    type: \"search\",\n                                    placeholder: \"Search...\",\n                                    className: \"w-full pr-10\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    size: \"icon\",\n                                    variant: \"ghost\",\n                                    className: \"absolute right-0 top-0 h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this),\n                        isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin\",\n                                    className: \"block text-gray-700 hover:text-primary font-medium\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        logout();\n                                        setIsMenuOpen(false);\n                                    },\n                                    className: \"w-full\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            asChild: true,\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, this),\n                                    \" Login\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/performance/WebVitals.tsx":
/*!**************************************************!*\
  !*** ./src/components/performance/WebVitals.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageLoadMetrics: () => (/* binding */ PageLoadMetrics),\n/* harmony export */   WebVitals: () => (/* binding */ WebVitals),\n/* harmony export */   usePerformanceMetric: () => (/* binding */ usePerformanceMetric)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var web_vitals__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! web-vitals */ \"(ssr)/./node_modules/web-vitals/dist/web-vitals.js\");\n/* __next_internal_client_entry_do_not_use__ WebVitals,usePerformanceMetric,PageLoadMetrics auto */ \n\nfunction sendToAnalytics(metric) {\n    // In production, you would send this to your analytics service\n    // For now, we'll just log to console in development\n    if (true) {\n        console.log('Web Vital:', metric);\n    }\n// Example: Send to Google Analytics\n// gtag('event', metric.name, {\n//   event_category: 'Web Vitals',\n//   event_label: metric.id,\n//   value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),\n//   non_interaction: true,\n// });\n// Example: Send to custom analytics endpoint\n// fetch('/api/analytics/web-vitals', {\n//   method: 'POST',\n//   headers: { 'Content-Type': 'application/json' },\n//   body: JSON.stringify(metric),\n// });\n}\nfunction WebVitals() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"WebVitals.useEffect\": ()=>{\n            // Measure Core Web Vitals\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onCLS)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onINP)(sendToAnalytics); // INP replaced FID\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onFCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onLCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onTTFB)(sendToAnalytics);\n        }\n    }[\"WebVitals.useEffect\"], []);\n    return null; // This component doesn't render anything\n}\n// Hook for measuring custom metrics\nfunction usePerformanceMetric(name, value) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceMetric.useEffect\": ()=>{\n            const metric = {\n                name,\n                value,\n                id: `${name}-${Date.now()}`,\n                delta: value,\n                entries: []\n            };\n            sendToAnalytics(metric);\n        }\n    }[\"usePerformanceMetric.useEffect\"], [\n        name,\n        value\n    ]);\n}\n// Component for measuring page load performance\nfunction PageLoadMetrics({ pageName }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"PageLoadMetrics.useEffect\": ()=>{\n            const startTime = performance.now();\n            return ({\n                \"PageLoadMetrics.useEffect\": ()=>{\n                    const endTime = performance.now();\n                    const loadTime = endTime - startTime;\n                    const metric = {\n                        name: 'page_load_time',\n                        value: loadTime,\n                        id: `${pageName}-${Date.now()}`,\n                        delta: loadTime,\n                        entries: [],\n                        page: pageName\n                    };\n                    sendToAnalytics(metric);\n                }\n            })[\"PageLoadMetrics.useEffect\"];\n        }\n    }[\"PageLoadMetrics.useEffect\"], [\n        pageName\n    ]);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/performance/WebVitals.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n\n\nfunction QueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"QueryProvider.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        staleTime: 60 * 1000,\n                        retry: 1\n                    }\n                }\n            })\n    }[\"QueryProvider.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\providers\\\\QueryProvider.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvUXVlcnlQcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFeUU7QUFDeEM7QUFFMUIsU0FBU0csY0FBYyxFQUFFQyxRQUFRLEVBQWlDO0lBQ3ZFLE1BQU0sQ0FBQ0MsWUFBWSxHQUFHSCwrQ0FBUUE7a0NBQzVCLElBQ0UsSUFBSUYsOERBQVdBLENBQUM7Z0JBQ2RNLGdCQUFnQjtvQkFDZEMsU0FBUzt3QkFDUEMsV0FBVyxLQUFLO3dCQUNoQkMsT0FBTztvQkFDVDtnQkFDRjtZQUNGOztJQUdKLHFCQUNFLDhEQUFDUixzRUFBbUJBO1FBQUNTLFFBQVFMO2tCQUMxQkQ7Ozs7OztBQUdQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhhc2liXFxPbmVEcml2ZVxcRGVza3RvcFxcY2hhcml0eV9pbmZvXFxjaGFyaXR5X2luZm9cXGNoYXJpdHktbmV4dGpzXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVyc1xcUXVlcnlQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxuZXhwb3J0IGZ1bmN0aW9uIFF1ZXJ5UHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCBbcXVlcnlDbGllbnRdID0gdXNlU3RhdGUoXG4gICAgKCkgPT5cbiAgICAgIG5ldyBRdWVyeUNsaWVudCh7XG4gICAgICAgIGRlZmF1bHRPcHRpb25zOiB7XG4gICAgICAgICAgcXVlcmllczoge1xuICAgICAgICAgICAgc3RhbGVUaW1lOiA2MCAqIDEwMDAsIC8vIDEgbWludXRlXG4gICAgICAgICAgICByZXRyeTogMSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSlcbiAgKTtcblxuICByZXR1cm4gKFxuICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvUXVlcnlDbGllbnRQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJRdWVyeUNsaWVudCIsIlF1ZXJ5Q2xpZW50UHJvdmlkZXIiLCJ1c2VTdGF0ZSIsIlF1ZXJ5UHJvdmlkZXIiLCJjaGlsZHJlbiIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyaWVzIiwic3RhbGVUaW1lIiwicmV0cnkiLCJjbGllbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/QueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200\", {\n    variants: {\n        variant: {\n            default: \"bg-charity-primary text-white hover:bg-charity-secondary\",\n            destructive: \"bg-charity-destructive text-white hover:bg-charity-destructive/90\",\n            outline: \"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-charity-light text-charity-dark hover:bg-charity-light/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-charity-primary underline-offset-4 hover:underline\",\n            accent: \"bg-charity-accent text-charity-dark hover:bg-charity-accent/80\",\n            success: \"bg-charity-success text-white hover:bg-charity-success/90\",\n            warning: \"bg-charity-warning text-white hover:bg-charity-warning/90\",\n            info: \"bg-charity-info text-white hover:bg-charity-info/90\",\n            donate: \"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3 py-1.5 text-xs\",\n            lg: \"h-11 rounded-md px-8 py-2.5\",\n            xl: \"h-12 rounded-md px-10 py-3 text-base\",\n            icon: \"h-10 w-10 rounded-full\",\n            wide: \"h-10 px-8 py-2 w-full\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 53,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4QixrWkFDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FBUztZQUNUQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07WUFDTkMsUUFBUTtZQUNSQyxTQUFTO1lBQ1RDLFNBQVM7WUFDVEMsTUFBTTtZQUNOQyxRQUFRO1FBQ1Y7UUFDQUMsTUFBTTtZQUNKWCxTQUFTO1lBQ1RZLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsTUFBTTtRQUNSO0lBQ0Y7SUFDQUMsaUJBQWlCO1FBQ2ZsQixTQUFTO1FBQ1RZLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTU8sdUJBQVN6Qiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFMkIsU0FBUyxFQUFFckIsT0FBTyxFQUFFWSxJQUFJLEVBQUVVLFVBQVUsS0FBSyxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDeEQsTUFBTUMsT0FBT0gsVUFBVTNCLHNEQUFJQSxHQUFHO0lBQzlCLHFCQUNFLDhEQUFDOEI7UUFDQ0osV0FBV3hCLDhDQUFFQSxDQUFDQyxlQUFlO1lBQUVFO1lBQVNZO1lBQU1TO1FBQVU7UUFDeERHLEtBQUtBO1FBQ0osR0FBR0QsS0FBSzs7Ozs7O0FBR2Y7QUFFRkosT0FBT08sV0FBVyxHQUFHO0FBRVkiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFzaWJcXE9uZURyaXZlXFxEZXNrdG9wXFxjaGFyaXR5X2luZm9cXGNoYXJpdHlfaW5mb1xcY2hhcml0eS1uZXh0anNcXHNyY1xcY29tcG9uZW50c1xcdWlcXGJ1dHRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFNsb3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNsb3RcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGJ1dHRvblZhcmlhbnRzID0gY3ZhKFxuICBcImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiB3aGl0ZXNwYWNlLW5vd3JhcCByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gcmluZy1vZmZzZXQtYmFja2dyb3VuZCB0cmFuc2l0aW9uLWNvbG9ycyBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwIFsmX3N2Z106cG9pbnRlci1ldmVudHMtbm9uZSBbJl9zdmddOnNpemUtNCBbJl9zdmddOnNocmluay0wIHNoYWRvdy1zbSBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCIsXG4gIHtcbiAgICB2YXJpYW50czoge1xuICAgICAgdmFyaWFudDoge1xuICAgICAgICBkZWZhdWx0OiBcImJnLWNoYXJpdHktcHJpbWFyeSB0ZXh0LXdoaXRlIGhvdmVyOmJnLWNoYXJpdHktc2Vjb25kYXJ5XCIsXG4gICAgICAgIGRlc3RydWN0aXZlOlxuICAgICAgICAgIFwiYmctY2hhcml0eS1kZXN0cnVjdGl2ZSB0ZXh0LXdoaXRlIGhvdmVyOmJnLWNoYXJpdHktZGVzdHJ1Y3RpdmUvOTBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBib3JkZXItaW5wdXQgYmctdHJhbnNwYXJlbnQgdGV4dC1mb3JlZ3JvdW5kIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIHNlY29uZGFyeTpcbiAgICAgICAgICBcImJnLWNoYXJpdHktbGlnaHQgdGV4dC1jaGFyaXR5LWRhcmsgaG92ZXI6YmctY2hhcml0eS1saWdodC84MFwiLFxuICAgICAgICBnaG9zdDogXCJob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBsaW5rOiBcInRleHQtY2hhcml0eS1wcmltYXJ5IHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcbiAgICAgICAgYWNjZW50OiBcImJnLWNoYXJpdHktYWNjZW50IHRleHQtY2hhcml0eS1kYXJrIGhvdmVyOmJnLWNoYXJpdHktYWNjZW50LzgwXCIsXG4gICAgICAgIHN1Y2Nlc3M6IFwiYmctY2hhcml0eS1zdWNjZXNzIHRleHQtd2hpdGUgaG92ZXI6YmctY2hhcml0eS1zdWNjZXNzLzkwXCIsXG4gICAgICAgIHdhcm5pbmc6IFwiYmctY2hhcml0eS13YXJuaW5nIHRleHQtd2hpdGUgaG92ZXI6YmctY2hhcml0eS13YXJuaW5nLzkwXCIsXG4gICAgICAgIGluZm86IFwiYmctY2hhcml0eS1pbmZvIHRleHQtd2hpdGUgaG92ZXI6YmctY2hhcml0eS1pbmZvLzkwXCIsXG4gICAgICAgIGRvbmF0ZTogXCJiZy1ncmFkaWVudC10by1yIGZyb20tY2hhcml0eS1wcmltYXJ5IHRvLWNoYXJpdHktc2Vjb25kYXJ5IHRleHQtd2hpdGUgaG92ZXI6YnJpZ2h0bmVzcy0xMDVcIixcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaC0xMCBweC00IHB5LTJcIixcbiAgICAgICAgc206IFwiaC05IHJvdW5kZWQtbWQgcHgtMyBweS0xLjUgdGV4dC14c1wiLFxuICAgICAgICBsZzogXCJoLTExIHJvdW5kZWQtbWQgcHgtOCBweS0yLjVcIixcbiAgICAgICAgeGw6IFwiaC0xMiByb3VuZGVkLW1kIHB4LTEwIHB5LTMgdGV4dC1iYXNlXCIsXG4gICAgICAgIGljb246IFwiaC0xMCB3LTEwIHJvdW5kZWQtZnVsbFwiLFxuICAgICAgICB3aWRlOiBcImgtMTAgcHgtOCBweS0yIHctZnVsbFwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmV4cG9ydCBpbnRlcmZhY2UgQnV0dG9uUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5CdXR0b25IVE1MQXR0cmlidXRlczxIVE1MQnV0dG9uRWxlbWVudD4sXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4ge1xuICBhc0NoaWxkPzogYm9vbGVhblxufVxuXG5jb25zdCBCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBCdXR0b25Qcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdmFyaWFudCwgc2l6ZSwgYXNDaGlsZCA9IGZhbHNlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICBjb25zdCBDb21wID0gYXNDaGlsZCA/IFNsb3QgOiBcImJ1dHRvblwiXG4gICAgcmV0dXJuIChcbiAgICAgIDxDb21wXG4gICAgICAgIGNsYXNzTmFtZT17Y24oYnV0dG9uVmFyaWFudHMoeyB2YXJpYW50LCBzaXplLCBjbGFzc05hbWUgfSkpfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbkJ1dHRvbi5kaXNwbGF5TmFtZSA9IFwiQnV0dG9uXCJcblxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJTbG90IiwiY3ZhIiwiY24iLCJidXR0b25WYXJpYW50cyIsInZhcmlhbnRzIiwidmFyaWFudCIsImRlZmF1bHQiLCJkZXN0cnVjdGl2ZSIsIm91dGxpbmUiLCJzZWNvbmRhcnkiLCJnaG9zdCIsImxpbmsiLCJhY2NlbnQiLCJzdWNjZXNzIiwid2FybmluZyIsImluZm8iLCJkb25hdGUiLCJzaXplIiwic20iLCJsZyIsInhsIiwiaWNvbiIsIndpZGUiLCJkZWZhdWx0VmFyaWFudHMiLCJCdXR0b24iLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwiYXNDaGlsZCIsInByb3BzIiwicmVmIiwiQ29tcCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border-[1px] border-input bg-background px-3 py-2 text-base text-foreground ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground/60 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-charity-primary focus-visible:border-charity-primary focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200 md:text-sm shadow-none\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   logInfo: () => (/* binding */ logInfo),\n/* harmony export */   logWarning: () => (/* binding */ logWarning),\n/* harmony export */   parseNewsContent: () => (/* binding */ parseNewsContent),\n/* harmony export */   sanitizeData: () => (/* binding */ sanitizeData)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Parse and format tags in news content\n * Converts text in the format \"**Tags:** tag1, tag2, tag3\" to HTML tags\n * @param content - The HTML content to parse\n * @returns Formatted HTML content with tags\n */ function parseNewsContent(content) {\n    // Regular expression to match tag format: **Tags:** tag1, tag2, tag3\n    const tagRegex = /\\*\\*Tags:\\*\\*\\s*([^<]+)(?=<\\/p>|$)/g;\n    // Replace matched tag sections with formatted tags\n    return content.replace(tagRegex, (match, tagList)=>{\n        // Split the tag list by commas and trim whitespace\n        const tags = tagList.split(',').map((tag)=>tag.trim()).filter(Boolean);\n        if (tags.length === 0) {\n            return match; // No valid tags found, return original text\n        }\n        // Create HTML for tags\n        const tagsHtml = tags.map((tag)=>`<span class=\"inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2\">${tag}</span>`).join('');\n        return `<div class=\"mt-4\"><span class=\"font-semibold\">Tags:</span> <div class=\"flex flex-wrap mt-1\">${tagsHtml}</div></div>`;\n    });\n}\n/**\n * Sanitize sensitive data before logging\n * Removes passwords, tokens, etc.\n */ const sanitizeData = (data)=>{\n    if (!data || typeof data !== 'object') return data;\n    // Create a shallow copy to avoid modifying the original\n    const sanitized = {\n        ...data\n    };\n    // List of sensitive fields to mask\n    const sensitiveFields = [\n        'password',\n        'passwordHash',\n        'token',\n        'secret',\n        'apiKey',\n        'authorization',\n        'accessToken',\n        'refreshToken',\n        'csrf',\n        'cookie',\n        'session',\n        'key',\n        'credential',\n        'auth'\n    ];\n    // Mask sensitive fields\n    Object.keys(sanitized).forEach((key)=>{\n        const lowerKey = key.toLowerCase();\n        if (sensitiveFields.some((field)=>lowerKey.includes(field))) {\n            sanitized[key] = '[REDACTED]';\n        } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {\n            // Recursively sanitize nested objects\n            sanitized[key] = sanitizeData(sanitized[key]);\n        }\n    });\n    return sanitized;\n};\n/**\n * Log information to the console in development mode\n */ const logInfo = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.log(message, sanitizeData(data));\n        } else {\n            console.log(message);\n        }\n    }\n};\n/**\n * Log errors to the console\n * Always logs in production, but sanitizes sensitive data\n */ const logError = (message, error)=>{\n    if (error instanceof Error) {\n        // For Error objects, log the message and stack\n        console.error(message, {\n            message: error.message,\n            stack: error.stack\n        });\n    } else if (error) {\n        // For other data, sanitize before logging\n        console.error(message, sanitizeData(error));\n    } else {\n        console.error(message);\n    }\n};\n/**\n * Log warnings to the console in development mode\n */ const logWarning = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.warn(message, sanitizeData(data));\n        } else {\n            console.warn(message);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRUE7Ozs7O0NBS0MsR0FDTSxTQUFTQyxpQkFBaUJDLE9BQWU7SUFDOUMscUVBQXFFO0lBQ3JFLE1BQU1DLFdBQVc7SUFFakIsbURBQW1EO0lBQ25ELE9BQU9ELFFBQVFFLE9BQU8sQ0FBQ0QsVUFBVSxDQUFDRSxPQUFPQztRQUN2QyxtREFBbUQ7UUFDbkQsTUFBTUMsT0FBT0QsUUFBUUUsS0FBSyxDQUFDLEtBQUtDLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUMsSUFBSSxJQUFJQyxNQUFNLENBQUNDO1FBRTlELElBQUlOLEtBQUtPLE1BQU0sS0FBSyxHQUFHO1lBQ3JCLE9BQU9ULE9BQU8sNENBQTRDO1FBQzVEO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1VLFdBQVdSLEtBQUtFLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFDeEIsQ0FBQywwSEFBMEgsRUFBRUEsSUFBSSxPQUFPLENBQUMsRUFDeklNLElBQUksQ0FBQztRQUVQLE9BQU8sQ0FBQyw0RkFBNEYsRUFBRUQsU0FBUyxZQUFZLENBQUM7SUFDOUg7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLE1BQU1FLGVBQWUsQ0FBQ0M7SUFDM0IsSUFBSSxDQUFDQSxRQUFRLE9BQU9BLFNBQVMsVUFBVSxPQUFPQTtJQUU5Qyx3REFBd0Q7SUFDeEQsTUFBTUMsWUFBWTtRQUFFLEdBQUdELElBQUk7SUFBQztJQUU1QixtQ0FBbUM7SUFDbkMsTUFBTUUsa0JBQWtCO1FBQ3RCO1FBQVk7UUFBZ0I7UUFBUztRQUFVO1FBQy9DO1FBQWlCO1FBQWU7UUFBZ0I7UUFDaEQ7UUFBVTtRQUFXO1FBQU87UUFBYztLQUMzQztJQUVELHdCQUF3QjtJQUN4QkMsT0FBT0MsSUFBSSxDQUFDSCxXQUFXSSxPQUFPLENBQUNDLENBQUFBO1FBQzdCLE1BQU1DLFdBQVdELElBQUlFLFdBQVc7UUFDaEMsSUFBSU4sZ0JBQWdCTyxJQUFJLENBQUNDLENBQUFBLFFBQVNILFNBQVNJLFFBQVEsQ0FBQ0QsU0FBUztZQUMzRFQsU0FBUyxDQUFDSyxJQUFJLEdBQUc7UUFDbkIsT0FBTyxJQUFJLE9BQU9MLFNBQVMsQ0FBQ0ssSUFBSSxLQUFLLFlBQVlMLFNBQVMsQ0FBQ0ssSUFBSSxLQUFLLE1BQU07WUFDeEUsc0NBQXNDO1lBQ3RDTCxTQUFTLENBQUNLLElBQUksR0FBR1AsYUFBYUUsU0FBUyxDQUFDSyxJQUFJO1FBQzlDO0lBQ0Y7SUFFQSxPQUFPTDtBQUNULEVBQUU7QUFFRjs7Q0FFQyxHQUNNLE1BQU1XLFVBQVUsQ0FBQ0MsU0FBaUJiO0lBQ3ZDLElBQUljLElBQXFDLEVBQUU7UUFDekMsSUFBSWQsTUFBTTtZQUNSZSxRQUFRQyxHQUFHLENBQUNILFNBQVNkLGFBQWFDO1FBQ3BDLE9BQU87WUFDTGUsUUFBUUMsR0FBRyxDQUFDSDtRQUNkO0lBQ0Y7QUFDRixFQUFFO0FBRUY7OztDQUdDLEdBQ00sTUFBTUksV0FBVyxDQUFDSixTQUFpQks7SUFDeEMsSUFBSUEsaUJBQWlCQyxPQUFPO1FBQzFCLCtDQUErQztRQUMvQ0osUUFBUUcsS0FBSyxDQUFDTCxTQUFTO1lBQ3JCQSxTQUFTSyxNQUFNTCxPQUFPO1lBQ3RCTyxPQUFPRixNQUFNRSxLQUFLO1FBQ3BCO0lBQ0YsT0FBTyxJQUFJRixPQUFPO1FBQ2hCLDBDQUEwQztRQUMxQ0gsUUFBUUcsS0FBSyxDQUFDTCxTQUFTZCxhQUFhbUI7SUFDdEMsT0FBTztRQUNMSCxRQUFRRyxLQUFLLENBQUNMO0lBQ2hCO0FBQ0YsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTVEsYUFBYSxDQUFDUixTQUFpQmI7SUFDMUMsSUFBSWMsSUFBcUMsRUFBRTtRQUN6QyxJQUFJZCxNQUFNO1lBQ1JlLFFBQVFPLElBQUksQ0FBQ1QsU0FBU2QsYUFBYUM7UUFDckMsT0FBTztZQUNMZSxRQUFRTyxJQUFJLENBQUNUO1FBQ2Y7SUFDRjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFzaWJcXE9uZURyaXZlXFxEZXNrdG9wXFxjaGFyaXR5X2luZm9cXGNoYXJpdHlfaW5mb1xcY2hhcml0eS1uZXh0anNcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cblxuLyoqXG4gKiBQYXJzZSBhbmQgZm9ybWF0IHRhZ3MgaW4gbmV3cyBjb250ZW50XG4gKiBDb252ZXJ0cyB0ZXh0IGluIHRoZSBmb3JtYXQgXCIqKlRhZ3M6KiogdGFnMSwgdGFnMiwgdGFnM1wiIHRvIEhUTUwgdGFnc1xuICogQHBhcmFtIGNvbnRlbnQgLSBUaGUgSFRNTCBjb250ZW50IHRvIHBhcnNlXG4gKiBAcmV0dXJucyBGb3JtYXR0ZWQgSFRNTCBjb250ZW50IHdpdGggdGFnc1xuICovXG5leHBvcnQgZnVuY3Rpb24gcGFyc2VOZXdzQ29udGVudChjb250ZW50OiBzdHJpbmcpOiBzdHJpbmcge1xuICAvLyBSZWd1bGFyIGV4cHJlc3Npb24gdG8gbWF0Y2ggdGFnIGZvcm1hdDogKipUYWdzOioqIHRhZzEsIHRhZzIsIHRhZzNcbiAgY29uc3QgdGFnUmVnZXggPSAvXFwqXFwqVGFnczpcXCpcXCpcXHMqKFtePF0rKSg/PTxcXC9wPnwkKS9nO1xuXG4gIC8vIFJlcGxhY2UgbWF0Y2hlZCB0YWcgc2VjdGlvbnMgd2l0aCBmb3JtYXR0ZWQgdGFnc1xuICByZXR1cm4gY29udGVudC5yZXBsYWNlKHRhZ1JlZ2V4LCAobWF0Y2gsIHRhZ0xpc3QpID0+IHtcbiAgICAvLyBTcGxpdCB0aGUgdGFnIGxpc3QgYnkgY29tbWFzIGFuZCB0cmltIHdoaXRlc3BhY2VcbiAgICBjb25zdCB0YWdzID0gdGFnTGlzdC5zcGxpdCgnLCcpLm1hcCh0YWcgPT4gdGFnLnRyaW0oKSkuZmlsdGVyKEJvb2xlYW4pO1xuXG4gICAgaWYgKHRhZ3MubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gbWF0Y2g7IC8vIE5vIHZhbGlkIHRhZ3MgZm91bmQsIHJldHVybiBvcmlnaW5hbCB0ZXh0XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIEhUTUwgZm9yIHRhZ3NcbiAgICBjb25zdCB0YWdzSHRtbCA9IHRhZ3MubWFwKHRhZyA9PlxuICAgICAgYDxzcGFuIGNsYXNzPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHJvdW5kZWQtZnVsbCBiZy10ZWFsLTEwMCBweC0yLjUgcHktMC41IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC10ZWFsLTgwMCBtci0yIG1iLTJcIj4ke3RhZ308L3NwYW4+YFxuICAgICkuam9pbignJyk7XG5cbiAgICByZXR1cm4gYDxkaXYgY2xhc3M9XCJtdC00XCI+PHNwYW4gY2xhc3M9XCJmb250LXNlbWlib2xkXCI+VGFnczo8L3NwYW4+IDxkaXYgY2xhc3M9XCJmbGV4IGZsZXgtd3JhcCBtdC0xXCI+JHt0YWdzSHRtbH08L2Rpdj48L2Rpdj5gO1xuICB9KTtcbn1cblxuLyoqXG4gKiBTYW5pdGl6ZSBzZW5zaXRpdmUgZGF0YSBiZWZvcmUgbG9nZ2luZ1xuICogUmVtb3ZlcyBwYXNzd29yZHMsIHRva2VucywgZXRjLlxuICovXG5leHBvcnQgY29uc3Qgc2FuaXRpemVEYXRhID0gKGRhdGE6IGFueSk6IGFueSA9PiB7XG4gIGlmICghZGF0YSB8fCB0eXBlb2YgZGF0YSAhPT0gJ29iamVjdCcpIHJldHVybiBkYXRhO1xuXG4gIC8vIENyZWF0ZSBhIHNoYWxsb3cgY29weSB0byBhdm9pZCBtb2RpZnlpbmcgdGhlIG9yaWdpbmFsXG4gIGNvbnN0IHNhbml0aXplZCA9IHsgLi4uZGF0YSB9O1xuXG4gIC8vIExpc3Qgb2Ygc2Vuc2l0aXZlIGZpZWxkcyB0byBtYXNrXG4gIGNvbnN0IHNlbnNpdGl2ZUZpZWxkcyA9IFtcbiAgICAncGFzc3dvcmQnLCAncGFzc3dvcmRIYXNoJywgJ3Rva2VuJywgJ3NlY3JldCcsICdhcGlLZXknLFxuICAgICdhdXRob3JpemF0aW9uJywgJ2FjY2Vzc1Rva2VuJywgJ3JlZnJlc2hUb2tlbicsICdjc3JmJyxcbiAgICAnY29va2llJywgJ3Nlc3Npb24nLCAna2V5JywgJ2NyZWRlbnRpYWwnLCAnYXV0aCdcbiAgXTtcblxuICAvLyBNYXNrIHNlbnNpdGl2ZSBmaWVsZHNcbiAgT2JqZWN0LmtleXMoc2FuaXRpemVkKS5mb3JFYWNoKGtleSA9PiB7XG4gICAgY29uc3QgbG93ZXJLZXkgPSBrZXkudG9Mb3dlckNhc2UoKTtcbiAgICBpZiAoc2Vuc2l0aXZlRmllbGRzLnNvbWUoZmllbGQgPT4gbG93ZXJLZXkuaW5jbHVkZXMoZmllbGQpKSkge1xuICAgICAgc2FuaXRpemVkW2tleV0gPSAnW1JFREFDVEVEXSc7XG4gICAgfSBlbHNlIGlmICh0eXBlb2Ygc2FuaXRpemVkW2tleV0gPT09ICdvYmplY3QnICYmIHNhbml0aXplZFtrZXldICE9PSBudWxsKSB7XG4gICAgICAvLyBSZWN1cnNpdmVseSBzYW5pdGl6ZSBuZXN0ZWQgb2JqZWN0c1xuICAgICAgc2FuaXRpemVkW2tleV0gPSBzYW5pdGl6ZURhdGEoc2FuaXRpemVkW2tleV0pO1xuICAgIH1cbiAgfSk7XG5cbiAgcmV0dXJuIHNhbml0aXplZDtcbn07XG5cbi8qKlxuICogTG9nIGluZm9ybWF0aW9uIHRvIHRoZSBjb25zb2xlIGluIGRldmVsb3BtZW50IG1vZGVcbiAqL1xuZXhwb3J0IGNvbnN0IGxvZ0luZm8gPSAobWVzc2FnZTogc3RyaW5nLCBkYXRhPzogYW55KTogdm9pZCA9PiB7XG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgaWYgKGRhdGEpIHtcbiAgICAgIGNvbnNvbGUubG9nKG1lc3NhZ2UsIHNhbml0aXplRGF0YShkYXRhKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUubG9nKG1lc3NhZ2UpO1xuICAgIH1cbiAgfVxufTtcblxuLyoqXG4gKiBMb2cgZXJyb3JzIHRvIHRoZSBjb25zb2xlXG4gKiBBbHdheXMgbG9ncyBpbiBwcm9kdWN0aW9uLCBidXQgc2FuaXRpemVzIHNlbnNpdGl2ZSBkYXRhXG4gKi9cbmV4cG9ydCBjb25zdCBsb2dFcnJvciA9IChtZXNzYWdlOiBzdHJpbmcsIGVycm9yPzogYW55KTogdm9pZCA9PiB7XG4gIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgLy8gRm9yIEVycm9yIG9iamVjdHMsIGxvZyB0aGUgbWVzc2FnZSBhbmQgc3RhY2tcbiAgICBjb25zb2xlLmVycm9yKG1lc3NhZ2UsIHtcbiAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICBzdGFjazogZXJyb3Iuc3RhY2ssXG4gICAgfSk7XG4gIH0gZWxzZSBpZiAoZXJyb3IpIHtcbiAgICAvLyBGb3Igb3RoZXIgZGF0YSwgc2FuaXRpemUgYmVmb3JlIGxvZ2dpbmdcbiAgICBjb25zb2xlLmVycm9yKG1lc3NhZ2UsIHNhbml0aXplRGF0YShlcnJvcikpO1xuICB9IGVsc2Uge1xuICAgIGNvbnNvbGUuZXJyb3IobWVzc2FnZSk7XG4gIH1cbn07XG5cbi8qKlxuICogTG9nIHdhcm5pbmdzIHRvIHRoZSBjb25zb2xlIGluIGRldmVsb3BtZW50IG1vZGVcbiAqL1xuZXhwb3J0IGNvbnN0IGxvZ1dhcm5pbmcgPSAobWVzc2FnZTogc3RyaW5nLCBkYXRhPzogYW55KTogdm9pZCA9PiB7XG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgaWYgKGRhdGEpIHtcbiAgICAgIGNvbnNvbGUud2FybihtZXNzYWdlLCBzYW5pdGl6ZURhdGEoZGF0YSkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLndhcm4obWVzc2FnZSk7XG4gICAgfVxuICB9XG59O1xuXG5cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIiwicGFyc2VOZXdzQ29udGVudCIsImNvbnRlbnQiLCJ0YWdSZWdleCIsInJlcGxhY2UiLCJtYXRjaCIsInRhZ0xpc3QiLCJ0YWdzIiwic3BsaXQiLCJtYXAiLCJ0YWciLCJ0cmltIiwiZmlsdGVyIiwiQm9vbGVhbiIsImxlbmd0aCIsInRhZ3NIdG1sIiwiam9pbiIsInNhbml0aXplRGF0YSIsImRhdGEiLCJzYW5pdGl6ZWQiLCJzZW5zaXRpdmVGaWVsZHMiLCJPYmplY3QiLCJrZXlzIiwiZm9yRWFjaCIsImtleSIsImxvd2VyS2V5IiwidG9Mb3dlckNhc2UiLCJzb21lIiwiZmllbGQiLCJpbmNsdWRlcyIsImxvZ0luZm8iLCJtZXNzYWdlIiwicHJvY2VzcyIsImNvbnNvbGUiLCJsb2ciLCJsb2dFcnJvciIsImVycm9yIiwiRXJyb3IiLCJzdGFjayIsImxvZ1dhcm5pbmciLCJ3YXJuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/@tanstack","vendor-chunks/lucide-react","vendor-chunks/web-vitals","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=node_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();