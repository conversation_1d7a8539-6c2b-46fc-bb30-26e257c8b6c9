/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/news/page";
exports.ids = ["app/news/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"548041302bb1\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFzaWJcXE9uZURyaXZlXFxEZXNrdG9wXFxjaGFyaXR5X2luZm9cXGNoYXJpdHlfaW5mb1xcY2hhcml0eS1uZXh0anNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI1NDgwNDEzMDJiYjFcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/QueryProvider */ \"(rsc)/./src/components/providers/QueryProvider.tsx\");\n/* harmony import */ var _components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/seo/StructuredData */ \"(rsc)/./src/components/seo/StructuredData.tsx\");\n/* harmony import */ var _components_performance_WebVitals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/performance/WebVitals */ \"(rsc)/./src/components/performance/WebVitals.tsx\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"Charity Welcome Hub - Supporting Communities Through Action\",\n        template: \"%s | Charity Welcome Hub\"\n    },\n    description: \"Join our mission to support communities through charitable work, transparency, and meaningful impact. Discover our programs, news, and ways to get involved.\",\n    keywords: [\n        \"charity\",\n        \"community\",\n        \"support\",\n        \"donations\",\n        \"volunteer\",\n        \"nonprofit\",\n        \"humanitarian\",\n        \"social impact\",\n        \"community service\",\n        \"charitable organization\",\n        \"helping others\",\n        \"social good\"\n    ],\n    authors: [\n        {\n            name: \"Charity Welcome Hub\"\n        }\n    ],\n    creator: \"Charity Welcome Hub\",\n    publisher: \"Charity Welcome Hub\",\n    metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),\n    alternates: {\n        canonical: '/'\n    },\n    openGraph: {\n        title: \"Charity Welcome Hub - Supporting Communities Through Action\",\n        description: \"Join our mission to support communities through charitable work, transparency, and meaningful impact.\",\n        type: \"website\",\n        locale: \"en_US\",\n        url: '/',\n        siteName: \"Charity Welcome Hub\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"Charity Welcome Hub - Supporting Communities Through Action\",\n        description: \"Join our mission to support communities through charitable work, transparency, and meaningful impact.\",\n        creator: \"@CharityWelcomeHub\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().variable),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_5__.OrganizationStructuredData, {\n                        name: \"Charity Welcome Hub\",\n                        description: \"Supporting communities through charitable work, transparency, and meaningful impact\",\n                        url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',\n                        contactPoint: {\n                            telephone: \"******-0123\",\n                            contactType: \"customer service\",\n                            email: \"<EMAIL>\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_5__.WebsiteStructuredData, {\n                        name: \"Charity Welcome Hub\",\n                        description: \"Supporting communities through charitable work, transparency, and meaningful impact\",\n                        url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: \"min-h-screen bg-background font-sans antialiased\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_WebVitals__WEBPACK_IMPORTED_MODULE_6__.WebVitals, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_QueryProvider__WEBPACK_IMPORTED_MODULE_4__.QueryProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col min-h-screen\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                    className: \"flex-1\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/loading.tsx":
/*!*************************!*\
  !*** ./app/loading.tsx ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(rsc)/./src/components/ui/skeleton.tsx\");\n\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-8 w-64 mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                        className: \"h-4 w-96\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: [\n                    ...Array(6)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                className: \"h-48 w-full rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-3/4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-1/2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/news/NewsPageContent.tsx":
/*!**************************************!*\
  !*** ./app/news/NewsPageContent.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   NewsPageContent: () => (/* binding */ NewsPageContent)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const NewsPageContent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call NewsPageContent() from the server but NewsPageContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\NewsPageContent.tsx",
"NewsPageContent",
);

/***/ }),

/***/ "(rsc)/./app/news/loading.tsx":
/*!******************************!*\
  !*** ./app/news/loading.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewsLoading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(rsc)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n\n\n\nfunction NewsLoading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                className: \"h-8 w-48 mb-6\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"news-grid\",\n                children: [\n                    ...Array(6)\n                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        className: \"overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                className: \"aspect-video w-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                                lineNumber: 12,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-5\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-6 w-full mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                                className: \"h-4 w-full\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                                                lineNumber: 16,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                                className: \"h-4 w-3/4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                                                lineNumber: 17,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                                className: \"h-4 w-1/2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                                                lineNumber: 18,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                                        lineNumber: 15,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardFooter, {\n                                className: \"px-5 pb-5 pt-0 flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_1__.Skeleton, {\n                                        className: \"h-4 w-24\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                                        lineNumber: 23,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, i, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/news/loading.tsx\n");

/***/ }),

/***/ "(rsc)/./app/news/page.tsx":
/*!***************************!*\
  !*** ./app/news/page.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NewsPage),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _NewsPageContent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./NewsPageContent */ \"(rsc)/./app/news/NewsPageContent.tsx\");\n\n\nconst metadata = {\n    title: 'Latest News - Charity Welcome Hub',\n    description: 'Stay updated with the latest news, announcements, and stories from our charity organization and community impact.',\n    keywords: [\n        'news',\n        'updates',\n        'announcements',\n        'charity news',\n        'community',\n        'stories',\n        'impact'\n    ],\n    openGraph: {\n        title: 'Latest News - Charity Welcome Hub',\n        description: 'Stay updated with the latest news, announcements, and stories from our charity organization and community impact.',\n        type: 'website'\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'Latest News - Charity Welcome Hub',\n        description: 'Stay updated with the latest news, announcements, and stories from our charity organization and community impact.'\n    }\n};\nfunction NewsPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NewsPageContent__WEBPACK_IMPORTED_MODULE_1__.NewsPageContent, {}, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\page.tsx\",\n        lineNumber: 21,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/news/page.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fnews%2Fpage&page=%2Fnews%2Fpage&appPaths=%2Fnews%2Fpage&pagePath=private-next-app-dir%2Fnews%2Fpage.tsx&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fnews%2Fpage&page=%2Fnews%2Fpage&appPaths=%2Fnews%2Fpage&pagePath=private-next-app-dir%2Fnews%2Fpage.tsx&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/loading.tsx */ \"(rsc)/./app/loading.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/news/loading.tsx */ \"(rsc)/./app/news/loading.tsx\"));\nconst page6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/news/page.tsx */ \"(rsc)/./app/news/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'news',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page6, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'loading': [module5, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\loading.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\layout.tsx\"],\n'loading': [module1, \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\loading.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/news/page\",\n        pathname: \"/news\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fnews%2Fpage&page=%2Fnews%2Fpage&appPaths=%2Fnews%2Fpage&pagePath=private-next-app-dir%2Fnews%2Fpage.tsx&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cnews%5C%5CNewsPageContent.tsx%22%2C%22ids%22%3A%5B%22NewsPageContent%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cnews%5C%5CNewsPageContent.tsx%22%2C%22ids%22%3A%5B%22NewsPageContent%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/news/NewsPageContent.tsx */ \"(rsc)/./app/news/NewsPageContent.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hhc2liJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDY2hhcml0eV9pbmZvJTVDJTVDY2hhcml0eV9pbmZvJTVDJTVDY2hhcml0eS1uZXh0anMlNUMlNUNhcHAlNUMlNUNuZXdzJTVDJTVDTmV3c1BhZ2VDb250ZW50LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMk5ld3NQYWdlQ29udGVudCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQXlMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJOZXdzUGFnZUNvbnRlbnRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxoYXNpYlxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXGNoYXJpdHlfaW5mb1xcXFxjaGFyaXR5X2luZm9cXFxcY2hhcml0eS1uZXh0anNcXFxcYXBwXFxcXG5ld3NcXFxcTmV3c1BhZ2VDb250ZW50LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cnews%5C%5CNewsPageContent.tsx%22%2C%22ids%22%3A%5B%22NewsPageContent%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(rsc)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(rsc)/./src/components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/WebVitals.tsx */ \"(rsc)/./src/components/performance/WebVitals.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/QueryProvider.tsx */ \"(rsc)/./src/components/providers/QueryProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ Footer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Footer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Footer.tsx",
"Footer",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\layout\\Header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./src/components/performance/WebVitals.tsx":
/*!**************************************************!*\
  !*** ./src/components/performance/WebVitals.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PageLoadMetrics: () => (/* binding */ PageLoadMetrics),
/* harmony export */   WebVitals: () => (/* binding */ WebVitals),
/* harmony export */   usePerformanceMetric: () => (/* binding */ usePerformanceMetric)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const WebVitals = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx",
"WebVitals",
);const usePerformanceMetric = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call usePerformanceMetric() from the server but usePerformanceMetric is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx",
"usePerformanceMetric",
);const PageLoadMetrics = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call PageLoadMetrics() from the server but PageLoadMetrics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\performance\\WebVitals.tsx",
"PageLoadMetrics",
);

/***/ }),

/***/ "(rsc)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const QueryProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\src\\components\\providers\\QueryProvider.tsx",
"QueryProvider",
);

/***/ }),

/***/ "(rsc)/./src/components/seo/StructuredData.tsx":
/*!***********************************************!*\
  !*** ./src/components/seo/StructuredData.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BreadcrumbStructuredData: () => (/* binding */ BreadcrumbStructuredData),\n/* harmony export */   NewsStructuredData: () => (/* binding */ NewsStructuredData),\n/* harmony export */   OrganizationStructuredData: () => (/* binding */ OrganizationStructuredData),\n/* harmony export */   WebsiteStructuredData: () => (/* binding */ WebsiteStructuredData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction OrganizationStructuredData({ name, description, url, logo, contactPoint }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'Organization',\n        name,\n        description,\n        url,\n        ...logo && {\n            logo\n        },\n        ...contactPoint && {\n            contactPoint\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\nfunction NewsStructuredData({ article, organizationName, organizationUrl }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'NewsArticle',\n        headline: article.title,\n        description: article.body.substring(0, 160),\n        datePublished: article.publishDate,\n        author: {\n            '@type': 'Organization',\n            name: organizationName,\n            url: organizationUrl\n        },\n        publisher: {\n            '@type': 'Organization',\n            name: organizationName,\n            url: organizationUrl\n        },\n        mainEntityOfPage: {\n            '@type': 'WebPage',\n            '@id': `${organizationUrl}/news/${article.slug}`\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\nfunction BreadcrumbStructuredData({ items }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'BreadcrumbList',\n        itemListElement: items.map((item, index)=>({\n                '@type': 'ListItem',\n                position: index + 1,\n                name: item.name,\n                item: item.url\n            }))\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\nfunction WebsiteStructuredData({ name, description, url }) {\n    const structuredData = {\n        '@context': 'https://schema.org',\n        '@type': 'WebSite',\n        name,\n        description,\n        url,\n        potentialAction: {\n            '@type': 'SearchAction',\n            target: {\n                '@type': 'EntryPoint',\n                urlTemplate: `${url}/search?q={search_term_string}`\n            },\n            'query-input': 'required name=search_term_string'\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(structuredData)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/seo/StructuredData.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/skeleton.tsx":
/*!****************************************!*\
  !*** ./src/components/ui/skeleton.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9za2VsZXRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBZ0M7QUFFaEMsU0FBU0MsU0FBUyxFQUNoQkMsU0FBUyxFQUNULEdBQUdDLE9BQ2tDO0lBQ3JDLHFCQUNFLDhEQUFDQztRQUNDRixXQUFXRiw4Q0FBRUEsQ0FBQyxxQ0FBcUNFO1FBQ2xELEdBQUdDLEtBQUs7Ozs7OztBQUdmO0FBRW1CIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhhc2liXFxPbmVEcml2ZVxcRGVza3RvcFxcY2hhcml0eV9pbmZvXFxjaGFyaXR5X2luZm9cXGNoYXJpdHktbmV4dGpzXFxzcmNcXGNvbXBvbmVudHNcXHVpXFxza2VsZXRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5mdW5jdGlvbiBTa2VsZXRvbih7XG4gIGNsYXNzTmFtZSxcbiAgLi4ucHJvcHNcbn06IFJlYWN0LkhUTUxBdHRyaWJ1dGVzPEhUTUxEaXZFbGVtZW50Pikge1xuICByZXR1cm4gKFxuICAgIDxkaXZcbiAgICAgIGNsYXNzTmFtZT17Y24oXCJhbmltYXRlLXB1bHNlIHJvdW5kZWQtbWQgYmctbXV0ZWRcIiwgY2xhc3NOYW1lKX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApXG59XG5cbmV4cG9ydCB7IFNrZWxldG9uIH1cbiJdLCJuYW1lcyI6WyJjbiIsIlNrZWxldG9uIiwiY2xhc3NOYW1lIiwicHJvcHMiLCJkaXYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/skeleton.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   logInfo: () => (/* binding */ logInfo),\n/* harmony export */   logWarning: () => (/* binding */ logWarning),\n/* harmony export */   parseNewsContent: () => (/* binding */ parseNewsContent),\n/* harmony export */   sanitizeData: () => (/* binding */ sanitizeData)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Parse and format tags in news content\n * Converts text in the format \"**Tags:** tag1, tag2, tag3\" to HTML tags\n * @param content - The HTML content to parse\n * @returns Formatted HTML content with tags\n */ function parseNewsContent(content) {\n    // Regular expression to match tag format: **Tags:** tag1, tag2, tag3\n    const tagRegex = /\\*\\*Tags:\\*\\*\\s*([^<]+)(?=<\\/p>|$)/g;\n    // Replace matched tag sections with formatted tags\n    return content.replace(tagRegex, (match, tagList)=>{\n        // Split the tag list by commas and trim whitespace\n        const tags = tagList.split(',').map((tag)=>tag.trim()).filter(Boolean);\n        if (tags.length === 0) {\n            return match; // No valid tags found, return original text\n        }\n        // Create HTML for tags\n        const tagsHtml = tags.map((tag)=>`<span class=\"inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2\">${tag}</span>`).join('');\n        return `<div class=\"mt-4\"><span class=\"font-semibold\">Tags:</span> <div class=\"flex flex-wrap mt-1\">${tagsHtml}</div></div>`;\n    });\n}\n/**\n * Sanitize sensitive data before logging\n * Removes passwords, tokens, etc.\n */ const sanitizeData = (data)=>{\n    if (!data || typeof data !== 'object') return data;\n    // Create a shallow copy to avoid modifying the original\n    const sanitized = {\n        ...data\n    };\n    // List of sensitive fields to mask\n    const sensitiveFields = [\n        'password',\n        'passwordHash',\n        'token',\n        'secret',\n        'apiKey',\n        'authorization',\n        'accessToken',\n        'refreshToken',\n        'csrf',\n        'cookie',\n        'session',\n        'key',\n        'credential',\n        'auth'\n    ];\n    // Mask sensitive fields\n    Object.keys(sanitized).forEach((key)=>{\n        const lowerKey = key.toLowerCase();\n        if (sensitiveFields.some((field)=>lowerKey.includes(field))) {\n            sanitized[key] = '[REDACTED]';\n        } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {\n            // Recursively sanitize nested objects\n            sanitized[key] = sanitizeData(sanitized[key]);\n        }\n    });\n    return sanitized;\n};\n/**\n * Log information to the console in development mode\n */ const logInfo = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.log(message, sanitizeData(data));\n        } else {\n            console.log(message);\n        }\n    }\n};\n/**\n * Log errors to the console\n * Always logs in production, but sanitizes sensitive data\n */ const logError = (message, error)=>{\n    if (error instanceof Error) {\n        // For Error objects, log the message and stack\n        console.error(message, {\n            message: error.message,\n            stack: error.stack\n        });\n    } else if (error) {\n        // For other data, sanitize before logging\n        console.error(message, sanitizeData(error));\n    } else {\n        console.error(message);\n    }\n};\n/**\n * Log warnings to the console in development mode\n */ const logWarning = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.warn(message, sanitizeData(data));\n        } else {\n            console.warn(message);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./app/news/NewsPageContent.tsx":
/*!**************************************!*\
  !*** ./app/news/NewsPageContent.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NewsPageContent: () => (/* binding */ NewsPageContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Loader!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Loader!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calendar_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calendar,Loader!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(ssr)/./node_modules/date-fns/format.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/pagination */ \"(ssr)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _config_api__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/config/api */ \"(ssr)/./src/config/api.ts\");\n/* __next_internal_client_entry_do_not_use__ NewsPageContent auto */ \n\n\n\n\n\n\n\n\n\n\n\nfunction NewsPageContent() {\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [featuredImages, setFeaturedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [imagesLoaded, setImagesLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const { data, isLoading, error } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.useQuery)({\n        queryKey: [\n            'news',\n            currentPage\n        ],\n        queryFn: {\n            \"NewsPageContent.useQuery\": ()=>_lib_api__WEBPACK_IMPORTED_MODULE_5__.newsApi.getAll(currentPage)\n        }[\"NewsPageContent.useQuery\"]\n    });\n    // Set featured images directly from the news data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NewsPageContent.useEffect\": ()=>{\n            if (data?.news && data.news.length > 0) {\n                // Process all articles at once to set featured images\n                const newFeaturedImages = {};\n                const newImagesLoaded = {};\n                data.news.forEach({\n                    \"NewsPageContent.useEffect\": (article)=>{\n                        if (article.attachments && article.attachments.length > 0) {\n                            // Find the first image attachment\n                            const imageAttachment = article.attachments.find({\n                                \"NewsPageContent.useEffect.imageAttachment\": (att)=>att.mimetype && att.mimetype.startsWith('image/')\n                            }[\"NewsPageContent.useEffect.imageAttachment\"]);\n                            if (imageAttachment) {\n                                const imageUrl = `${_config_api__WEBPACK_IMPORTED_MODULE_7__[\"default\"].baseURL}/api/news/attachments/${imageAttachment._id}/content`;\n                                newFeaturedImages[article._id] = imageUrl;\n                                newImagesLoaded[article._id] = false;\n                            }\n                        }\n                    }\n                }[\"NewsPageContent.useEffect\"]);\n                setFeaturedImages(newFeaturedImages);\n                setImagesLoaded(newImagesLoaded);\n            }\n        }\n    }[\"NewsPageContent.useEffect\"], [\n        data\n    ]);\n    // Handle image load\n    const handleImageLoad = (articleId)=>{\n        setImagesLoaded((prev)=>({\n                ...prev,\n                [articleId]: true\n            }));\n    };\n    // Handle image error\n    const handleImageError = (articleId)=>{\n        setFeaturedImages((prev)=>{\n            const updated = {\n                ...prev\n            };\n            delete updated[articleId];\n            return updated;\n        });\n        setImagesLoaded((prev)=>({\n                ...prev,\n                [articleId]: true\n            }));\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center min-h-[400px]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-red-200 bg-red-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"text-red-800\",\n                                children: \"Error\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                className: \"text-red-700\",\n                                children: \"Failed to load news\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: error instanceof Error ? error.message : 'Unknown error occurred'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n            lineNumber: 86,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto px-4 py-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-3xl font-bold mb-6\",\n                children: \"Latest News\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"news-grid\",\n                children: data.news.map((article, index)=>{\n                    const hasImage = featuredImages[article._id];\n                    const isLoaded = imagesLoaded[article._id] || !hasImage;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: `/news/${article.slug}`,\n                        className: \"group block\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 h-full flex flex-col\", index === 0 && \"md:col-span-2 md:row-span-2\"),\n                            children: [\n                                hasImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"relative overflow-hidden bg-gray-100\", index === 0 ? \"aspect-[16/10]\" : \"aspect-video\"),\n                                    children: [\n                                        !isLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-6 w-6 animate-spin text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 25\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: hasImage,\n                                            alt: article.title,\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"w-full h-full object-cover transition-all duration-700 group-hover:scale-105\", !isLoaded && \"opacity-0\"),\n                                            onLoad: ()=>handleImageLoad(article._id),\n                                            onError: ()=>handleImageError(article._id),\n                                            loading: \"lazy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"p-5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300\",\n                                            children: article.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"line-clamp-3 text-sm text-gray-600\",\n                                            dangerouslySetInnerHTML: {\n                                                __html: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.parseNewsContent)(article.body.substring(0, 150) + '...')\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardFooter, {\n                                    className: \"px-5 pb-5 pt-0 flex flex-col gap-2 w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-gray-500 w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 21\n                                                }, this),\n                                                (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_11__.format)(new Date(article.publishDate), 'MMMM d, yyyy')\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-primary font-medium flex items-center w-full\",\n                                            children: [\n                                                \"Read More \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calendar_Loader_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"h-4 w-4 ml-1 transition-transform duration-300 group-hover:translate-x-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 31\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 15\n                        }, this)\n                    }, article._id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            data.pagination && data.pagination.pages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.Pagination, {\n                className: \"mt-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationPrevious, {\n                                href: \"#\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    if (currentPage > 1) setCurrentPage(currentPage - 1);\n                                },\n                                className: currentPage === 1 ? 'pointer-events-none opacity-50' : ''\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, this),\n                        currentPage > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationLink, {\n                                href: \"#\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    setCurrentPage(1);\n                                },\n                                children: \"1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 15\n                        }, this),\n                        currentPage > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationEllipsis, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationLink, {\n                                href: \"#\",\n                                isActive: true,\n                                children: currentPage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this),\n                        currentPage < data.pagination.pages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationLink, {\n                                href: \"#\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    setCurrentPage(currentPage + 1);\n                                },\n                                children: currentPage + 1\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                            lineNumber: 210,\n                            columnNumber: 15\n                        }, this),\n                        currentPage < data.pagination.pages - 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationEllipsis, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 15\n                        }, this),\n                        currentPage < data.pagination.pages - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationLink, {\n                                href: \"#\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    setCurrentPage(data.pagination.pages);\n                                },\n                                children: data.pagination.pages\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_4__.PaginationNext, {\n                                href: \"#\",\n                                onClick: (e)=>{\n                                    e.preventDefault();\n                                    if (currentPage < data.pagination.pages) setCurrentPage(currentPage + 1);\n                                },\n                                className: currentPage === data.pagination.pages ? 'pointer-events-none opacity-50' : ''\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n                lineNumber: 169,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\app\\\\news\\\\NewsPageContent.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/news/NewsPageContent.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cnews%5C%5CNewsPageContent.tsx%22%2C%22ids%22%3A%5B%22NewsPageContent%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cnews%5C%5CNewsPageContent.tsx%22%2C%22ids%22%3A%5B%22NewsPageContent%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/news/NewsPageContent.tsx */ \"(ssr)/./app/news/NewsPageContent.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2hhc2liJTVDJTVDT25lRHJpdmUlNUMlNUNEZXNrdG9wJTVDJTVDY2hhcml0eV9pbmZvJTVDJTVDY2hhcml0eV9pbmZvJTVDJTVDY2hhcml0eS1uZXh0anMlNUMlNUNhcHAlNUMlNUNuZXdzJTVDJTVDTmV3c1BhZ2VDb250ZW50LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMk5ld3NQYWdlQ29udGVudCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0tBQXlMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJOZXdzUGFnZUNvbnRlbnRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxoYXNpYlxcXFxPbmVEcml2ZVxcXFxEZXNrdG9wXFxcXGNoYXJpdHlfaW5mb1xcXFxjaGFyaXR5X2luZm9cXFxcY2hhcml0eS1uZXh0anNcXFxcYXBwXFxcXG5ld3NcXFxcTmV3c1BhZ2VDb250ZW50LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cnews%5C%5CNewsPageContent.tsx%22%2C%22ids%22%3A%5B%22NewsPageContent%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/performance/WebVitals.tsx */ \"(ssr)/./src/components/performance/WebVitals.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/QueryProvider.tsx */ \"(ssr)/./src/components/providers/QueryProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cperformance%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Chasib%5C%5COneDrive%5C%5CDesktop%5C%5Ccharity_info%5C%5Ccharity_info%5C%5Ccharity-nextjs%5C%5Csrc%5C%5Ccomponents%5C%5Cproviders%5C%5CQueryProvider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rss.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Calendar,Gift,Heart,HelpCircle,Image,Info,Mail,MapPin,Phone,Rss,Search!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\n// import { locationsApi } from \"@/services/api\";\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    // Temporary: Comment out API call for migration\n    // const { data: locationsData, isLoading: isLoadingLocations } = useQuery({\n    //   queryKey: ['locations'],\n    //   queryFn: () => locationsApi.getAll({ active: true }),\n    //   staleTime: 5 * 60 * 1000, // 5 minutes\n    // });\n    // Temporary mock data for migration\n    const isLoadingLocations = false;\n    const locationsData = {\n        locations: [\n            {\n                isMainOffice: true,\n                phone: \"+****************\",\n                email: \"<EMAIL>\",\n                address: \"123 Charity Street, City, State 12345\"\n            }\n        ]\n    };\n    // Find the main office location\n    const mainOffice = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo({\n        \"Footer.useMemo[mainOffice]\": ()=>{\n            if (!locationsData?.locations) return null;\n            return locationsData.locations.find({\n                \"Footer.useMemo[mainOffice]\": (loc)=>loc.isMainOffice\n            }[\"Footer.useMemo[mainOffice]\"]) || locationsData.locations[0];\n        }\n    }[\"Footer.useMemo[mainOffice]\"], [\n        locationsData\n    ]);\n    // Show placeholder content while loading\n    if (isLoadingLocations) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n            className: \"bg-teal-50 border-t border-teal-100 py-10 mt-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 text-center text-teal-700\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Loading footer content...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                lineNumber: 42,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 41,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-teal-50 border-t border-teal-100 py-10 mt-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"col-span-1 md:col-span-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"font-bold text-xl mb-4 text-teal-800\",\n                                    children: \"Charity Info\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-teal-700 mb-4\",\n                                    children: \"Supporting communities and making a difference\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        mainOffice?.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-700 hover:text-teal-900 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: mainOffice.phone\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this),\n                                        mainOffice?.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-700 hover:text-teal-900 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: `mailto:${mainOffice.email}`,\n                                                    className: \"hover:text-teal-900\",\n                                                    children: mainOffice.email\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 69,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        mainOffice?.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-teal-700 hover:text-teal-900 transition-colors\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: mainOffice.address\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 75,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-bold mb-4 text-teal-800\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Home\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/news\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"News\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/gallery\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Gallery\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/about\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"About Us\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/contact\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Contact Us\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/search\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Search\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-bold mb-4 text-teal-800\",\n                                    children: \"Resources\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/faq\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"FAQ\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: \"/donate\",\n                                                className: \"text-teal-700 hover:text-teal-900 flex items-center transition-colors\",\n                                                onClick: ()=>window.scrollTo(0, 0),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"How to Donate\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-6 border-t border-teal-100 text-center text-teal-700\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"\\xa9 \",\n                                currentYear,\n                                \" Charity Info. All rights reserved.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 flex items-center justify-center text-sm\",\n                            children: [\n                                \"Built with\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Calendar_Gift_Heart_HelpCircle_Image_Info_Mail_MapPin_Phone_Rss_Search_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mx-1 text-pink-500 fill-pink-500\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this),\n                                \" for charitable causes\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-in.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=LogIn,Menu,Search,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./src/components/ui/input.tsx\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // const { isAuthenticated, user, logout } = useAuth();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Temporary auth state for migration\n    const isAuthenticated = false;\n    const user = null;\n    const logout = ()=>{};\n    const handleSearch = (e)=>{\n        e.preventDefault();\n        if (searchQuery.trim()) {\n            router.push(`/search?q=${encodeURIComponent(searchQuery)}`);\n            setSearchQuery(\"\");\n            setIsMenuOpen(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 bg-charity-muted border-b border-gray-200 shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-4 flex items-center justify-between bg-charity-muted\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/\",\n                            className: \"font-bold text-2xl text-primary mr-4\",\n                            children: \"Charity Info\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center space-x-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/news\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"News\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/gallery\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"Gallery\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/about\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"About Us\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/contact\",\n                                className: \"text-gray-700 hover:text-primary font-medium\",\n                                children: \"Contact\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSearch,\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        type: \"search\",\n                                        placeholder: \"Search...\",\n                                        className: \"w-64 pr-10\",\n                                        value: searchQuery,\n                                        onChange: (e)=>setSearchQuery(e.target.value)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        type: \"submit\",\n                                        size: \"icon\",\n                                        variant: \"ghost\",\n                                        className: \"absolute right-0 top-0 h-full\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this),\n                            isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/admin\",\n                                        className: \"text-gray-700 hover:text-primary font-medium\",\n                                        children: \"Dashboard\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>logout(),\n                                        children: \"Logout\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/login\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" Login\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        className: \"md:hidden\",\n                        onClick: ()=>setIsMenuOpen(!isMenuOpen),\n                        children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 25\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-6 w-6\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 53\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden bg-charity-muted border-t border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"container mx-auto px-4 py-4 space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/news\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"News\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/gallery\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"Gallery\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/about\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"About Us\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/contact\",\n                            className: \"block text-gray-700 hover:text-primary font-medium\",\n                            onClick: ()=>setIsMenuOpen(false),\n                            children: \"Contact\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSearch,\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                    type: \"search\",\n                                    placeholder: \"Search...\",\n                                    className: \"w-full pr-10\",\n                                    value: searchQuery,\n                                    onChange: (e)=>setSearchQuery(e.target.value)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    size: \"icon\",\n                                    variant: \"ghost\",\n                                    className: \"absolute right-0 top-0 h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this),\n                        isAuthenticated ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/admin\",\n                                    className: \"block text-gray-700 hover:text-primary font-medium\",\n                                    onClick: ()=>setIsMenuOpen(false),\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>{\n                                        logout();\n                                        setIsMenuOpen(false);\n                                    },\n                                    className: \"w-full\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            asChild: true,\n                            className: \"w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/login\",\n                                onClick: ()=>setIsMenuOpen(false),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogIn_Menu_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 19\n                                    }, this),\n                                    \" Login\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 106,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/performance/WebVitals.tsx":
/*!**************************************************!*\
  !*** ./src/components/performance/WebVitals.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PageLoadMetrics: () => (/* binding */ PageLoadMetrics),\n/* harmony export */   WebVitals: () => (/* binding */ WebVitals),\n/* harmony export */   usePerformanceMetric: () => (/* binding */ usePerformanceMetric)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var web_vitals__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! web-vitals */ \"(ssr)/./node_modules/web-vitals/dist/web-vitals.js\");\n/* __next_internal_client_entry_do_not_use__ WebVitals,usePerformanceMetric,PageLoadMetrics auto */ \n\nfunction sendToAnalytics(metric) {\n    // In production, you would send this to your analytics service\n    // For now, we'll just log to console in development\n    if (true) {\n        console.log('Web Vital:', metric);\n    }\n// Example: Send to Google Analytics\n// gtag('event', metric.name, {\n//   event_category: 'Web Vitals',\n//   event_label: metric.id,\n//   value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),\n//   non_interaction: true,\n// });\n// Example: Send to custom analytics endpoint\n// fetch('/api/analytics/web-vitals', {\n//   method: 'POST',\n//   headers: { 'Content-Type': 'application/json' },\n//   body: JSON.stringify(metric),\n// });\n}\nfunction WebVitals() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"WebVitals.useEffect\": ()=>{\n            // Measure Core Web Vitals\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onCLS)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onINP)(sendToAnalytics); // INP replaced FID\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onFCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onLCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onTTFB)(sendToAnalytics);\n        }\n    }[\"WebVitals.useEffect\"], []);\n    return null; // This component doesn't render anything\n}\n// Hook for measuring custom metrics\nfunction usePerformanceMetric(name, value) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"usePerformanceMetric.useEffect\": ()=>{\n            const metric = {\n                name,\n                value,\n                id: `${name}-${Date.now()}`,\n                delta: value,\n                entries: []\n            };\n            sendToAnalytics(metric);\n        }\n    }[\"usePerformanceMetric.useEffect\"], [\n        name,\n        value\n    ]);\n}\n// Component for measuring page load performance\nfunction PageLoadMetrics({ pageName }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"PageLoadMetrics.useEffect\": ()=>{\n            const startTime = performance.now();\n            return ({\n                \"PageLoadMetrics.useEffect\": ()=>{\n                    const endTime = performance.now();\n                    const loadTime = endTime - startTime;\n                    const metric = {\n                        name: 'page_load_time',\n                        value: loadTime,\n                        id: `${pageName}-${Date.now()}`,\n                        delta: loadTime,\n                        entries: [],\n                        page: pageName\n                    };\n                    sendToAnalytics(metric);\n                }\n            })[\"PageLoadMetrics.useEffect\"];\n        }\n    }[\"PageLoadMetrics.useEffect\"], [\n        pageName\n    ]);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/performance/WebVitals.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n\n\nfunction QueryProvider({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"QueryProvider.useState\": ()=>new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n                defaultOptions: {\n                    queries: {\n                        staleTime: 60 * 1000,\n                        retry: 1\n                    }\n                }\n            })\n    }[\"QueryProvider.useState\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\providers\\\\QueryProvider.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvUXVlcnlQcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFeUU7QUFDeEM7QUFFMUIsU0FBU0csY0FBYyxFQUFFQyxRQUFRLEVBQWlDO0lBQ3ZFLE1BQU0sQ0FBQ0MsWUFBWSxHQUFHSCwrQ0FBUUE7a0NBQzVCLElBQ0UsSUFBSUYsOERBQVdBLENBQUM7Z0JBQ2RNLGdCQUFnQjtvQkFDZEMsU0FBUzt3QkFDUEMsV0FBVyxLQUFLO3dCQUNoQkMsT0FBTztvQkFDVDtnQkFDRjtZQUNGOztJQUdKLHFCQUNFLDhEQUFDUixzRUFBbUJBO1FBQUNTLFFBQVFMO2tCQUMxQkQ7Ozs7OztBQUdQIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGhhc2liXFxPbmVEcml2ZVxcRGVza3RvcFxcY2hhcml0eV9pbmZvXFxjaGFyaXR5X2luZm9cXGNoYXJpdHktbmV4dGpzXFxzcmNcXGNvbXBvbmVudHNcXHByb3ZpZGVyc1xcUXVlcnlQcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcblxuZXhwb3J0IGZ1bmN0aW9uIFF1ZXJ5UHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCBbcXVlcnlDbGllbnRdID0gdXNlU3RhdGUoXG4gICAgKCkgPT5cbiAgICAgIG5ldyBRdWVyeUNsaWVudCh7XG4gICAgICAgIGRlZmF1bHRPcHRpb25zOiB7XG4gICAgICAgICAgcXVlcmllczoge1xuICAgICAgICAgICAgc3RhbGVUaW1lOiA2MCAqIDEwMDAsIC8vIDEgbWludXRlXG4gICAgICAgICAgICByZXRyeTogMSxcbiAgICAgICAgICB9LFxuICAgICAgICB9LFxuICAgICAgfSlcbiAgKTtcblxuICByZXR1cm4gKFxuICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvUXVlcnlDbGllbnRQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJRdWVyeUNsaWVudCIsIlF1ZXJ5Q2xpZW50UHJvdmlkZXIiLCJ1c2VTdGF0ZSIsIlF1ZXJ5UHJvdmlkZXIiLCJjaGlsZHJlbiIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyaWVzIiwic3RhbGVUaW1lIiwicmV0cnkiLCJjbGllbnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/QueryProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200\", {\n    variants: {\n        variant: {\n            default: \"bg-charity-primary text-white hover:bg-charity-secondary\",\n            destructive: \"bg-charity-destructive text-white hover:bg-charity-destructive/90\",\n            outline: \"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-charity-light text-charity-dark hover:bg-charity-light/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-charity-primary underline-offset-4 hover:underline\",\n            accent: \"bg-charity-accent text-charity-dark hover:bg-charity-accent/80\",\n            success: \"bg-charity-success text-white hover:bg-charity-success/90\",\n            warning: \"bg-charity-warning text-white hover:bg-charity-warning/90\",\n            info: \"bg-charity-info text-white hover:bg-charity-info/90\",\n            donate: \"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3 py-1.5 text-xs\",\n            lg: \"h-11 rounded-md px-8 py-2.5\",\n            xl: \"h-12 rounded-md px-10 py-3 text-base\",\n            icon: \"h-10 w-10 rounded-full\",\n            wide: \"h-10 px-8 py-2 w-full\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 53,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/input.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/input.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border-[1px] border-input bg-background px-3 py-2 text-base text-foreground ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground/60 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-charity-primary focus-visible:border-charity-primary focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200 md:text-sm shadow-none\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/pagination.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/pagination.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Pagination: () => (/* binding */ Pagination),\n/* harmony export */   PaginationContent: () => (/* binding */ PaginationContent),\n/* harmony export */   PaginationEllipsis: () => (/* binding */ PaginationEllipsis),\n/* harmony export */   PaginationItem: () => (/* binding */ PaginationItem),\n/* harmony export */   PaginationLink: () => (/* binding */ PaginationLink),\n/* harmony export */   PaginationNext: () => (/* binding */ PaginationNext),\n/* harmony export */   PaginationPrevious: () => (/* binding */ PaginationPrevious)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,MoreHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,MoreHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,MoreHorizontal!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n\n\n\n\n\nconst Pagination = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        role: \"navigation\",\n        \"aria-label\": \"pagination\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"mx-auto flex w-full justify-center\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined);\nPagination.displayName = \"Pagination\";\nconst PaginationContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-row items-center gap-1\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined));\nPaginationContent.displayName = \"PaginationContent\";\nconst PaginationItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n        lineNumber: 33,\n        columnNumber: 3\n    }, undefined));\nPaginationItem.displayName = \"PaginationItem\";\nconst PaginationLink = ({ className, isActive, size = \"icon\", ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        \"aria-current\": isActive ? \"page\" : undefined,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)((0,_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.buttonVariants)({\n            variant: isActive ? \"outline\" : \"ghost\",\n            size\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n        lineNumber: 48,\n        columnNumber: 3\n    }, undefined);\nPaginationLink.displayName = \"PaginationLink\";\nconst PaginationPrevious = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PaginationLink, {\n        \"aria-label\": \"Go to previous page\",\n        size: \"default\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"gap-1 pl-2.5\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                lineNumber: 72,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: \"Previous\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                lineNumber: 73,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n        lineNumber: 66,\n        columnNumber: 3\n    }, undefined);\nPaginationPrevious.displayName = \"PaginationPrevious\";\nconst PaginationNext = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PaginationLink, {\n        \"aria-label\": \"Go to next page\",\n        size: \"default\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"gap-1 pr-2.5\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                children: \"Next\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                lineNumber: 88,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                lineNumber: 89,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n        lineNumber: 82,\n        columnNumber: 3\n    }, undefined);\nPaginationNext.displayName = \"PaginationNext\";\nconst PaginationEllipsis = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        \"aria-hidden\": true,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-9 w-9 items-center justify-center\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                lineNumber: 103,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"More pages\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n                lineNumber: 104,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\ui\\\\pagination.tsx\",\n        lineNumber: 98,\n        columnNumber: 3\n    }, undefined);\nPaginationEllipsis.displayName = \"PaginationEllipsis\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/pagination.tsx\n");

/***/ }),

/***/ "(ssr)/./src/config/api.ts":
/*!***************************!*\
  !*** ./src/config/api.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// API configuration\nconst API_CONFIG = {\n    baseURL:  false ? 0 // In production, use relative path to Next.js API routes\n     : '/api',\n    backendURL:  false ? 0 : 'http://localhost:5000',\n    timeout: 15000,\n    withCredentials: true,\n    headers: {\n        'Cache-Control': 'max-age=300',\n        'Content-Type': 'application/json'\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (API_CONFIG);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29uZmlnL2FwaS50cyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsb0JBQW9CO0FBQ3BCLE1BQU1BLGFBQWE7SUFDakJDLFNBQVNDLE1BQXFDLEdBQzFDLENBQU0sQ0FBQyx5REFBeUQ7T0FDaEU7SUFDSkMsWUFBWUQsTUFBcUMsR0FDN0NBLENBQThELEdBQzlEO0lBQ0pJLFNBQVM7SUFDVEMsaUJBQWlCO0lBQ2pCQyxTQUFTO1FBQ1AsaUJBQWlCO1FBQ2pCLGdCQUFnQjtJQUNsQjtBQUNGO0FBRUEsaUVBQWVSLFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFzaWJcXE9uZURyaXZlXFxEZXNrdG9wXFxjaGFyaXR5X2luZm9cXGNoYXJpdHlfaW5mb1xcY2hhcml0eS1uZXh0anNcXHNyY1xcY29uZmlnXFxhcGkudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQVBJIGNvbmZpZ3VyYXRpb25cbmNvbnN0IEFQSV9DT05GSUcgPSB7XG4gIGJhc2VVUkw6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbidcbiAgICA/ICcvYXBpJyAvLyBJbiBwcm9kdWN0aW9uLCB1c2UgcmVsYXRpdmUgcGF0aCB0byBOZXh0LmpzIEFQSSByb3V0ZXNcbiAgICA6ICcvYXBpJywgLy8gSW4gZGV2ZWxvcG1lbnQsIHVzZSBOZXh0LmpzIEFQSSByb3V0ZXNcbiAgYmFja2VuZFVSTDogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJ1xuICAgID8gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQkFDS0VORF9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6NTAwMCdcbiAgICA6ICdodHRwOi8vbG9jYWxob3N0OjUwMDAnLCAvLyBEaXJlY3QgYmFja2VuZCBVUkwgZm9yIHVwbG9hZHMvaW1hZ2VzXG4gIHRpbWVvdXQ6IDE1MDAwLCAvLyBJbmNyZWFzZWQgdGltZW91dCBmb3Igc2xvd2VyIGNvbm5lY3Rpb25zXG4gIHdpdGhDcmVkZW50aWFsczogdHJ1ZSxcbiAgaGVhZGVyczoge1xuICAgICdDYWNoZS1Db250cm9sJzogJ21heC1hZ2U9MzAwJywgLy8gQ2FjaGUgcmVzcG9uc2VzIGZvciA1IG1pbnV0ZXNcbiAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICB9LFxufTtcblxuZXhwb3J0IGRlZmF1bHQgQVBJX0NPTkZJRztcbiJdLCJuYW1lcyI6WyJBUElfQ09ORklHIiwiYmFzZVVSTCIsInByb2Nlc3MiLCJiYWNrZW5kVVJMIiwiZW52IiwiTkVYVF9QVUJMSUNfQkFDS0VORF9VUkwiLCJ0aW1lb3V0Iiwid2l0aENyZWRlbnRpYWxzIiwiaGVhZGVycyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/config/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aboutApi: () => (/* binding */ aboutApi),\n/* harmony export */   contactApi: () => (/* binding */ contactApi),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   faqApi: () => (/* binding */ faqApi),\n/* harmony export */   galleryApi: () => (/* binding */ galleryApi),\n/* harmony export */   locationsApi: () => (/* binding */ locationsApi),\n/* harmony export */   newsApi: () => (/* binding */ newsApi),\n/* harmony export */   teamApi: () => (/* binding */ teamApi)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _config_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../config/api */ \"(ssr)/./src/config/api.ts\");\n\n\n// Create axios instance with config\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: _config_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].baseURL,\n    timeout: _config_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].timeout,\n    withCredentials: _config_api__WEBPACK_IMPORTED_MODULE_0__[\"default\"].withCredentials\n});\n// Add request interceptor for authentication\napi.interceptors.request.use(async (config)=>{\n    if (false) {}\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Add response interceptor for error handling\napi.interceptors.response.use((response)=>response, (error)=>{\n    if (error.response?.status === 401 && \"undefined\" !== 'undefined') {}\n    return Promise.reject(error);\n});\n// About API\nconst aboutApi = {\n    getContent: async ()=>{\n        const response = await api.get('/about');\n        return response.data;\n    },\n    updateContent: async (aboutData)=>{\n        const response = await api.post('/about', aboutData);\n        return response.data;\n    }\n};\n// Team API\nconst teamApi = {\n    getAll: async (params)=>{\n        const response = await api.get('/team', {\n            params\n        });\n        return {\n            teamMembers: response.data\n        };\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/team/${id}`);\n        return {\n            teamMember: response.data\n        };\n    },\n    create: async (formData)=>{\n        const response = await api.post('/team', formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return response.data;\n    },\n    update: async (id, formData)=>{\n        const response = await api.put(`/team/${id}`, formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return response.data;\n    },\n    delete: async (id)=>{\n        const response = await api.delete(`/team/${id}`);\n        return response.data;\n    }\n};\n// Contact API\nconst contactApi = {\n    submit: async (contactData)=>{\n        const response = await api.post('/contact', contactData);\n        return response.data;\n    },\n    getAll: async (page = 1, limit = 10, status)=>{\n        const params = {\n            page,\n            limit,\n            ...status ? {\n                status\n            } : {}\n        };\n        const response = await api.get('/contact', {\n            params\n        });\n        return response.data;\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/contact/${id}`);\n        return response.data;\n    },\n    updateStatus: async (id, status)=>{\n        const response = await api.put(`/contact/${id}/status`, {\n            status\n        });\n        return response.data;\n    },\n    delete: async (id)=>{\n        const response = await api.delete(`/contact/${id}`);\n        return response.data;\n    }\n};\n// Locations API\nconst locationsApi = {\n    getAll: async (params)=>{\n        const response = await api.get('/locations', {\n            params\n        });\n        return {\n            locations: response.data\n        };\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/locations/${id}`);\n        return {\n            location: response.data\n        };\n    },\n    create: async (locationData)=>{\n        const formattedData = {\n            ...locationData,\n            isMainOffice: locationData.isMainOffice !== undefined ? String(locationData.isMainOffice) : undefined,\n            active: locationData.active !== undefined ? String(locationData.active) : undefined\n        };\n        const response = await api.post('/locations', formattedData);\n        return response.data;\n    },\n    update: async (id, locationData)=>{\n        const formattedData = {\n            ...locationData,\n            isMainOffice: locationData.isMainOffice !== undefined ? String(locationData.isMainOffice) : undefined,\n            active: locationData.active !== undefined ? String(locationData.active) : undefined\n        };\n        const response = await api.put(`/locations/${id}`, formattedData);\n        return response.data;\n    },\n    delete: async (id)=>{\n        const response = await api.delete(`/locations/${id}`);\n        return response.data;\n    }\n};\n// FAQ API\nconst faqApi = {\n    getAll: async ()=>{\n        const response = await api.get('/faqs');\n        return response.data;\n    },\n    getAllAdmin: async ()=>{\n        const response = await api.get('/admin/faqs');\n        return response.data;\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/admin/faqs/${id}`);\n        return response.data;\n    },\n    create: async (faqData)=>{\n        const response = await api.post('/admin/faqs', faqData);\n        return response.data;\n    },\n    update: async (id, faqData, isPartialUpdate = false)=>{\n        let sanitizedData;\n        if (isPartialUpdate) {\n            // For partial updates, only include the isActive field\n            sanitizedData = {\n                isActive: faqData.isActive === undefined ? true : Boolean(faqData.isActive)\n            };\n        } else {\n            // For full updates, include all fields with proper formatting\n            sanitizedData = {\n                question: faqData.question?.trim(),\n                answer: faqData.answer?.trim(),\n                category: faqData.category?.trim() || 'General',\n                order: typeof faqData.order === 'number' ? faqData.order : 0,\n                isActive: faqData.isActive === undefined ? true : Boolean(faqData.isActive)\n            };\n        }\n        try {\n            const response = await api.put(`/admin/faqs/${id}`, sanitizedData);\n            return response.data;\n        } catch (error) {\n            console.error('FAQ update error:', error);\n            throw error;\n        }\n    },\n    delete: async (id)=>{\n        const response = await api.delete(`/admin/faqs/${id}`);\n        return response.data;\n    }\n};\n// News API\nconst newsApi = {\n    getAll: async (page = 1, limit = 10)=>{\n        const response = await api.get(`/news?page=${page}&limit=${limit}&includeAttachments=true`);\n        return response.data;\n    },\n    getBySlug: async (slug)=>{\n        const response = await api.get(`/news/${slug}?includeAttachments=true`);\n        return response.data.news;\n    },\n    getById: async (id)=>{\n        const response = await api.get(`/admin/news/${id}`);\n        return response.data.news;\n    },\n    create: async (newsData)=>{\n        const response = await api.post('/admin/news', newsData);\n        return response.data.news;\n    },\n    update: async (id, newsData)=>{\n        const response = await api.put(`/admin/news/${id}`, newsData);\n        return response.data.news;\n    },\n    delete: async (id)=>{\n        const response = await api.delete(`/admin/news/${id}`);\n        return response.data;\n    }\n};\n// Gallery API\nconst galleryApi = {\n    getAllAlbums: async ()=>{\n        const response = await api.get('/gallery/albums');\n        return response.data;\n    },\n    getAlbumBySlug: async (slug)=>{\n        try {\n            const response = await api.get(`/gallery/albums/${slug}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching album by slug:', error);\n            throw error;\n        }\n    },\n    getAlbumById: async (id)=>{\n        try {\n            const response = await api.get(`/admin/gallery/albums/${id}`);\n            return response.data;\n        } catch (error) {\n            console.error('Error fetching album by ID:', error);\n            throw error;\n        }\n    },\n    createAlbum: async (albumData)=>{\n        const response = await api.post('/admin/gallery/albums', albumData);\n        return response.data.album;\n    },\n    updateAlbum: async (id, albumData)=>{\n        const response = await api.put(`/admin/gallery/albums/${id}`, albumData);\n        return response.data.album;\n    },\n    deleteAlbum: async (id)=>{\n        await api.delete(`/admin/gallery/albums/${id}`);\n    },\n    uploadImage: async (albumId, formData)=>{\n        // Ensure CSRF token is included\n        const csrfToken = document.querySelector('meta[name=\"csrf-token\"]')?.getAttribute('content');\n        if (csrfToken) {\n            formData.append('_csrf', csrfToken);\n        }\n        const response = await api.post(`/admin/gallery/albums/${albumId}/images`, formData, {\n            headers: {\n                'Content-Type': 'multipart/form-data'\n            }\n        });\n        return response.data;\n    },\n    deleteImage: async (imageId)=>{\n        await api.delete(`/admin/gallery/images/${imageId}`);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   logError: () => (/* binding */ logError),\n/* harmony export */   logInfo: () => (/* binding */ logInfo),\n/* harmony export */   logWarning: () => (/* binding */ logWarning),\n/* harmony export */   parseNewsContent: () => (/* binding */ parseNewsContent),\n/* harmony export */   sanitizeData: () => (/* binding */ sanitizeData)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Parse and format tags in news content\n * Converts text in the format \"**Tags:** tag1, tag2, tag3\" to HTML tags\n * @param content - The HTML content to parse\n * @returns Formatted HTML content with tags\n */ function parseNewsContent(content) {\n    // Regular expression to match tag format: **Tags:** tag1, tag2, tag3\n    const tagRegex = /\\*\\*Tags:\\*\\*\\s*([^<]+)(?=<\\/p>|$)/g;\n    // Replace matched tag sections with formatted tags\n    return content.replace(tagRegex, (match, tagList)=>{\n        // Split the tag list by commas and trim whitespace\n        const tags = tagList.split(',').map((tag)=>tag.trim()).filter(Boolean);\n        if (tags.length === 0) {\n            return match; // No valid tags found, return original text\n        }\n        // Create HTML for tags\n        const tagsHtml = tags.map((tag)=>`<span class=\"inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2\">${tag}</span>`).join('');\n        return `<div class=\"mt-4\"><span class=\"font-semibold\">Tags:</span> <div class=\"flex flex-wrap mt-1\">${tagsHtml}</div></div>`;\n    });\n}\n/**\n * Sanitize sensitive data before logging\n * Removes passwords, tokens, etc.\n */ const sanitizeData = (data)=>{\n    if (!data || typeof data !== 'object') return data;\n    // Create a shallow copy to avoid modifying the original\n    const sanitized = {\n        ...data\n    };\n    // List of sensitive fields to mask\n    const sensitiveFields = [\n        'password',\n        'passwordHash',\n        'token',\n        'secret',\n        'apiKey',\n        'authorization',\n        'accessToken',\n        'refreshToken',\n        'csrf',\n        'cookie',\n        'session',\n        'key',\n        'credential',\n        'auth'\n    ];\n    // Mask sensitive fields\n    Object.keys(sanitized).forEach((key)=>{\n        const lowerKey = key.toLowerCase();\n        if (sensitiveFields.some((field)=>lowerKey.includes(field))) {\n            sanitized[key] = '[REDACTED]';\n        } else if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {\n            // Recursively sanitize nested objects\n            sanitized[key] = sanitizeData(sanitized[key]);\n        }\n    });\n    return sanitized;\n};\n/**\n * Log information to the console in development mode\n */ const logInfo = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.log(message, sanitizeData(data));\n        } else {\n            console.log(message);\n        }\n    }\n};\n/**\n * Log errors to the console\n * Always logs in production, but sanitizes sensitive data\n */ const logError = (message, error)=>{\n    if (error instanceof Error) {\n        // For Error objects, log the message and stack\n        console.error(message, {\n            message: error.message,\n            stack: error.stack\n        });\n    } else if (error) {\n        // For other data, sanitize before logging\n        console.error(message, sanitizeData(error));\n    } else {\n        console.error(message);\n    }\n};\n/**\n * Log warnings to the console in development mode\n */ const logWarning = (message, data)=>{\n    if (true) {\n        if (data) {\n            console.warn(message, sanitizeData(data));\n        } else {\n            console.warn(message);\n        }\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCO0FBRUE7Ozs7O0NBS0MsR0FDTSxTQUFTQyxpQkFBaUJDLE9BQWU7SUFDOUMscUVBQXFFO0lBQ3JFLE1BQU1DLFdBQVc7SUFFakIsbURBQW1EO0lBQ25ELE9BQU9ELFFBQVFFLE9BQU8sQ0FBQ0QsVUFBVSxDQUFDRSxPQUFPQztRQUN2QyxtREFBbUQ7UUFDbkQsTUFBTUMsT0FBT0QsUUFBUUUsS0FBSyxDQUFDLEtBQUtDLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFBT0EsSUFBSUMsSUFBSSxJQUFJQyxNQUFNLENBQUNDO1FBRTlELElBQUlOLEtBQUtPLE1BQU0sS0FBSyxHQUFHO1lBQ3JCLE9BQU9ULE9BQU8sNENBQTRDO1FBQzVEO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1VLFdBQVdSLEtBQUtFLEdBQUcsQ0FBQ0MsQ0FBQUEsTUFDeEIsQ0FBQywwSEFBMEgsRUFBRUEsSUFBSSxPQUFPLENBQUMsRUFDeklNLElBQUksQ0FBQztRQUVQLE9BQU8sQ0FBQyw0RkFBNEYsRUFBRUQsU0FBUyxZQUFZLENBQUM7SUFDOUg7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLE1BQU1FLGVBQWUsQ0FBQ0M7SUFDM0IsSUFBSSxDQUFDQSxRQUFRLE9BQU9BLFNBQVMsVUFBVSxPQUFPQTtJQUU5Qyx3REFBd0Q7SUFDeEQsTUFBTUMsWUFBWTtRQUFFLEdBQUdELElBQUk7SUFBQztJQUU1QixtQ0FBbUM7SUFDbkMsTUFBTUUsa0JBQWtCO1FBQ3RCO1FBQVk7UUFBZ0I7UUFBUztRQUFVO1FBQy9DO1FBQWlCO1FBQWU7UUFBZ0I7UUFDaEQ7UUFBVTtRQUFXO1FBQU87UUFBYztLQUMzQztJQUVELHdCQUF3QjtJQUN4QkMsT0FBT0MsSUFBSSxDQUFDSCxXQUFXSSxPQUFPLENBQUNDLENBQUFBO1FBQzdCLE1BQU1DLFdBQVdELElBQUlFLFdBQVc7UUFDaEMsSUFBSU4sZ0JBQWdCTyxJQUFJLENBQUNDLENBQUFBLFFBQVNILFNBQVNJLFFBQVEsQ0FBQ0QsU0FBUztZQUMzRFQsU0FBUyxDQUFDSyxJQUFJLEdBQUc7UUFDbkIsT0FBTyxJQUFJLE9BQU9MLFNBQVMsQ0FBQ0ssSUFBSSxLQUFLLFlBQVlMLFNBQVMsQ0FBQ0ssSUFBSSxLQUFLLE1BQU07WUFDeEUsc0NBQXNDO1lBQ3RDTCxTQUFTLENBQUNLLElBQUksR0FBR1AsYUFBYUUsU0FBUyxDQUFDSyxJQUFJO1FBQzlDO0lBQ0Y7SUFFQSxPQUFPTDtBQUNULEVBQUU7QUFFRjs7Q0FFQyxHQUNNLE1BQU1XLFVBQVUsQ0FBQ0MsU0FBaUJiO0lBQ3ZDLElBQUljLElBQXFDLEVBQUU7UUFDekMsSUFBSWQsTUFBTTtZQUNSZSxRQUFRQyxHQUFHLENBQUNILFNBQVNkLGFBQWFDO1FBQ3BDLE9BQU87WUFDTGUsUUFBUUMsR0FBRyxDQUFDSDtRQUNkO0lBQ0Y7QUFDRixFQUFFO0FBRUY7OztDQUdDLEdBQ00sTUFBTUksV0FBVyxDQUFDSixTQUFpQks7SUFDeEMsSUFBSUEsaUJBQWlCQyxPQUFPO1FBQzFCLCtDQUErQztRQUMvQ0osUUFBUUcsS0FBSyxDQUFDTCxTQUFTO1lBQ3JCQSxTQUFTSyxNQUFNTCxPQUFPO1lBQ3RCTyxPQUFPRixNQUFNRSxLQUFLO1FBQ3BCO0lBQ0YsT0FBTyxJQUFJRixPQUFPO1FBQ2hCLDBDQUEwQztRQUMxQ0gsUUFBUUcsS0FBSyxDQUFDTCxTQUFTZCxhQUFhbUI7SUFDdEMsT0FBTztRQUNMSCxRQUFRRyxLQUFLLENBQUNMO0lBQ2hCO0FBQ0YsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTVEsYUFBYSxDQUFDUixTQUFpQmI7SUFDMUMsSUFBSWMsSUFBcUMsRUFBRTtRQUN6QyxJQUFJZCxNQUFNO1lBQ1JlLFFBQVFPLElBQUksQ0FBQ1QsU0FBU2QsYUFBYUM7UUFDckMsT0FBTztZQUNMZSxRQUFRTyxJQUFJLENBQUNUO1FBQ2Y7SUFDRjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFzaWJcXE9uZURyaXZlXFxEZXNrdG9wXFxjaGFyaXR5X2luZm9cXGNoYXJpdHlfaW5mb1xcY2hhcml0eS1uZXh0anNcXHNyY1xcbGliXFx1dGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjbHN4LCB0eXBlIENsYXNzVmFsdWUgfSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQgeyB0d01lcmdlIH0gZnJvbSBcInRhaWx3aW5kLW1lcmdlXCJcblxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0czogQ2xhc3NWYWx1ZVtdKSB7XG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSlcbn1cblxuLyoqXG4gKiBQYXJzZSBhbmQgZm9ybWF0IHRhZ3MgaW4gbmV3cyBjb250ZW50XG4gKiBDb252ZXJ0cyB0ZXh0IGluIHRoZSBmb3JtYXQgXCIqKlRhZ3M6KiogdGFnMSwgdGFnMiwgdGFnM1wiIHRvIEhUTUwgdGFnc1xuICogQHBhcmFtIGNvbnRlbnQgLSBUaGUgSFRNTCBjb250ZW50IHRvIHBhcnNlXG4gKiBAcmV0dXJucyBGb3JtYXR0ZWQgSFRNTCBjb250ZW50IHdpdGggdGFnc1xuICovXG5leHBvcnQgZnVuY3Rpb24gcGFyc2VOZXdzQ29udGVudChjb250ZW50OiBzdHJpbmcpOiBzdHJpbmcge1xuICAvLyBSZWd1bGFyIGV4cHJlc3Npb24gdG8gbWF0Y2ggdGFnIGZvcm1hdDogKipUYWdzOioqIHRhZzEsIHRhZzIsIHRhZzNcbiAgY29uc3QgdGFnUmVnZXggPSAvXFwqXFwqVGFnczpcXCpcXCpcXHMqKFtePF0rKSg/PTxcXC9wPnwkKS9nO1xuXG4gIC8vIFJlcGxhY2UgbWF0Y2hlZCB0YWcgc2VjdGlvbnMgd2l0aCBmb3JtYXR0ZWQgdGFnc1xuICByZXR1cm4gY29udGVudC5yZXBsYWNlKHRhZ1JlZ2V4LCAobWF0Y2gsIHRhZ0xpc3QpID0+IHtcbiAgICAvLyBTcGxpdCB0aGUgdGFnIGxpc3QgYnkgY29tbWFzIGFuZCB0cmltIHdoaXRlc3BhY2VcbiAgICBjb25zdCB0YWdzID0gdGFnTGlzdC5zcGxpdCgnLCcpLm1hcCh0YWcgPT4gdGFnLnRyaW0oKSkuZmlsdGVyKEJvb2xlYW4pO1xuXG4gICAgaWYgKHRhZ3MubGVuZ3RoID09PSAwKSB7XG4gICAgICByZXR1cm4gbWF0Y2g7IC8vIE5vIHZhbGlkIHRhZ3MgZm91bmQsIHJldHVybiBvcmlnaW5hbCB0ZXh0XG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIEhUTUwgZm9yIHRhZ3NcbiAgICBjb25zdCB0YWdzSHRtbCA9IHRhZ3MubWFwKHRhZyA9PlxuICAgICAgYDxzcGFuIGNsYXNzPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHJvdW5kZWQtZnVsbCBiZy10ZWFsLTEwMCBweC0yLjUgcHktMC41IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC10ZWFsLTgwMCBtci0yIG1iLTJcIj4ke3RhZ308L3NwYW4+YFxuICAgICkuam9pbignJyk7XG5cbiAgICByZXR1cm4gYDxkaXYgY2xhc3M9XCJtdC00XCI+PHNwYW4gY2xhc3M9XCJmb250LXNlbWlib2xkXCI+VGFnczo8L3NwYW4+IDxkaXYgY2xhc3M9XCJmbGV4IGZsZXgtd3JhcCBtdC0xXCI+JHt0YWdzSHRtbH08L2Rpdj48L2Rpdj5gO1xuICB9KTtcbn1cblxuLyoqXG4gKiBTYW5pdGl6ZSBzZW5zaXRpdmUgZGF0YSBiZWZvcmUgbG9nZ2luZ1xuICogUmVtb3ZlcyBwYXNzd29yZHMsIHRva2VucywgZXRjLlxuICovXG5leHBvcnQgY29uc3Qgc2FuaXRpemVEYXRhID0gKGRhdGE6IGFueSk6IGFueSA9PiB7XG4gIGlmICghZGF0YSB8fCB0eXBlb2YgZGF0YSAhPT0gJ29iamVjdCcpIHJldHVybiBkYXRhO1xuXG4gIC8vIENyZWF0ZSBhIHNoYWxsb3cgY29weSB0byBhdm9pZCBtb2RpZnlpbmcgdGhlIG9yaWdpbmFsXG4gIGNvbnN0IHNhbml0aXplZCA9IHsgLi4uZGF0YSB9O1xuXG4gIC8vIExpc3Qgb2Ygc2Vuc2l0aXZlIGZpZWxkcyB0byBtYXNrXG4gIGNvbnN0IHNlbnNpdGl2ZUZpZWxkcyA9IFtcbiAgICAncGFzc3dvcmQnLCAncGFzc3dvcmRIYXNoJywgJ3Rva2VuJywgJ3NlY3JldCcsICdhcGlLZXknLFxuICAgICdhdXRob3JpemF0aW9uJywgJ2FjY2Vzc1Rva2VuJywgJ3JlZnJlc2hUb2tlbicsICdjc3JmJyxcbiAgICAnY29va2llJywgJ3Nlc3Npb24nLCAna2V5JywgJ2NyZWRlbnRpYWwnLCAnYXV0aCdcbiAgXTtcblxuICAvLyBNYXNrIHNlbnNpdGl2ZSBmaWVsZHNcbiAgT2JqZWN0LmtleXMoc2FuaXRpemVkKS5mb3JFYWNoKGtleSA9PiB7XG4gICAgY29uc3QgbG93ZXJLZXkgPSBrZXkudG9Mb3dlckNhc2UoKTtcbiAgICBpZiAoc2Vuc2l0aXZlRmllbGRzLnNvbWUoZmllbGQgPT4gbG93ZXJLZXkuaW5jbHVkZXMoZmllbGQpKSkge1xuICAgICAgc2FuaXRpemVkW2tleV0gPSAnW1JFREFDVEVEXSc7XG4gICAgfSBlbHNlIGlmICh0eXBlb2Ygc2FuaXRpemVkW2tleV0gPT09ICdvYmplY3QnICYmIHNhbml0aXplZFtrZXldICE9PSBudWxsKSB7XG4gICAgICAvLyBSZWN1cnNpdmVseSBzYW5pdGl6ZSBuZXN0ZWQgb2JqZWN0c1xuICAgICAgc2FuaXRpemVkW2tleV0gPSBzYW5pdGl6ZURhdGEoc2FuaXRpemVkW2tleV0pO1xuICAgIH1cbiAgfSk7XG5cbiAgcmV0dXJuIHNhbml0aXplZDtcbn07XG5cbi8qKlxuICogTG9nIGluZm9ybWF0aW9uIHRvIHRoZSBjb25zb2xlIGluIGRldmVsb3BtZW50IG1vZGVcbiAqL1xuZXhwb3J0IGNvbnN0IGxvZ0luZm8gPSAobWVzc2FnZTogc3RyaW5nLCBkYXRhPzogYW55KTogdm9pZCA9PiB7XG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgaWYgKGRhdGEpIHtcbiAgICAgIGNvbnNvbGUubG9nKG1lc3NhZ2UsIHNhbml0aXplRGF0YShkYXRhKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGNvbnNvbGUubG9nKG1lc3NhZ2UpO1xuICAgIH1cbiAgfVxufTtcblxuLyoqXG4gKiBMb2cgZXJyb3JzIHRvIHRoZSBjb25zb2xlXG4gKiBBbHdheXMgbG9ncyBpbiBwcm9kdWN0aW9uLCBidXQgc2FuaXRpemVzIHNlbnNpdGl2ZSBkYXRhXG4gKi9cbmV4cG9ydCBjb25zdCBsb2dFcnJvciA9IChtZXNzYWdlOiBzdHJpbmcsIGVycm9yPzogYW55KTogdm9pZCA9PiB7XG4gIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgLy8gRm9yIEVycm9yIG9iamVjdHMsIGxvZyB0aGUgbWVzc2FnZSBhbmQgc3RhY2tcbiAgICBjb25zb2xlLmVycm9yKG1lc3NhZ2UsIHtcbiAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2UsXG4gICAgICBzdGFjazogZXJyb3Iuc3RhY2ssXG4gICAgfSk7XG4gIH0gZWxzZSBpZiAoZXJyb3IpIHtcbiAgICAvLyBGb3Igb3RoZXIgZGF0YSwgc2FuaXRpemUgYmVmb3JlIGxvZ2dpbmdcbiAgICBjb25zb2xlLmVycm9yKG1lc3NhZ2UsIHNhbml0aXplRGF0YShlcnJvcikpO1xuICB9IGVsc2Uge1xuICAgIGNvbnNvbGUuZXJyb3IobWVzc2FnZSk7XG4gIH1cbn07XG5cbi8qKlxuICogTG9nIHdhcm5pbmdzIHRvIHRoZSBjb25zb2xlIGluIGRldmVsb3BtZW50IG1vZGVcbiAqL1xuZXhwb3J0IGNvbnN0IGxvZ1dhcm5pbmcgPSAobWVzc2FnZTogc3RyaW5nLCBkYXRhPzogYW55KTogdm9pZCA9PiB7XG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgaWYgKGRhdGEpIHtcbiAgICAgIGNvbnNvbGUud2FybihtZXNzYWdlLCBzYW5pdGl6ZURhdGEoZGF0YSkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLndhcm4obWVzc2FnZSk7XG4gICAgfVxuICB9XG59O1xuXG5cbiJdLCJuYW1lcyI6WyJjbHN4IiwidHdNZXJnZSIsImNuIiwiaW5wdXRzIiwicGFyc2VOZXdzQ29udGVudCIsImNvbnRlbnQiLCJ0YWdSZWdleCIsInJlcGxhY2UiLCJtYXRjaCIsInRhZ0xpc3QiLCJ0YWdzIiwic3BsaXQiLCJtYXAiLCJ0YWciLCJ0cmltIiwiZmlsdGVyIiwiQm9vbGVhbiIsImxlbmd0aCIsInRhZ3NIdG1sIiwiam9pbiIsInNhbml0aXplRGF0YSIsImRhdGEiLCJzYW5pdGl6ZWQiLCJzZW5zaXRpdmVGaWVsZHMiLCJPYmplY3QiLCJrZXlzIiwiZm9yRWFjaCIsImtleSIsImxvd2VyS2V5IiwidG9Mb3dlckNhc2UiLCJzb21lIiwiZmllbGQiLCJpbmNsdWRlcyIsImxvZ0luZm8iLCJtZXNzYWdlIiwicHJvY2VzcyIsImNvbnNvbGUiLCJsb2ciLCJsb2dFcnJvciIsImVycm9yIiwiRXJyb3IiLCJzdGFjayIsImxvZ1dhcm5pbmciLCJ3YXJuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/@tanstack","vendor-chunks/lucide-react","vendor-chunks/web-vitals","vendor-chunks/@radix-ui","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/date-fns"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fnews%2Fpage&page=%2Fnews%2Fpage&appPaths=%2Fnews%2Fpage&pagePath=private-next-app-dir%2Fnews%2Fpage.tsx&appDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Chasib%5COneDrive%5CDesktop%5Ccharity_info%5Ccharity_info%5Ccharity-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();