(()=>{var e={};e.id=324,e.ids=[324],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14389:(e,a,t)=>{Promise.resolve().then(t.bind(t,99531))},14952:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24746:(e,a,t)=>{"use strict";t.d(a,{NewsPageContent:()=>k});var s=t(60687),r=t(43210),i=t(51423),n=t(85814),l=t.n(n),o=t(43125),c=t(40228),d=t(70334),p=t(44867),m=t(44493),u=t(47033),h=t(14952);let x=(0,t(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);var y=t(4780),f=t(29523);let g=({className:e,...a})=>(0,s.jsx)("nav",{role:"navigation","aria-label":"pagination",className:(0,y.cn)("mx-auto flex w-full justify-center",e),...a});g.displayName="Pagination";let w=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("ul",{ref:t,className:(0,y.cn)("flex flex-row items-center gap-1",e),...a}));w.displayName="PaginationContent";let v=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("li",{ref:t,className:(0,y.cn)("",e),...a}));v.displayName="PaginationItem";let j=({className:e,isActive:a,size:t="icon",...r})=>(0,s.jsx)("a",{"aria-current":a?"page":void 0,className:(0,y.cn)((0,f.r)({variant:a?"outline":"ghost",size:t}),e),...r});j.displayName="PaginationLink";let N=({className:e,...a})=>(0,s.jsxs)(j,{"aria-label":"Go to previous page",size:"default",className:(0,y.cn)("gap-1 pl-2.5",e),...a,children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Previous"})]});N.displayName="PaginationPrevious";let b=({className:e,...a})=>(0,s.jsxs)(j,{"aria-label":"Go to next page",size:"default",className:(0,y.cn)("gap-1 pr-2.5",e),...a,children:[(0,s.jsx)("span",{children:"Next"}),(0,s.jsx)(h.A,{className:"h-4 w-4"})]});b.displayName="PaginationNext";let A=({className:e,...a})=>(0,s.jsxs)("span",{"aria-hidden":!0,className:(0,y.cn)("flex h-9 w-9 items-center justify-center",e),...a,children:[(0,s.jsx)(x,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"More pages"})]});A.displayName="PaginationEllipsis";var C=t(62185);function k(){let[e,a]=(0,r.useState)(1),[t,n]=(0,r.useState)({}),[u,h]=(0,r.useState)({}),{data:x,isLoading:f,error:k}=(0,i.I)({queryKey:["news",e],queryFn:()=>C.AY.getAll(e)}),P=e=>{h(a=>({...a,[e]:!0}))},_=e=>{n(a=>{let t={...a};return delete t[e],t}),h(a=>({...a,[e]:!0}))};return f?(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsx)("div",{className:"flex justify-center items-center min-h-[400px]",children:(0,s.jsx)(o.A,{className:"h-8 w-8 animate-spin text-primary"})})}):k||!x?(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)(m.Zp,{className:"border-red-200 bg-red-50",children:[(0,s.jsxs)(m.aR,{children:[(0,s.jsx)(m.ZB,{className:"text-red-800",children:"Error"}),(0,s.jsx)(m.BT,{className:"text-red-700",children:"Failed to load news"})]}),(0,s.jsx)(m.Wu,{children:(0,s.jsx)("p",{className:"text-red-600",children:k instanceof Error?k.message:"Unknown error occurred"})})]})}):(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Latest News"}),(0,s.jsx)("div",{className:"news-grid",children:x.news.map((e,a)=>{let r=t[e._id],i=u[e._id]||!r;return(0,s.jsx)(l(),{href:`/news/${e.slug}`,className:"group block",children:(0,s.jsxs)(m.Zp,{className:(0,y.cn)("overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 h-full flex flex-col",0===a&&"md:col-span-2 md:row-span-2"),children:[r&&(0,s.jsxs)("div",{className:(0,y.cn)("relative overflow-hidden bg-gray-100",0===a?"aspect-[16/10]":"aspect-video"),children:[!i&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)(o.A,{className:"h-6 w-6 animate-spin text-gray-400"})}),(0,s.jsx)("img",{src:r,alt:e.title,className:(0,y.cn)("w-full h-full object-cover transition-all duration-700 group-hover:scale-105",!i&&"opacity-0"),onLoad:()=>P(e._id),onError:()=>_(e._id),loading:"lazy"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,s.jsxs)(m.Wu,{className:"p-5",children:[(0,s.jsx)("h3",{className:"text-xl font-bold mb-2 group-hover:text-primary transition-colors duration-300",children:e.title}),(0,s.jsx)("div",{className:"line-clamp-3 text-sm text-gray-600",dangerouslySetInnerHTML:{__html:(0,y.oH)(e.body.substring(0,150)+"...")}})]}),(0,s.jsxs)(m.wL,{className:"px-5 pb-5 pt-0 flex flex-col gap-2 w-full",children:[(0,s.jsxs)("div",{className:"flex items-center text-gray-500 w-full",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),(0,p.GP)(new Date(e.publishDate),"MMMM d, yyyy")]}),(0,s.jsxs)("div",{className:"text-primary font-medium flex items-center w-full",children:["Read More ",(0,s.jsx)(d.A,{className:"h-4 w-4 ml-1 transition-transform duration-300 group-hover:translate-x-1"})]})]})]})},e._id)})}),x.pagination&&x.pagination.pages>1&&(0,s.jsx)(g,{className:"mt-8",children:(0,s.jsxs)(w,{children:[(0,s.jsx)(v,{children:(0,s.jsx)(N,{href:"#",onClick:t=>{t.preventDefault(),e>1&&a(e-1)},className:1===e?"pointer-events-none opacity-50":""})}),e>2&&(0,s.jsx)(v,{children:(0,s.jsx)(j,{href:"#",onClick:e=>{e.preventDefault(),a(1)},children:"1"})}),e>3&&(0,s.jsx)(v,{children:(0,s.jsx)(A,{})}),(0,s.jsx)(v,{children:(0,s.jsx)(j,{href:"#",isActive:!0,children:e})}),e<x.pagination.pages&&(0,s.jsx)(v,{children:(0,s.jsx)(j,{href:"#",onClick:t=>{t.preventDefault(),a(e+1)},children:e+1})}),e<x.pagination.pages-2&&(0,s.jsx)(v,{children:(0,s.jsx)(A,{})}),e<x.pagination.pages-1&&(0,s.jsx)(v,{children:(0,s.jsx)(j,{href:"#",onClick:e=>{e.preventDefault(),a(x.pagination.pages)},children:x.pagination.pages})}),(0,s.jsx)(v,{children:(0,s.jsx)(b,{href:"#",onClick:t=>{t.preventDefault(),e<x.pagination.pages&&a(e+1)},className:e===x.pagination.pages?"pointer-events-none opacity-50":""})})]})})]})}t(70216)},26380:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>n});var s=t(37413),r=t(6972),i=t(78963);function n(){return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)(r.E,{className:"h-8 w-48 mb-6"}),(0,s.jsx)("div",{className:"news-grid",children:[...Array(6)].map((e,a)=>(0,s.jsxs)(i.Zp,{className:"overflow-hidden",children:[(0,s.jsx)(r.E,{className:"aspect-video w-full"}),(0,s.jsxs)(i.Wu,{className:"p-5",children:[(0,s.jsx)(r.E,{className:"h-6 w-full mb-2"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(r.E,{className:"h-4 w-full"}),(0,s.jsx)(r.E,{className:"h-4 w-3/4"}),(0,s.jsx)(r.E,{className:"h-4 w-1/2"})]})]}),(0,s.jsxs)(i.wL,{className:"px-5 pb-5 pt-0 flex flex-col gap-2",children:[(0,s.jsx)(r.E,{className:"h-4 w-32"}),(0,s.jsx)(r.E,{className:"h-4 w-24"})]})]},a))})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33095:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>n,metadata:()=>i});var s=t(37413),r=t(99531);let i={title:"Latest News - Charity Welcome Hub",description:"Stay updated with the latest news, announcements, and stories from our charity organization and community impact.",keywords:["news","updates","announcements","charity news","community","stories","impact"],openGraph:{title:"Latest News - Charity Welcome Hub",description:"Stay updated with the latest news, announcements, and stories from our charity organization and community impact.",type:"website"},twitter:{card:"summary_large_image",title:"Latest News - Charity Welcome Hub",description:"Stay updated with the latest news, announcements, and stories from our charity organization and community impact."}};function n(){return(0,s.jsx)(r.NewsPageContent,{})}},33873:e=>{"use strict";e.exports=require("path")},43125:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("loader",[["path",{d:"M12 2v4",key:"3427ic"}],["path",{d:"m16.2 7.8 2.9-2.9",key:"r700ao"}],["path",{d:"M18 12h4",key:"wj9ykh"}],["path",{d:"m16.2 16.2 2.9 2.9",key:"1bxg5t"}],["path",{d:"M12 18v4",key:"jadmvz"}],["path",{d:"m4.9 19.1 2.9-2.9",key:"bwix9q"}],["path",{d:"M2 12h4",key:"j09sii"}],["path",{d:"m4.9 4.9 2.9 2.9",key:"giyufr"}]])},44493:(e,a,t)=>{"use strict";t.d(a,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>l,wL:()=>p});var s=t(60687),r=t(43210),i=t(4780);let n=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...a}));n.displayName="Card";let l=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...a}));l.displayName="CardHeader";let o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));o.displayName="CardTitle";let c=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...a}));c.displayName="CardDescription";let d=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...a}));d.displayName="CardContent";let p=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...a}));p.displayName="CardFooter"},47033:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56245:(e,a,t)=>{Promise.resolve().then(t.bind(t,24746))},62185:(e,a,t)=>{"use strict";t.d(a,{AY:()=>p,EO:()=>n,TP:()=>o,YV:()=>l,jE:()=>m,l4:()=>d,lM:()=>c});var s=t(51060),r=t(70216);let i=s.A.create({baseURL:r.A.baseURL,timeout:r.A.timeout,withCredentials:r.A.withCredentials});i.interceptors.request.use(async e=>e,e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response?.status,Promise.reject(e)));let n={getContent:async()=>(await i.get("/about")).data,updateContent:async e=>(await i.post("/about",e)).data},l={getAll:async e=>({teamMembers:(await i.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await i.get(`/team/${e}`)).data}),create:async e=>(await i.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,a)=>(await i.put(`/team/${e}`,a,{headers:{"Content-Type":"multipart/form-data"}})).data,delete:async e=>(await i.delete(`/team/${e}`)).data},o={submit:async e=>(await i.post("/contact",e)).data,getAll:async(e=1,a=10,t)=>{let s={page:e,limit:a,...t?{status:t}:{}};return(await i.get("/contact",{params:s})).data},getById:async e=>(await i.get(`/contact/${e}`)).data,updateStatus:async(e,a)=>(await i.put(`/contact/${e}/status`,{status:a})).data,delete:async e=>(await i.delete(`/contact/${e}`)).data},c={getAll:async e=>({locations:(await i.get("/locations",{params:e})).data}),getById:async e=>({location:(await i.get(`/locations/${e}`)).data}),create:async e=>{let a={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await i.post("/locations",a)).data},update:async(e,a)=>{let t={...a,isMainOffice:void 0!==a.isMainOffice?String(a.isMainOffice):void 0,active:void 0!==a.active?String(a.active):void 0};return(await i.put(`/locations/${e}`,t)).data},delete:async e=>(await i.delete(`/locations/${e}`)).data},d={getAll:async()=>(await i.get("/faqs")).data,getAllAdmin:async()=>(await i.get("/admin/faqs")).data,getById:async e=>(await i.get(`/admin/faqs/${e}`)).data,create:async e=>(await i.post("/admin/faqs",e)).data,update:async(e,a,t=!1)=>{let s;s=t?{isActive:void 0===a.isActive||!!a.isActive}:{question:a.question?.trim(),answer:a.answer?.trim(),category:a.category?.trim()||"General",order:"number"==typeof a.order?a.order:0,isActive:void 0===a.isActive||!!a.isActive};try{return(await i.put(`/admin/faqs/${e}`,s)).data}catch(e){throw e}},delete:async e=>(await i.delete(`/admin/faqs/${e}`)).data},p={getAll:async(e=1,a=10)=>(await i.get(`/news?page=${e}&limit=${a}&includeAttachments=true`)).data,getBySlug:async e=>(await i.get(`/news/${e}?includeAttachments=true`)).data.news,getById:async e=>(await i.get(`/admin/news/${e}`)).data.news,create:async e=>(await i.post("/admin/news",e)).data.news,update:async(e,a)=>(await i.put(`/admin/news/${e}`,a)).data.news,delete:async e=>(await i.delete(`/admin/news/${e}`)).data},m={getAllAlbums:async()=>(await i.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await i.get(`/gallery/albums/${e}`)).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await i.get(`/admin/gallery/albums/${e}`)).data}catch(e){throw e}},createAlbum:async e=>(await i.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,a)=>(await i.put(`/admin/gallery/albums/${e}`,a)).data.album,deleteAlbum:async e=>{await i.delete(`/admin/gallery/albums/${e}`)},uploadImage:async(e,a)=>{let t=document.querySelector('meta[name="csrf-token"]')?.getAttribute("content");return t&&a.append("_csrf",t),(await i.post(`/admin/gallery/albums/${e}/images`,a,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await i.delete(`/admin/gallery/images/${e}`)}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70216:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},70334:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},74075:e=>{"use strict";e.exports=require("zlib")},78963:(e,a,t)=>{"use strict";t.d(a,{Wu:()=>l,Zp:()=>n,wL:()=>o});var s=t(37413),r=t(61120),i=t(10974);let n=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...a}));n.displayName="Card",r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...a})).displayName="CardHeader",r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a})).displayName="CardTitle",r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...a})).displayName="CardDescription";let l=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...a}));l.displayName="CardContent";let o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...a}));o.displayName="CardFooter"},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},93597:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(a,o);let c={children:["",{children:["news",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,33095)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,26380)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,68178)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,99766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/news/page",pathname:"/news",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94735:e=>{"use strict";e.exports=require("events")},99531:(e,a,t)=>{"use strict";t.d(a,{NewsPageContent:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call NewsPageContent() from the server but NewsPageContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\news\\NewsPageContent.tsx","NewsPageContent")}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[447,699,62,867,775],()=>t(93597));module.exports=s})();