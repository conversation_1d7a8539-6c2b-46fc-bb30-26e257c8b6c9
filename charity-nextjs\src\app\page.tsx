"use client";

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar, Heart, Gift, Users, Image, ArrowRight, Mail } from 'lucide-react';
import Link from 'next/link';
import { useQuery } from '@tanstack/react-query';
import { newsApi, galleryApi } from '@/lib/services';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { format } from 'date-fns';
import NewsletterSignup from '@/components/NewsletterSignup';
import PartnersSection from '@/components/PartnersSection';
import InteractiveMap from '@/components/InteractiveMap';
import HeroSlider from '@/components/HeroSlider';
import DonationGoals from '@/components/DonationGoals';
import API_CONFIG from '@/config/api';
import NextImage from 'next/image';

export default function HomePage() {
  const [newsThumbnails, setNewsThumbnails] = useState<Record<string, string>>({});
  const [galleryThumbnails, setGalleryThumbnails] = useState<Record<string, string>>({});

  // Fetch latest news
  const {
    data: newsData,
    isLoading: isNewsLoading,
    isError: isNewsError
  } = useQuery({
    queryKey: ['latestNews'],
    queryFn: () => newsApi.getAll(1, 3), // Get first page with 3 items
  });

  // Fetch gallery albums
  const {
    data: galleryData,
    isLoading: isGalleryLoading,
    isError: isGalleryError
  } = useQuery({
    queryKey: ['galleryAlbums'],
    queryFn: galleryApi.getAllAlbums,
  });

  // Set news thumbnails
  useEffect(() => {
    const fetchNewsThumbnails = async () => {
      if (newsData?.news && newsData.news.length > 0) {
        const thumbnails: Record<string, string> = {};

        for (const article of newsData.news) {
          try {
            // Check if article has attachments
            if (article.attachments && article.attachments.length > 0) {
              // Find the first image attachment
              const firstImage = article.attachments.find((att: any) =>
                att.mimeType && att.mimeType.startsWith('image/'));

              if (firstImage && firstImage._id) {
                const imageUrl = `${API_CONFIG.backendURL}/api/news/attachments/${firstImage._id}/content`;
                thumbnails[article._id] = imageUrl;
                continue;
              }
            }

            // If no attachments in the article data, fetch the full article
            const newsItem = await newsApi.getBySlug(article.slug);

            if (newsItem.attachments && newsItem.attachments.length > 0) {
              // Find the first image attachment
              const firstImage = newsItem.attachments.find((att: any) =>
                att.mimeType && att.mimeType.startsWith('image/'));

              if (firstImage && firstImage._id) {
                const imageUrl = `${API_CONFIG.backendURL}/api/news/attachments/${firstImage._id}/content`;
                thumbnails[article._id] = imageUrl;
              }
            }
          } catch (error) {
            console.error(`Error setting thumbnail for article ${article._id}:`, error);
          }
        }

        setNewsThumbnails(thumbnails);
      }
    };

    fetchNewsThumbnails();
  }, [newsData?.news]);

  // Set gallery thumbnails
  useEffect(() => {
    const fetchGalleryThumbnails = async () => {
      if (galleryData?.albums && galleryData.albums.length > 0) {
        const thumbnails: Record<string, string> = {};

        // Only process the first 3 albums
        const albumsToProcess = galleryData.albums.slice(0, 3);

        for (const album of albumsToProcess) {
          try {
            // Get the first image of each album to use as thumbnail
            const albumDetail = await galleryApi.getAlbumBySlug(album.slug);
            if (albumDetail.images && albumDetail.images.length > 0) {
              const firstImage = albumDetail.images[0];
              thumbnails[album._id] = `${API_CONFIG.backendURL}/uploads/gallery/${album._id}/${firstImage.filename}`;
            }
          } catch (error) {
            console.error(`Error fetching thumbnail for album ${album.title}:`, error);
          }
        }

        setGalleryThumbnails(thumbnails);
      }
    };

    fetchGalleryThumbnails();
  }, [galleryData?.albums]);

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <HeroSlider />

      {/* Impact Stats */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12 text-teal-800">Our Collective Impact</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6 rounded-lg border border-teal-100 shadow-sm hover:shadow-md transition-shadow">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-teal-100 text-teal-600 mb-4">
                <Heart className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold text-teal-700 mb-2">10,000+</h3>
              <p className="text-teal-600">Lives Changed</p>
            </div>

            <div className="text-center p-6 rounded-lg border border-teal-100 shadow-sm hover:shadow-md transition-shadow">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-teal-100 text-teal-600 mb-4">
                <Gift className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold text-teal-700 mb-2">$2.5M</h3>
              <p className="text-teal-600">Donations Facilitated</p>
            </div>

            <div className="text-center p-6 rounded-lg border border-teal-100 shadow-sm hover:shadow-md transition-shadow">
              <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-teal-100 text-teal-600 mb-4">
                <Users className="h-8 w-8" />
              </div>
              <h3 className="text-2xl font-bold text-teal-700 mb-2">500+</h3>
              <p className="text-teal-600">Community Partners</p>
            </div>
          </div>
        </div>
      </section>

      {/* Latest News Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-teal-800">Latest News</h2>
            <Button asChild variant="ghost" className="text-teal-600 hover:text-teal-700">
              <Link href="/news" className="flex items-center">
                View All <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>

          {isNewsLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="animate-pulse overflow-hidden flex flex-col h-full">
                  <div className="aspect-video bg-gray-200"></div>
                  <CardContent className="p-4 flex-grow">
                    <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-100 rounded w-1/2 mb-4"></div>
                    <div className="h-4 bg-gray-100 rounded w-full mb-2"></div>
                    <div className="h-4 bg-gray-100 rounded w-full"></div>
                  </CardContent>
                  <CardFooter className="px-4 pb-4 pt-0 flex flex-col gap-2 w-full mt-auto">
                    <div className="h-4 bg-gray-100 rounded w-1/3 mb-2"></div>
                    <div className="h-8 bg-gray-100 rounded w-full"></div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          ) : isNewsError ? (
            <div className="text-center py-8 bg-red-50 rounded-lg">
              <p className="text-red-600">Failed to load latest news.</p>
            </div>
          ) : !newsData?.news || newsData.news.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <p className="text-gray-600">No news articles available.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {newsData.news.map((article) => (
                <Card key={article._id} className="overflow-hidden hover:shadow-md transition-shadow flex flex-col h-full">
                  <div className="aspect-video bg-gray-100 overflow-hidden">
                    {newsThumbnails[article._id] ? (
                      <img
                        src={newsThumbnails[article._id]}
                        alt={article.title}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-teal-50">
                        <Calendar className="h-12 w-12 text-teal-300" />
                      </div>
                    )}
                  </div>
                  <CardContent className="p-4 flex-grow">
                    <h3 className="font-semibold text-lg mb-2 text-teal-800 line-clamp-2">
                      {article.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-4 line-clamp-3">
                      {article.content.replace(/<[^>]*>/g, '').substring(0, 150)}...
                    </p>
                  </CardContent>
                  <CardFooter className="px-4 pb-4 pt-0 flex flex-col gap-2 w-full mt-auto">
                    <p className="text-xs text-gray-500 mb-2">
                      {format(new Date(article.publishedAt), 'MMM dd, yyyy')}
                    </p>
                    <Button asChild size="sm" className="w-full">
                      <Link href={`/news/${article.slug}`}>
                        Read More
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-16 bg-teal-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-teal-800">Photo Gallery</h2>
            <Button asChild variant="ghost" className="text-teal-600 hover:text-teal-700">
              <Link href="/gallery" className="flex items-center">
                View All <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>

          {isGalleryLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="animate-pulse overflow-hidden">
                  <div className="aspect-video bg-gray-200"></div>
                  <CardContent className="p-4">
                    <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-4 bg-gray-100 rounded w-full"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : isGalleryError ? (
            <div className="text-center py-8 bg-red-50 rounded-lg">
              <p className="text-red-600">Failed to load gallery albums.</p>
            </div>
          ) : !galleryData?.albums || galleryData.albums.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <p className="text-gray-600">No gallery albums available.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {galleryData.albums.slice(0, 3).map((album) => (
                <Card key={album._id} className="overflow-hidden hover:shadow-md transition-shadow">
                  <div className="aspect-video bg-gray-100 overflow-hidden">
                    {galleryThumbnails[album._id] ? (
                      <img
                        src={galleryThumbnails[album._id]}
                        alt={album.title}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-teal-50">
                        <Image className="h-12 w-12 text-teal-300" />
                      </div>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-2 text-teal-800">
                      {album.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-4">
                      {album.description}
                    </p>
                    <Button asChild size="sm" variant="outline" className="w-full">
                      <Link href={`/gallery/${album.slug}`}>
                        View Album
                      </Link>
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Migration Status */}
      <section className="py-8 bg-primary/5">
        <div className="container mx-auto px-4">
          <Card className="bg-white border-primary/20">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-primary mb-4">🚀 Next.js Migration Progress</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✅</span>
                  <span className="text-sm">Project Setup</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✅</span>
                  <span className="text-sm">Components</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-green-600">✅</span>
                  <span className="text-sm">Layout System</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-yellow-600">🔄</span>
                  <span className="text-sm">Pages Migration</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
