(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[235],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>c});var n=a(5155),r=a(2115),l=a(9708),i=a(2085),s=a(9434);let c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200",{variants:{variant:{default:"bg-charity-primary text-white hover:bg-charity-secondary",destructive:"bg-charity-destructive text-white hover:bg-charity-destructive/90",outline:"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground",secondary:"bg-charity-light text-charity-dark hover:bg-charity-light/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-charity-primary underline-offset-4 hover:underline",accent:"bg-charity-accent text-charity-dark hover:bg-charity-accent/80",success:"bg-charity-success text-white hover:bg-charity-success/90",warning:"bg-charity-warning text-white hover:bg-charity-warning/90",info:"bg-charity-info text-white hover:bg-charity-info/90",donate:"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 py-1.5 text-xs",lg:"h-11 rounded-md px-8 py-2.5",xl:"h-12 rounded-md px-10 py-3 text-base",icon:"h-10 w-10 rounded-full",wide:"h-10 px-8 py-2 w-full"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,t)=>{let{className:a,variant:r,size:i,asChild:o=!1,...d}=e,u=o?l.DX:"button";return(0,n.jsx)(u,{className:(0,s.cn)(c({variant:r,size:i,className:a})),ref:t,...d})});o.displayName="Button"},1008:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},2085:(e,t,a)=>{"use strict";a.d(t,{F:()=>i});var n=a(2596);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,i=(e,t)=>a=>{var n;if((null==t?void 0:t.variants)==null)return l(e,null==a?void 0:a.class,null==a?void 0:a.className);let{variants:i,defaultVariants:s}=t,c=Object.keys(i).map(e=>{let t=null==a?void 0:a[e],n=null==s?void 0:s[e];if(null===t)return null;let l=r(t)||r(n);return i[e][l]}),o=a&&Object.entries(a).reduce((e,t)=>{let[a,n]=t;return void 0===n||(e[a]=n),e},{});return l(e,c,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:a,className:n,...r}=t;return Object.entries(r).every(e=>{let[t,a]=e;return Array.isArray(a)?a.includes({...s,...o}[t]):({...s,...o})[t]===a})?[...e,a,n]:e},[]),null==a?void 0:a.class,null==a?void 0:a.className)}},5731:(e,t,a)=>{"use strict";a.d(t,{AY:()=>u,EO:()=>i,TP:()=>c,YV:()=>s,jE:()=>m,l4:()=>d,lM:()=>o});var n=a(3464),r=a(1008);let l=n.A.create({baseURL:r.A.baseURL,timeout:r.A.timeout,withCredentials:r.A.withCredentials});l.interceptors.request.use(async e=>{{let t=localStorage.getItem("authToken");t&&(e.headers.Authorization="Bearer ".concat(t))}return e},e=>Promise.reject(e)),l.interceptors.response.use(e=>e,e=>{var t;return(null==(t=e.response)?void 0:t.status)===401&&(localStorage.removeItem("authToken"),window.location.href="/login"),Promise.reject(e)});let i={getContent:async()=>(await l.get("/about")).data,updateContent:async e=>(await l.post("/about",e)).data},s={getAll:async e=>({teamMembers:(await l.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await l.get("/team/".concat(e))).data}),create:async e=>(await l.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,t)=>(await l.put("/team/".concat(e),t,{headers:{"Content-Type":"multipart/form-data"}})).data,patch:async(e,t)=>(await l.patch("/team/".concat(e),t)).data,delete:async e=>(await l.delete("/team/".concat(e))).data},c={submit:async e=>(await l.post("/contact",e)).data,getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=arguments.length>2?arguments[2]:void 0,n={page:e,limit:t,...a?{status:a}:{}};return(await l.get("/contact",{params:n})).data},getById:async e=>(await l.get("/contact/".concat(e))).data,updateStatus:async(e,t)=>(await l.put("/contact/".concat(e,"/status"),{status:t})).data,delete:async e=>(await l.delete("/contact/".concat(e))).data},o={getAll:async e=>({locations:(await l.get("/locations",{params:e})).data}),getById:async e=>({location:(await l.get("/locations/".concat(e))).data}),create:async e=>{let t={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await l.post("/locations",t)).data},update:async(e,t)=>{let a={...t,isMainOffice:void 0!==t.isMainOffice?String(t.isMainOffice):void 0,active:void 0!==t.active?String(t.active):void 0};return(await l.put("/locations/".concat(e),a)).data},delete:async e=>(await l.delete("/locations/".concat(e))).data},d={getAll:async()=>(await l.get("/faqs")).data,getAllAdmin:async()=>(await l.get("/admin/faqs")).data,getById:async e=>(await l.get("/admin/faqs/".concat(e))).data,create:async e=>(await l.post("/admin/faqs",e)).data,update:async function(e,t){let a,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(n)a={isActive:void 0===t.isActive||!!t.isActive};else{var r,i,s;a={question:null==(r=t.question)?void 0:r.trim(),answer:null==(i=t.answer)?void 0:i.trim(),category:(null==(s=t.category)?void 0:s.trim())||"General",order:"number"==typeof t.order?t.order:0,isActive:void 0===t.isActive||!!t.isActive}}try{return(await l.put("/admin/faqs/".concat(e),a)).data}catch(e){throw e}},delete:async e=>(await l.delete("/admin/faqs/".concat(e))).data},u={getAll:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10;return(await l.get("/news?page=".concat(e,"&limit=").concat(t,"&includeAttachments=true"))).data},getBySlug:async e=>(await l.get("/news/".concat(e,"?includeAttachments=true"))).data.news,getById:async e=>(await l.get("/admin/news/".concat(e))).data.news,create:async e=>(await l.post("/admin/news",e)).data.news,update:async(e,t)=>(await l.put("/admin/news/".concat(e),t)).data.news,delete:async e=>(await l.delete("/admin/news/".concat(e))).data},m={getAllAlbums:async()=>(await l.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await l.get("/gallery/albums/".concat(e))).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await l.get("/admin/gallery/albums/".concat(e))).data}catch(e){throw e}},createAlbum:async e=>(await l.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,t)=>(await l.put("/admin/gallery/albums/".concat(e),t)).data.album,deleteAlbum:async e=>{await l.delete("/admin/gallery/albums/".concat(e))},uploadImage:async(e,t)=>{var a;let n=null==(a=document.querySelector('meta[name="csrf-token"]'))?void 0:a.getAttribute("content");return n&&t.append("_csrf",n),(await l.post("/admin/gallery/albums/".concat(e,"/images"),t,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await l.delete("/admin/gallery/images/".concat(e))}}},5832:(e,t,a)=>{Promise.resolve().then(a.bind(a,7063))},6101:(e,t,a)=>{"use strict";a.d(t,{s:()=>i,t:()=>l});var n=a(2115);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let a=!1,n=e.map(e=>{let n=r(e,t);return a||"function"!=typeof n||(a=!0),n});if(a)return()=>{for(let t=0;t<n.length;t++){let a=n[t];"function"==typeof a?a():r(e[t],null)}}}}function i(...e){return n.useCallback(l(...e),e)}},6695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>s,wL:()=>u});var n=a(5155),r=a(2115),l=a(9434);let i=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",a),...r})});i.displayName="Card";let s=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",a),...r})});s.displayName="CardHeader";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",a),...r})});c.displayName="CardTitle";let o=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",a),...r})});o.displayName="CardDescription";let d=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",a),...r})});d.displayName="CardContent";let u=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,n.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",a),...r})});u.displayName="CardFooter"},7063:(e,t,a)=>{"use strict";a.d(t,{GalleryPageContent:()=>y});var n=a(5155),r=a(2115),l=a(6874),i=a.n(l),s=a(6766),c=a(2960),o=a(7213),d=a(6695),u=a(285),m=a(5731),g=a(1008);function y(){var e,t;let[a,l]=(0,r.useState)({}),{data:y,isLoading:f,isError:p}=(0,c.I)({queryKey:["albums"],queryFn:m.jE.getAllAlbums});return(0,r.useEffect)(()=>{(async()=>{if((null==y?void 0:y.albums)&&y.albums.length>0){let e={};for(let t of y.albums)try{let a=await m.jE.getAlbumBySlug(t.slug);if(a.images&&a.images.length>0){let n=a.images[0];e[t._id]="".concat(g.A.backendURL,"/uploads/gallery/").concat(t._id,"/").concat(n.filename)}}catch(e){}l(e)}})()},[null==y?void 0:y.albums]),(0,n.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,n.jsxs)("div",{className:"mb-8",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Gallery"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Browse our collection of images and photos"})]}),f?(0,n.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((e,t)=>(0,n.jsxs)(d.Zp,{className:"animate-pulse overflow-hidden",children:[(0,n.jsx)("div",{className:"aspect-square bg-gray-200"}),(0,n.jsxs)(d.Wu,{className:"p-4",children:[(0,n.jsx)("div",{className:"h-5 bg-gray-200 rounded w-3/4 mb-2"}),(0,n.jsx)("div",{className:"h-4 bg-gray-100 rounded w-full"})]})]},t))}):p?(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("p",{className:"text-xl text-gray-600",children:"Failed to load albums."}),(0,n.jsx)(u.$,{onClick:()=>window.location.reload(),className:"mt-4",children:"Try Again"})]}):(null==y||null==(e=y.albums)?void 0:e.length)===0?(0,n.jsx)("div",{className:"text-center py-12",children:(0,n.jsx)("p",{className:"text-xl text-gray-600",children:"No albums found."})}):(0,n.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:null==y||null==(t=y.albums)?void 0:t.map(e=>(0,n.jsxs)(d.Zp,{className:"overflow-hidden group hover:shadow-md transition-shadow",children:[(0,n.jsxs)("div",{className:"aspect-square bg-gray-100 flex items-center justify-center overflow-hidden relative",children:[a[e._id]?(0,n.jsx)(s.default,{src:a[e._id],alt:e.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300",sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",onError:e=>{e.currentTarget.style.display="none";let t=e.currentTarget.parentElement;if(t){let e=t.querySelector(".fallback-icon");e&&(e.style.display="block")}}}):null,(0,n.jsx)(o.A,{className:"fallback-icon h-12 w-12 text-gray-400",style:{display:a[e._id]?"none":"block"}})]}),(0,n.jsxs)(d.Wu,{className:"p-4",children:[(0,n.jsx)("h3",{className:"font-semibold text-lg mb-2 truncate",children:e.title}),(0,n.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2",children:e.description||"No description available"})]}),(0,n.jsxs)(d.wL,{className:"p-4 pt-0 flex flex-col gap-2 w-full mt-auto",children:[(0,n.jsxs)("p",{className:"text-sm text-gray-500 w-full",children:[e.imageCount||0," ",1===e.imageCount?"image":"images"]}),(0,n.jsx)(u.$,{asChild:!0,className:"w-full",children:(0,n.jsx)(i(),{href:"/gallery/".concat(e.slug),children:"View Album"})})]})]},e._id))})]})}},7213:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>l,oH:()=>i,vV:()=>s});var n=a(2596),r=a(9688);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,n.$)(t))}function i(e){return e.replace(/\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g,(e,t)=>{let a=t.split(",").map(e=>e.trim()).filter(Boolean);if(0===a.length)return e;let n=a.map(e=>'<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">'.concat(e,"</span>")).join("");return'<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">'.concat(n,"</div></div>")})}let s=(e,t)=>{}},9708:(e,t,a)=>{"use strict";a.d(t,{DX:()=>s,TL:()=>i});var n=a(2115),r=a(6101),l=a(5155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:a,...l}=e;if(n.isValidElement(a)){var i;let e,s,c=(i=a,(s=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(s=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),o=function(e,t){let a={...t};for(let n in t){let r=e[n],l=t[n];/^on[A-Z]/.test(n)?r&&l?a[n]=(...e)=>{let t=l(...e);return r(...e),t}:r&&(a[n]=r):"style"===n?a[n]={...r,...l}:"className"===n&&(a[n]=[r,l].filter(Boolean).join(" "))}return{...e,...a}}(l,a.props);return a.type!==n.Fragment&&(o.ref=t?(0,r.t)(t,c):c),n.cloneElement(a,o)}return n.Children.count(a)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),a=n.forwardRef((e,a)=>{let{children:r,...i}=e,s=n.Children.toArray(r),c=s.find(o);if(c){let e=c.props.children,r=s.map(t=>t!==c?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,l.jsx)(t,{...i,ref:a,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,l.jsx)(t,{...i,ref:a,children:r})});return a.displayName=`${e}.Slot`,a}var s=i("Slot"),c=Symbol("radix.slottable");function o(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}}},e=>{var t=t=>e(e.s=t);e.O(0,[598,967,951,874,766,441,684,358],()=>t(5832)),_N_E=e.O()}]);