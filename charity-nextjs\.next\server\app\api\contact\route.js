(()=>{var e={};e.id=746,e.ids=[746],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71170:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>u});var a=r(96559),o=r(48088),n=r(37719),i=r(32190);let c=process.env.BACKEND_URL||"http://localhost:5000";async function p(e){try{let t=new URL(e.url).searchParams,r=`${c}/api/contact?${t.toString()}`,s=await fetch(r,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error(`Backend responded with status: ${s.status}`);let a=await s.json();return i.NextResponse.json(a)}catch(e){return i.NextResponse.json({error:"Failed to fetch contact messages"},{status:500})}}async function u(e){try{let t=await e.json(),r=`${c}/api/contact`,s=await fetch(r,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok){let e=await s.json().catch(()=>({}));return i.NextResponse.json({error:e.message||"Failed to submit contact form"},{status:s.status})}let a=await s.json();return i.NextResponse.json(a)}catch(e){return i.NextResponse.json({error:"Failed to submit contact form"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\contact\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:h,serverHooks:x}=d;function j(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:h})}},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(71170));module.exports=s})();