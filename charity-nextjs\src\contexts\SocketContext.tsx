'use client';

import React, { createContext, useState, useEffect, useRef, useContext } from 'react';
import { io, Socket } from 'socket.io-client';
import { toast } from 'sonner';
import API_CONFIG from '../config/api';
import { logError, logInfo } from '@/lib/utils';

// Socket instance - only create on client side
let socket: Socket | null = null;

// Initialize socket only on client side
const initializeSocket = () => {
  if (typeof window === 'undefined') return null;
  
  if (!socket) {
    socket = io(API_CONFIG.backendURL, {
      autoConnect: false, // Don't auto connect, we'll handle this manually
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5,
      timeout: 10000,
      withCredentials: true,
    });
  }
  
  return socket;
};

// Create a context with the socket and online count
interface SocketContextValue {
  socket: Socket | null;
  onlineCount: number;
  isConnected: boolean;
}

export const SocketContext = createContext<SocketContextValue>({
  socket: null,
  onlineCount: 0,
  isConnected: false
});

// Provider component
export const SocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [onlineCount, setOnlineCount] = useState<number>(0);
  const [isConnected, setIsConnected] = useState<boolean>(false);
  const errorCountRef = useRef(0);
  const hasShownToastRef = useRef(false);
  const connectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [socketInstance, setSocketInstance] = useState<Socket | null>(null);

  // Initialize socket on client side only
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const sock = initializeSocket();
    if (!sock) return;

    setSocketInstance(sock);

    // Try to connect with a small delay
    connectTimeoutRef.current = setTimeout(() => {
      try {
        sock.connect();
      } catch (error) {
        logError('Failed to initialize socket connection', error);
      }
    }, 1000);

    sock.on('connect', () => {
      logInfo('Socket connected successfully');
      setIsConnected(true);
      // Reset error count on successful connection
      errorCountRef.current = 0;
      hasShownToastRef.current = false;
    });

    sock.on('connect_error', (error) => {
      logError('Socket connection error', error);
      setIsConnected(false);

      // Increment error count
      errorCountRef.current += 1;

      // Only show toast after multiple connection failures and if not shown yet
      if (errorCountRef.current > 3 && !hasShownToastRef.current) {
        toast.info("Connection Notice", {
          description: "Real-time updates are not available. The application will continue to work normally.",
        });
        hasShownToastRef.current = true;
      }
    });

    sock.on('disconnect', (reason) => {
      logInfo('Socket disconnected', { reason });
      setIsConnected(false);
    });

    sock.on('onlineCount', (data: { count: number }) => {
      setOnlineCount(data.count);
    });

    return () => {
      if (connectTimeoutRef.current) {
        clearTimeout(connectTimeoutRef.current);
      }
      if (sock) {
        sock.off('connect');
        sock.off('connect_error');
        sock.off('disconnect');
        sock.off('onlineCount');
        sock.disconnect();
      }
    };
  }, []);

  return (
    <SocketContext.Provider value={{ socket: socketInstance, onlineCount, isConnected }}>
      {children}
    </SocketContext.Provider>
  );
};

// Hook for convenient socket access
export const useSocket = () => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
