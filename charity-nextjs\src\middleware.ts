import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define protected routes and their required roles
const protectedRoutes = {
  '/admin': null, // Any authenticated user
  '/admin/news': null,
  '/admin/gallery': null,
  '/admin/faq': null,
  '/admin/team': null,
  '/admin/locations': 'super-admin',
  '/admin/partners': 'super-admin',
  '/admin/users': 'super-admin',
  '/admin/contact': 'super-admin',
  '/admin/subscribers': 'super-admin',
  '/admin/settings': 'super-admin',
};

// Helper function to verify JWT token
async function verifyToken(token: string): Promise<{ valid: boolean; user?: { id: string; role: string; username: string } }> {
  try {
    // In a real implementation, you would verify the JWT token here
    // For now, we'll make a request to the backend to verify the token
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/auth/verify`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (response.ok) {
      const data = await response.json();
      return { valid: true, user: data.user };
    }

    return { valid: false };
  } catch (error) {
    console.error('Token verification error:', error);
    return { valid: false };
  }
}

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Check if the current path is a protected route
  const isProtectedRoute = Object.keys(protectedRoutes).some(route => 
    pathname.startsWith(route)
  );

  if (!isProtectedRoute) {
    return NextResponse.next();
  }

  // Get the auth token from cookies or headers
  const authToken = request.cookies.get('authToken')?.value || 
                   request.headers.get('authorization')?.replace('Bearer ', '');

  if (!authToken) {
    // Redirect to login with the current path as redirect parameter
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Verify the token
  const { valid, user } = await verifyToken(authToken);

  if (!valid) {
    // Token is invalid, redirect to login
    const loginUrl = new URL('/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Check role-based access
  const requiredRole = Object.entries(protectedRoutes).find(([route]) => 
    pathname.startsWith(route)
  )?.[1];

  if (requiredRole && user?.role !== requiredRole && user?.role !== 'super-admin') {
    // User doesn't have the required role, redirect to admin dashboard
    return NextResponse.redirect(new URL('/admin', request.url));
  }

  // Add user info to headers for use in the application
  const response = NextResponse.next();
  response.headers.set('x-user-id', user?.id || '');
  response.headers.set('x-user-role', user?.role || '');
  response.headers.set('x-user-username', user?.username || '');

  return response;
}

// Configure which paths the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
