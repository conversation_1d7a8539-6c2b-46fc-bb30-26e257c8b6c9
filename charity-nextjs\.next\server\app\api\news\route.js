(()=>{var e={};e.id=635,e.ids=[635],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35959:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>u});var n=r(96559),a=r(48088),o=r(37719),i=r(32190);let p=process.env.BACKEND_URL||"http://localhost:5000";async function u(e){try{let t=new URL(e.url).searchParams,r=`${p}/api/news?${t.toString()}`,s=await fetch(r,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error(`Backend responded with status: ${s.status}`);let n=await s.json();return i.NextResponse.json(n)}catch(e){return i.NextResponse.json({error:"Failed to fetch news"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/news/route",pathname:"/api/news",filename:"route",bundlePath:"app/api/news/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\news\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(35959));module.exports=s})();