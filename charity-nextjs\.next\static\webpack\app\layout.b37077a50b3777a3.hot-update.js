"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4a3a6746e59d\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaGFzaWJcXE9uZURyaXZlXFxEZXNrdG9wXFxjaGFyaXR5X2luZm9cXGNoYXJpdHlfaW5mb1xcY2hhcml0eS1uZXh0anNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0YTNhNjc0NmU1OWRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/providers/QueryProvider.tsx":
/*!****************************************************!*\
  !*** ./src/components/providers/QueryProvider.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(app-pages-browser)/./node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n\n\n\nfunction makeQueryClient() {\n    return new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClient({\n        defaultOptions: {\n            queries: {\n                // With SSR, we usually want to set some default staleTime\n                // above 0 to avoid refetching immediately on the client\n                staleTime: 60 * 1000,\n                gcTime: 10 * 60 * 1000,\n                retry: (failureCount, error)=>{\n                    // Don't retry on 4xx errors except 408, 429\n                    if ((error === null || error === void 0 ? void 0 : error.status) >= 400 && (error === null || error === void 0 ? void 0 : error.status) < 500 && ![\n                        408,\n                        429\n                    ].includes(error === null || error === void 0 ? void 0 : error.status)) {\n                        return false;\n                    }\n                    // Retry up to 3 times for other errors\n                    return failureCount < 3;\n                },\n                retryDelay: (attemptIndex)=>Math.min(1000 * 2 ** attemptIndex, 30000),\n                refetchOnWindowFocus: false,\n                refetchOnReconnect: 'always'\n            },\n            mutations: {\n                retry: false,\n                onError: (error)=>{\n                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.logError)('Mutation error:', error);\n                }\n            }\n        }\n    });\n}\nlet browserQueryClient = undefined;\nfunction getQueryClient() {\n    if (false) {} else {\n        // Browser: make a new query client if we don't already have one\n        // This is very important, so we don't re-make a new client if React\n        // suspends during the initial render. This may not be needed if we\n        // have a suspense boundary BELOW the creation of the query client\n        if (!browserQueryClient) browserQueryClient = makeQueryClient();\n        return browserQueryClient;\n    }\n}\nfunction QueryProvider(param) {\n    let { children } = param;\n    // NOTE: Avoid useState when initializing the query client if you don't\n    //       have a suspense boundary between this and the code that may\n    //       suspend because React will throw away the client on the initial\n    //       render if it suspends and there is no boundary\n    const queryClient = getQueryClient();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_3__.QueryClientProvider, {\n        client: queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_4__.ReactQueryDevtools, {\n                initialIsOpen: false,\n                position: \"bottom-right\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\providers\\\\QueryProvider.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\charity_info\\\\charity_info\\\\charity-nextjs\\\\src\\\\components\\\\providers\\\\QueryProvider.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n_c = QueryProvider;\nvar _c;\n$RefreshReg$(_c, \"QueryProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/providers/QueryProvider.tsx\n"));

/***/ })

});