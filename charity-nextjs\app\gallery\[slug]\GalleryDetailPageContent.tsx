'use client';

import React, { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import { ArrowLeft, X, ChevronLeft, ChevronRight, ZoomIn } from "lucide-react";
import { Button } from "@/components/ui/button";
import { galleryApi } from "@/lib/api";
import { cn } from "@/lib/utils";
import API_CONFIG from "@/config/api";

interface GalleryDetailPageContentProps {
  slug: string;
}

export function GalleryDetailPageContent({ slug }: GalleryDetailPageContentProps) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedIndex, setSelectedIndex] = useState<number>(-1);
  const [imagesLoaded, setImagesLoaded] = useState<Record<string, boolean>>({});

  const { data, isLoading, isError } = useQuery({
    queryKey: ["albumDetail", slug],
    queryFn: async () => {
      if (!slug) return Promise.reject("No slug provided");
      const result = await galleryApi.getAlbumBySlug(slug);
      console.log('Album detail data:', result);
      return result;
    },
    enabled: !!slug,
  });

  // Handle image loading
  const handleImageLoad = useCallback((imageId: string) => {
    setImagesLoaded(prev => ({
      ...prev,
      [imageId]: true
    }));
  }, []);

  // Lightbox functions
  const openLightbox = useCallback((imageUrl: string, index: number) => {
    setSelectedImage(imageUrl);
    setSelectedIndex(index);
  }, []);

  const closeLightbox = useCallback(() => {
    setSelectedImage(null);
    setSelectedIndex(-1);
  }, []);

  const navigateLightbox = useCallback((direction: "prev" | "next") => {
    if (!data?.images) return;
    
    const totalImages = data.images.length;
    let newIndex = selectedIndex;
    
    if (direction === "prev") {
      newIndex = selectedIndex > 0 ? selectedIndex - 1 : totalImages - 1;
    } else {
      newIndex = selectedIndex < totalImages - 1 ? selectedIndex + 1 : 0;
    }
    
    setSelectedIndex(newIndex);
    const newImageUrl = `${API_CONFIG.backendURL}/uploads/gallery/${data.album._id}/${data.images[newIndex].filename}`;
    setSelectedImage(newImageUrl);
  }, [selectedIndex, data]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!selectedImage) return;
      
      if (e.key === "Escape") {
        closeLightbox();
      } else if (e.key === "ArrowLeft") {
        navigateLightbox("prev");
      } else if (e.key === "ArrowRight") {
        navigateLightbox("next");
      }
    };

    window.addEventListener("keydown", handleKeyPress);
    return () => window.removeEventListener("keydown", handleKeyPress);
  }, [selectedImage, closeLightbox, navigateLightbox]);

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-32 mb-4"></div>
          <div className="h-10 bg-gray-200 rounded w-64 mb-3"></div>
          <div className="h-6 bg-gray-100 rounded w-96 mb-8"></div>
          <div className="masonry-grid">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="masonry-item">
                <div className="bg-gray-200 rounded-lg" style={{ height: `${200 + Math.random() * 200}px` }}></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (isError || !data) {
    return (
      <div className="container mx-auto px-4 py-12 text-center">
        <h1 className="text-2xl font-bold text-gray-800 mb-4">Album Not Found</h1>
        <p className="text-gray-600 mb-6">The album you&apos;re looking for does not exist or has been removed.</p>
        <Button asChild>
          <Link href="/gallery">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Gallery
          </Link>
        </Button>
      </div>
    );
  }

  const { album, images } = data;

  return (
    <div className="container mx-auto px-4 py-8">
      <Button asChild variant="ghost" className="mb-4">
        <Link href="/gallery">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Gallery
        </Link>
      </Button>

      <h1 className="text-3xl font-bold text-gray-800 mb-3">{album.title}</h1>

      {album.description && (
        <p className="text-gray-600 mb-8 max-w-3xl">{album.description}</p>
      )}

      {images.length === 0 ? (
        <div className="bg-gray-50 rounded-lg p-12 text-center">
          <p className="text-gray-600 text-lg">This album has no images yet.</p>
        </div>
      ) : (
        <div className="masonry-grid">
          {images.map((image: { _id: string; filename: string; caption?: string }, index: number) => {
            const imageUrl = `${API_CONFIG.backendURL}/uploads/gallery/${album._id}/${image.filename}`;
            const isLoaded = imagesLoaded[image._id];

            return (
              <div
                key={image._id}
                className={cn(
                  "masonry-item group relative overflow-hidden cursor-pointer",
                  isLoaded && "loaded"
                )}
                // No need for custom style with column-based layout
              >
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity z-10 flex items-center justify-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-white border border-white/30 hover:bg-white/20"
                    onClick={(e) => {
                      e.stopPropagation();
                      openLightbox(imageUrl, index);
                    }}
                  >
                    <ZoomIn className="h-5 w-5" />
                  </Button>
                </div>
                <Image
                  src={imageUrl}
                  alt={`Gallery image ${image._id}`}
                  width={400}
                  height={300}
                  className="w-full h-auto object-cover transition-transform duration-500 group-hover:scale-110"
                  sizes="(max-width: 480px) 100vw, (max-width: 768px) 50vw, 33vw"
                  onLoad={() => {
                    console.log(`Image loaded successfully: ${image.filename}`);
                    handleImageLoad(image._id);
                  }}
                  onError={(e) => {
                    console.error(`Failed to load image: ${image.filename}`);
                    console.error(`Image URL: ${imageUrl}`);
                    e.currentTarget.src = '/placeholder.svg';
                    setImagesLoaded(prev => ({
                      ...prev,
                      [image._id]: true
                    }));
                  }}
                  onClick={() => openLightbox(imageUrl, index)}
                />
              </div>
            );
          })}
        </div>
      )}

      {/* Lightbox */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black/98 z-50 flex items-center justify-center p-4 backdrop-blur-sm"
          onClick={closeLightbox}
        >
          <div className="absolute top-4 right-4 flex space-x-2">
            <Button
              variant="ghost"
              size="icon"
              className="text-white hover:bg-white/10 rounded-full"
              onClick={closeLightbox}
            >
              <X className="h-6 w-6" />
            </Button>
          </div>

          {/* Navigation buttons */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-4 md:left-8 text-white hover:bg-white/10 h-12 w-12 rounded-full opacity-70 hover:opacity-100"
            onClick={(e) => {
              e.stopPropagation();
              navigateLightbox("prev");
            }}
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>

          <Button
            variant="ghost"
            size="icon"
            className="absolute right-4 md:right-8 text-white hover:bg-white/10 h-12 w-12 rounded-full opacity-70 hover:opacity-100"
            onClick={(e) => {
              e.stopPropagation();
              navigateLightbox("next");
            }}
          >
            <ChevronRight className="h-6 w-6" />
          </Button>

          <div className="relative max-h-[90vh] max-w-[90vw] animate-fadeIn">
            <img
              src={selectedImage}
              alt="Enlarged gallery image"
              className="max-h-[90vh] max-w-[90vw] object-contain rounded-lg shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            />
            <div className="absolute bottom-0 left-0 right-0 text-center text-white text-sm py-3 bg-gradient-to-t from-black/70 to-transparent rounded-b-lg">
              {selectedIndex + 1} / {images.length}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
