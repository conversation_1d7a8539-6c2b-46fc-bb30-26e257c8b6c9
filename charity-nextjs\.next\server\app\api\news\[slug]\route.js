(()=>{var e={};e.id=689,e.ids=[689],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23304:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p});var n=r(96559),a=r(48088),o=r(37719),i=r(32190);let u=process.env.BACKEND_URL||"http://localhost:5000";async function p(e,{params:t}){try{let{slug:r}=await t,s=new URL(e.url).searchParams,n=`${u}/api/news/${r}?${s.toString()}`,a=await fetch(n,{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok){if(404===a.status)return i.NextResponse.json({error:"News article not found"},{status:404});throw Error(`Backend responded with status: ${a.status}`)}let o=await a.json();return i.NextResponse.json(o)}catch(e){return i.NextResponse.json({error:"Failed to fetch news article"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/news/[slug]/route",pathname:"/api/news/[slug]",filename:"route",bundlePath:"app/api/news/[slug]/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\news\\[slug]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=c;function x(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(23304));module.exports=s})();