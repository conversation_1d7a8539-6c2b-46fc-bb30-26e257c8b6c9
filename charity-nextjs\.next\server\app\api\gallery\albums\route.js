(()=>{var e={};e.id=911,e.ids=[911],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},62153:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>p});var a=t(96559),o=t(48088),n=t(37719),i=t(32190);let u=process.env.BACKEND_URL||"http://localhost:5000";async function p(e){try{let r=new URL(e.url).searchParams,t=`${u}/api/gallery/albums?${r.toString()}`,s=await fetch(t,{method:"GET",headers:{"Content-Type":"application/json"}});if(!s.ok)throw Error(`Backend responded with status: ${s.status}`);let a=await s.json();return i.NextResponse.json(a)}catch(e){return i.NextResponse.json({error:"Failed to fetch albums"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/gallery/albums/route",pathname:"/api/gallery/albums",filename:"route",bundlePath:"app/api/gallery/albums/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\gallery\\albums\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:h}=l;function x(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(62153));module.exports=s})();