(()=>{var e={};e.id=605,e.ids=[605],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9255:(e,t,a)=>{Promise.resolve().then(a.bind(a,45776))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},14952:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19139:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=a(65239),r=a(48088),i=a(88170),n=a.n(i),l=a(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let c={children:["",{children:["gallery",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,73882)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\[slug]\\page.tsx"]}]},{}]},{loading:[()=>Promise.resolve().then(a.bind(a,92441)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,68178)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(a.bind(a,99766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\[slug]\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/gallery/[slug]/page",pathname:"/gallery/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45351:(e,t,a)=>{"use strict";a.d(t,{GalleryDetailPageContent:()=>f});var s=a(60687),r=a(43210),i=a(85814),n=a.n(i),l=a(30474),o=a(51423),c=a(62688);let d=(0,c.A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),m=(0,c.A)("zoom-in",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);var u=a(11860),p=a(47033),h=a(14952),y=a(29523),g=a(62185),x=a(4780),w=a(70216);function f({slug:e}){let[t,a]=(0,r.useState)(null),[i,c]=(0,r.useState)(-1),[f,v]=(0,r.useState)({}),[b,j]=(0,r.useState)({}),{data:N,isLoading:C,isError:A}=(0,o.I)({queryKey:["albumDetail",e],queryFn:async()=>e?await g.jE.getAlbumBySlug(e):Promise.reject("No slug provided"),enabled:!!e}),k=(0,r.useCallback)((e,t)=>{let{naturalHeight:a,naturalWidth:s}=t.currentTarget;j(t=>({...t,[e]:{height:a,width:s}})),v(t=>({...t,[e]:!0}))},[]),$=(0,r.useCallback)((e,t)=>{a(e),c(t)},[]),_=(0,r.useCallback)(()=>{a(null),c(-1)},[]),q=(0,r.useCallback)(e=>{if(!N?.images)return;let t=N.images.length,s=i;c(s="prev"===e?i>0?i-1:t-1:i<t-1?i+1:0),a(`${w.A.backendURL}/uploads/gallery/${N.album._id}/${N.images[s].filename}`)},[i,N]);if(C)return(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"animate-pulse",children:[(0,s.jsx)("div",{className:"h-8 bg-gray-200 rounded w-32 mb-4"}),(0,s.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-3"}),(0,s.jsx)("div",{className:"h-6 bg-gray-100 rounded w-96 mb-8"}),(0,s.jsx)("div",{className:"masonry-grid",children:[...Array(6)].map((e,t)=>(0,s.jsx)("div",{className:"masonry-item",children:(0,s.jsx)("div",{className:"bg-gray-200 rounded-lg",style:{height:`${200+200*Math.random()}px`}})},t))})]})});if(A||!N)return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-12 text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Album Not Found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"The album you're looking for does not exist or has been removed."}),(0,s.jsx)(y.$,{asChild:!0,children:(0,s.jsxs)(n(),{href:"/gallery",children:[(0,s.jsx)(d,{className:"mr-2 h-4 w-4"})," Back to Gallery"]})})]});let{album:P,images:D}=N;return void 0!==P.imageCount?P.imageCount:D.length,(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsx)(y.$,{asChild:!0,variant:"ghost",className:"mb-4",children:(0,s.jsxs)(n(),{href:"/gallery",children:[(0,s.jsx)(d,{className:"mr-2 h-4 w-4"})," Back to Gallery"]})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-800 mb-3",children:P.title}),P.description&&(0,s.jsx)("p",{className:"text-gray-600 mb-8 max-w-3xl",children:P.description}),0===D.length?(0,s.jsx)("div",{className:"bg-gray-50 rounded-lg p-12 text-center",children:(0,s.jsx)("p",{className:"text-gray-600 text-lg",children:"This album has no images yet."})}):(0,s.jsx)("div",{className:"masonry-grid",children:D.map((e,t)=>{let a=`${w.A.backendURL}/uploads/gallery/${P._id}/${e.filename}`,r=f[e._id];return(0,s.jsxs)("div",{className:(0,x.cn)("masonry-item group relative overflow-hidden cursor-pointer",r&&"loaded"),children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity z-10 flex items-center justify-center",children:(0,s.jsx)(y.$,{variant:"ghost",size:"icon",className:"text-white border border-white/30 hover:bg-white/20",onClick:e=>{e.stopPropagation(),$(a,t)},children:(0,s.jsx)(m,{className:"h-5 w-5"})})}),(0,s.jsx)(l.default,{src:a,alt:`Gallery image ${e._id}`,width:400,height:300,className:"w-full h-auto object-cover transition-transform duration-500 group-hover:scale-110",sizes:"(max-width: 480px) 100vw, (max-width: 768px) 50vw, 33vw",onLoad:t=>{k(e._id,t)},onError:t=>{t.currentTarget.src="/placeholder.svg",j(t=>({...t,[e._id]:{height:200,width:200}})),v(t=>({...t,[e._id]:!0}))},onClick:()=>$(a,t)})]},e._id)})}),t&&(0,s.jsxs)("div",{className:"fixed inset-0 bg-black/98 z-50 flex items-center justify-center p-4 backdrop-blur-sm",onClick:_,children:[(0,s.jsx)("div",{className:"absolute top-4 right-4 flex space-x-2",children:(0,s.jsx)(y.$,{variant:"ghost",size:"icon",className:"text-white hover:bg-white/10 rounded-full",onClick:_,children:(0,s.jsx)(u.A,{className:"h-6 w-6"})})}),(0,s.jsx)(y.$,{variant:"ghost",size:"icon",className:"absolute left-4 md:left-8 text-white hover:bg-white/10 h-12 w-12 rounded-full opacity-70 hover:opacity-100",onClick:e=>{e.stopPropagation(),q("prev")},children:(0,s.jsx)(p.A,{className:"h-6 w-6"})}),(0,s.jsx)(y.$,{variant:"ghost",size:"icon",className:"absolute right-4 md:right-8 text-white hover:bg-white/10 h-12 w-12 rounded-full opacity-70 hover:opacity-100",onClick:e=>{e.stopPropagation(),q("next")},children:(0,s.jsx)(h.A,{className:"h-6 w-6"})}),(0,s.jsxs)("div",{className:"relative max-h-[90vh] max-w-[90vw] animate-fadeIn",children:[(0,s.jsx)("img",{src:t,alt:"Enlarged gallery image",className:"max-h-[90vh] max-w-[90vw] object-contain rounded-lg shadow-2xl",onClick:e=>e.stopPropagation()}),(0,s.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 text-center text-white text-sm py-3 bg-gradient-to-t from-black/70 to-transparent rounded-b-lg",children:[i+1," / ",D.length]})]})]})]})}},45776:(e,t,a)=>{"use strict";a.d(t,{GalleryDetailPageContent:()=>s});let s=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call GalleryDetailPageContent() from the server but GalleryDetailPageContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\[slug]\\GalleryDetailPageContent.tsx","GalleryDetailPageContent")},47033:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62185:(e,t,a)=>{"use strict";a.d(t,{AY:()=>m,EO:()=>n,TP:()=>o,YV:()=>l,jE:()=>u,l4:()=>d,lM:()=>c});var s=a(51060),r=a(70216);let i=s.A.create({baseURL:r.A.baseURL,timeout:r.A.timeout,withCredentials:r.A.withCredentials});i.interceptors.request.use(async e=>e,e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response?.status,Promise.reject(e)));let n={getContent:async()=>(await i.get("/about")).data,updateContent:async e=>(await i.post("/about",e)).data},l={getAll:async e=>({teamMembers:(await i.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await i.get(`/team/${e}`)).data}),create:async e=>(await i.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,t)=>(await i.put(`/team/${e}`,t,{headers:{"Content-Type":"multipart/form-data"}})).data,delete:async e=>(await i.delete(`/team/${e}`)).data},o={submit:async e=>(await i.post("/contact",e)).data,getAll:async(e=1,t=10,a)=>{let s={page:e,limit:t,...a?{status:a}:{}};return(await i.get("/contact",{params:s})).data},getById:async e=>(await i.get(`/contact/${e}`)).data,updateStatus:async(e,t)=>(await i.put(`/contact/${e}/status`,{status:t})).data,delete:async e=>(await i.delete(`/contact/${e}`)).data},c={getAll:async e=>({locations:(await i.get("/locations",{params:e})).data}),getById:async e=>({location:(await i.get(`/locations/${e}`)).data}),create:async e=>{let t={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await i.post("/locations",t)).data},update:async(e,t)=>{let a={...t,isMainOffice:void 0!==t.isMainOffice?String(t.isMainOffice):void 0,active:void 0!==t.active?String(t.active):void 0};return(await i.put(`/locations/${e}`,a)).data},delete:async e=>(await i.delete(`/locations/${e}`)).data},d={getAll:async()=>(await i.get("/faqs")).data,getAllAdmin:async()=>(await i.get("/admin/faqs")).data,getById:async e=>(await i.get(`/admin/faqs/${e}`)).data,create:async e=>(await i.post("/admin/faqs",e)).data,update:async(e,t,a=!1)=>{let s;s=a?{isActive:void 0===t.isActive||!!t.isActive}:{question:t.question?.trim(),answer:t.answer?.trim(),category:t.category?.trim()||"General",order:"number"==typeof t.order?t.order:0,isActive:void 0===t.isActive||!!t.isActive};try{return(await i.put(`/admin/faqs/${e}`,s)).data}catch(e){throw e}},delete:async e=>(await i.delete(`/admin/faqs/${e}`)).data},m={getAll:async(e=1,t=10)=>(await i.get(`/news?page=${e}&limit=${t}&includeAttachments=true`)).data,getBySlug:async e=>(await i.get(`/news/${e}?includeAttachments=true`)).data.news,getById:async e=>(await i.get(`/admin/news/${e}`)).data.news,create:async e=>(await i.post("/admin/news",e)).data.news,update:async(e,t)=>(await i.put(`/admin/news/${e}`,t)).data.news,delete:async e=>(await i.delete(`/admin/news/${e}`)).data},u={getAllAlbums:async()=>(await i.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await i.get(`/gallery/albums/${e}`)).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await i.get(`/admin/gallery/albums/${e}`)).data}catch(e){throw e}},createAlbum:async e=>(await i.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,t)=>(await i.put(`/admin/gallery/albums/${e}`,t)).data.album,deleteAlbum:async e=>{await i.delete(`/admin/gallery/albums/${e}`)},uploadImage:async(e,t)=>{let a=document.querySelector('meta[name="csrf-token"]')?.getAttribute("content");return a&&t.append("_csrf",a),(await i.post(`/admin/gallery/albums/${e}/images`,t,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await i.delete(`/admin/gallery/images/${e}`)}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70216:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},73882:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n,generateMetadata:()=>i});var s=a(37413),r=a(45776);async function i({params:e}){return e.slug,{title:"Gallery Album - Charity Welcome Hub",description:"View photos from our charity events and community activities.",keywords:["gallery","album","photos","charity events","community"],openGraph:{title:"Gallery Album - Charity Welcome Hub",description:"View photos from our charity events and community activities.",type:"website"},twitter:{card:"summary_large_image",title:"Gallery Album - Charity Welcome Hub",description:"View photos from our charity events and community activities."}}}function n({params:e}){return(0,s.jsx)(r.GalleryDetailPageContent,{slug:e.slug})}},74075:e=>{"use strict";e.exports=require("zlib")},74751:(e,t,a)=>{Promise.resolve().then(a.bind(a,45351))},78963:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>l,Zp:()=>n,wL:()=>o});var s=a(37413),r=a(61120),i=a(10974);let n=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card",r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t})).displayName="CardHeader",r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t})).displayName="CardTitle",r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let l=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...t}));l.displayName="CardContent";let o=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t}));o.displayName="CardFooter"},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},92441:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var s=a(37413),r=a(6972),i=a(78963);function n(){return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)(r.E,{className:"h-8 w-32 mb-2"}),(0,s.jsx)(r.E,{className:"h-4 w-64"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((e,t)=>(0,s.jsxs)(i.Zp,{className:"overflow-hidden",children:[(0,s.jsx)(r.E,{className:"aspect-square w-full"}),(0,s.jsxs)(i.Wu,{className:"p-4",children:[(0,s.jsx)(r.E,{className:"h-5 w-full mb-2"}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(r.E,{className:"h-4 w-full"}),(0,s.jsx)(r.E,{className:"h-4 w-2/3"})]})]}),(0,s.jsxs)(i.wL,{className:"p-4 pt-0 flex flex-col gap-2",children:[(0,s.jsx)(r.E,{className:"h-4 w-20"}),(0,s.jsx)(r.E,{className:"h-9 w-full"})]})]},t))})]})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[447,699,62,474,775],()=>a(19139));module.exports=s})();