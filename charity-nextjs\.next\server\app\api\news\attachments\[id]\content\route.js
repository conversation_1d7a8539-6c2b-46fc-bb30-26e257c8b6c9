(()=>{var e={};e.id=724,e.ids=[724],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65230:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var s={};r.r(s),r.d(s,{GET:()=>c});var n=r(96559),a=r(48088),o=r(37719),i=r(32190);let p=process.env.BACKEND_URL||"http://localhost:5000";async function c(e,{params:t}){try{let{id:e}=await t,r=`${p}/api/news/attachments/${e}/content`,s=await fetch(r,{method:"GET"});if(!s.ok){if(404===s.status)return i.NextResponse.json({error:"Attachment not found"},{status:404});throw Error(`Backend responded with status: ${s.status}`)}let n=s.headers.get("content-type")||"application/octet-stream",a=s.headers.get("content-length"),o=s.headers.get("content-disposition"),c={"Content-Type":n};a&&(c["Content-Length"]=a),o&&(c["Content-Disposition"]=o);let u=await s.arrayBuffer();return new i.NextResponse(u,{status:200,headers:c})}catch(e){return i.NextResponse.json({error:"Failed to fetch attachment"},{status:500})}}let u=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/news/attachments/[id]/content/route",pathname:"/api/news/attachments/[id]/content",filename:"route",bundlePath:"app/api/news/attachments/[id]/content/route"},resolvedPagePath:"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\api\\news\\attachments\\[id]\\content\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:l}=u;function x(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}},78335:()=>{},96487:()=>{}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(65230));module.exports=s})();