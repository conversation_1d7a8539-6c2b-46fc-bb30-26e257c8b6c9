(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[57],{238:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("gift",[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]])},306:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]])},432:(e,t,i)=>{"use strict";i.d(t,{E:()=>g});var r=i(2020),s=i(9853),a=i(7165),n=i(5910),u=class extends n.Q{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,i){let a=t.queryKey,n=t.queryHash??(0,r.F$)(a,t),u=this.get(n);return u||(u=new s.X({client:e,queryKey:a,queryHash:n,options:e.defaultQueryOptions(t),state:i,defaultOptions:e.getQueryDefaults(a)}),this.add(u)),u}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){a.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,r.MK)(e,t)):t}notify(e){a.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){a.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){a.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},o=i(7948),l=i(6784),h=class extends o.k{#t;#i;#r;constructor(e){super(),this.mutationId=e.mutationId,this.#i=e.mutationCache,this.#t=[],this.state=e.state||{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0},this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#t.includes(e)||(this.#t.push(e),this.clearGcTimeout(),this.#i.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#t=this.#t.filter(t=>t!==e),this.scheduleGc(),this.#i.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#t.length||("pending"===this.state.status?this.scheduleGc():this.#i.remove(this))}continue(){return this.#r?.continue()??this.execute(this.state.variables)}async execute(e){let t=()=>{this.#s({type:"continue"})};this.#r=(0,l.II)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#s({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#s({type:"pause"})},onContinue:t,retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#i.canRun(this)});let i="pending"===this.state.status,r=!this.#r.canStart();try{if(i)t();else{this.#s({type:"pending",variables:e,isPaused:r}),await this.#i.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#s({type:"pending",context:t,variables:e,isPaused:r})}let s=await this.#r.start();return await this.#i.config.onSuccess?.(s,e,this.state.context,this),await this.options.onSuccess?.(s,e,this.state.context),await this.#i.config.onSettled?.(s,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(s,null,e,this.state.context),this.#s({type:"success",data:s}),s}catch(t){try{throw await this.#i.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#i.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#s({type:"error",error:t})}}finally{this.#i.runNext(this)}}#s(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),a.jG.batch(()=>{this.#t.forEach(t=>{t.onMutationUpdate(e)}),this.#i.notify({mutation:this,type:"updated",action:e})})}},c=class extends n.Q{constructor(e={}){super(),this.config=e,this.#a=new Set,this.#n=new Map,this.#u=0}#a;#n;#u;build(e,t,i){let r=new h({mutationCache:this,mutationId:++this.#u,options:e.defaultMutationOptions(t),state:i});return this.add(r),r}add(e){this.#a.add(e);let t=d(e);if("string"==typeof t){let i=this.#n.get(t);i?i.push(e):this.#n.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#a.delete(e)){let t=d(e);if("string"==typeof t){let i=this.#n.get(t);if(i)if(i.length>1){let t=i.indexOf(e);-1!==t&&i.splice(t,1)}else i[0]===e&&this.#n.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=d(e);if("string"!=typeof t)return!0;{let i=this.#n.get(t),r=i?.find(e=>"pending"===e.state.status);return!r||r===e}}runNext(e){let t=d(e);if("string"!=typeof t)return Promise.resolve();{let i=this.#n.get(t)?.find(t=>t!==e&&t.state.isPaused);return i?.continue()??Promise.resolve()}}clear(){a.jG.batch(()=>{this.#a.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#a.clear(),this.#n.clear()})}getAll(){return Array.from(this.#a)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,r.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,r.nJ)(e,t))}notify(e){a.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return a.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(r.lQ))))}};function d(e){return e.options.scope?.id}var p=i(920),f=i(1239);function y(e){return{onFetch:(t,i)=>{let s=t.options,a=t.fetchOptions?.meta?.fetchMore?.direction,n=t.state.data?.pages||[],u=t.state.data?.pageParams||[],o={pages:[],pageParams:[]},l=0,h=async()=>{let i=!1,h=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?i=!0:t.signal.addEventListener("abort",()=>{i=!0}),t.signal)})},c=(0,r.ZM)(t.options,t.fetchOptions),d=async(e,s,a)=>{if(i)return Promise.reject();if(null==s&&e.pages.length)return Promise.resolve(e);let n=(()=>{let e={client:t.client,queryKey:t.queryKey,pageParam:s,direction:a?"backward":"forward",meta:t.options.meta};return h(e),e})(),u=await c(n),{maxPages:o}=t.options,l=a?r.ZZ:r.y9;return{pages:l(e.pages,u,o),pageParams:l(e.pageParams,s,o)}};if(a&&n.length){let e="backward"===a,t={pages:n,pageParams:u},i=(e?function(e,{pages:t,pageParams:i}){return t.length>0?e.getPreviousPageParam?.(t[0],t,i[0],i):void 0}:m)(s,t);o=await d(t,i,e)}else{let t=e??n.length;do{let e=0===l?u[0]??s.initialPageParam:m(s,o);if(l>0&&null==e)break;o=await d(o,e),l++}while(l<t)}return o};t.options.persister?t.fetchFn=()=>t.options.persister?.(h,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},i):t.fetchFn=h}}}function m(e,{pages:t,pageParams:i}){let r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,i[r],i):void 0}var g=class{#o;#i;#l;#h;#c;#d;#p;#f;constructor(e={}){this.#o=e.queryCache||new u,this.#i=e.mutationCache||new c,this.#l=e.defaultOptions||{},this.#h=new Map,this.#c=new Map,this.#d=0}mount(){this.#d++,1===this.#d&&(this.#p=p.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#o.onFocus())}),this.#f=f.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#o.onOnline())}))}unmount(){this.#d--,0===this.#d&&(this.#p?.(),this.#p=void 0,this.#f?.(),this.#f=void 0)}isFetching(e){return this.#o.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#i.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#o.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),i=this.#o.build(this,t),s=i.state.data;return void 0===s?this.fetchQuery(e):(e.revalidateIfStale&&i.isStaleByTime((0,r.d2)(t.staleTime,i))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return this.#o.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,i){let s=this.defaultQueryOptions({queryKey:e}),a=this.#o.get(s.queryHash),n=a?.state.data,u=(0,r.Zw)(t,n);if(void 0!==u)return this.#o.build(this,s).setData(u,{...i,manual:!0})}setQueriesData(e,t,i){return a.jG.batch(()=>this.#o.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,i)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#o.get(t.queryHash)?.state}removeQueries(e){let t=this.#o;a.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let i=this.#o;return a.jG.batch(()=>(i.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let i={revert:!0,...t};return Promise.all(a.jG.batch(()=>this.#o.findAll(e).map(e=>e.cancel(i)))).then(r.lQ).catch(r.lQ)}invalidateQueries(e,t={}){return a.jG.batch(()=>(this.#o.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let i={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(a.jG.batch(()=>this.#o.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,i);return i.throwOnError||(t=t.catch(r.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(r.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let i=this.#o.build(this,t);return i.isStaleByTime((0,r.d2)(t.staleTime,i))?i.fetch(t):Promise.resolve(i.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(r.lQ).catch(r.lQ)}fetchInfiniteQuery(e){return e.behavior=y(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(r.lQ).catch(r.lQ)}ensureInfiniteQueryData(e){return e.behavior=y(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return f.t.isOnline()?this.#i.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#o}getMutationCache(){return this.#i}getDefaultOptions(){return this.#l}setDefaultOptions(e){this.#l=e}setQueryDefaults(e,t){this.#h.set((0,r.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#h.values()],i={};return t.forEach(t=>{(0,r.Cp)(e,t.queryKey)&&Object.assign(i,t.defaultOptions)}),i}setMutationDefaults(e,t){this.#c.set((0,r.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#c.values()],i={};return t.forEach(t=>{(0,r.Cp)(e,t.mutationKey)&&Object.assign(i,t.defaultOptions)}),i}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#l.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,r.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===r.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#l.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#o.clear(),this.#i.clear()}}},1264:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1284:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1976:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2085:(e,t,i)=>{"use strict";i.d(t,{F:()=>n});var r=i(2596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,n=(e,t)=>i=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==i?void 0:i.class,null==i?void 0:i.className);let{variants:n,defaultVariants:u}=t,o=Object.keys(n).map(e=>{let t=null==i?void 0:i[e],r=null==u?void 0:u[e];if(null===t)return null;let a=s(t)||s(r);return n[e][a]}),l=i&&Object.entries(i).reduce((e,t)=>{let[i,r]=t;return void 0===r||(e[i]=r),e},{});return a(e,o,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:i,className:r,...s}=t;return Object.entries(s).every(e=>{let[t,i]=e;return Array.isArray(i)?i.includes({...u,...l}[t]):({...u,...l})[t]===i})?[...e,i,r]:e},[]),null==i?void 0:i.class,null==i?void 0:i.className)}},3206:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},4416:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4783:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5695:(e,t,i)=>{"use strict";var r=i(8999);i.o(r,"useRouter")&&i.d(t,{useRouter:function(){return r.useRouter}})},6101:(e,t,i)=>{"use strict";i.d(t,{s:()=>n,t:()=>a});var r=i(2115);function s(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let i=!1,r=e.map(e=>{let r=s(e,t);return i||"function"!=typeof r||(i=!0),r});if(i)return()=>{for(let t=0;t<r.length;t++){let i=r[t];"function"==typeof i?i():s(e[t],null)}}}}function n(...e){return r.useCallback(a(...e),e)}},7213:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},7924:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8304:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("rss",[["path",{d:"M4 11a9 9 0 0 1 9 9",key:"pv89mb"}],["path",{d:"M4 4a16 16 0 0 1 16 16",key:"k0647b"}],["circle",{cx:"5",cy:"19",r:"1",key:"bfqh0e"}]])},9074:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9420:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9444:(e,t,i)=>{"use strict";let r;i.d(t,{Ck:()=>K,IN:()=>P,fK:()=>N,rH:()=>R,zB:()=>q});let s=-1,a=e=>{addEventListener("pageshow",t=>{t.persisted&&(s=t.timeStamp,e(t))},!0)},n=(e,t,i,r)=>{let s,a;return n=>{t.value>=0&&(n||r)&&((a=t.value-(s??0))||void 0===s)&&(s=t.value,t.delta=a,t.rating=((e,t)=>e>t[1]?"poor":e>t[0]?"needs-improvement":"good")(t.value,i),e(t))}},u=e=>{requestAnimationFrame(()=>requestAnimationFrame(()=>e()))},o=()=>{let e=performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},l=()=>{let e=o();return e?.activationStart??0},h=(e,t=-1)=>{let i=o(),r="navigate";return s>=0?r="back-forward-cache":i&&(document.prerendering||l()>0?r="prerender":document.wasDiscarded?r="restore":i.type&&(r=i.type.replace(/_/g,"-"))),{name:e,value:t,rating:"good",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(0x82f79cd8fff*Math.random())+1e12}`,navigationType:r}},c=new WeakMap;function d(e,t){return c.get(e)||c.set(e,new t),c.get(e)}class p{t;i=0;o=[];h(e){if(e.hadRecentInput)return;let t=this.o[0],i=this.o.at(-1);this.i&&t&&i&&e.startTime-i.startTime<1e3&&e.startTime-t.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),this.t?.(e)}}let f=(e,t,i={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){let r=new PerformanceObserver(e=>{Promise.resolve().then(()=>{t(e.getEntries())})});return r.observe({type:e,buffered:!0,...i}),r}}catch{}},y=e=>{let t=!1;return()=>{t||(e(),t=!0)}},m=-1,g=()=>"hidden"!==document.visibilityState||document.prerendering?1/0:0,v=e=>{"hidden"===document.visibilityState&&m>-1&&(m="visibilitychange"===e.type?e.timeStamp:0,C())},b=()=>{addEventListener("visibilitychange",v,!0),addEventListener("prerenderingchange",v,!0)},C=()=>{removeEventListener("visibilitychange",v,!0),removeEventListener("prerenderingchange",v,!0)},A=()=>{if(m<0){let e=l();m=(document.prerendering?void 0:globalThis.performance.getEntriesByType("visibility-state").filter(t=>"hidden"===t.name&&t.startTime>e)[0]?.startTime)??g(),b(),a(()=>{setTimeout(()=>{m=g(),b()})})}return{get firstHiddenTime(){return m}}},k=e=>{document.prerendering?addEventListener("prerenderingchange",()=>e(),!0):e()},w=[1800,3e3],q=(e,t={})=>{k(()=>{let i=A(),r,s=h("FCP"),o=f("paint",e=>{for(let t of e)"first-contentful-paint"===t.name&&(o.disconnect(),t.startTime<i.firstHiddenTime&&(s.value=Math.max(t.startTime-l(),0),s.entries.push(t),r(!0)))});o&&(r=n(e,s,w,t.reportAllChanges),a(i=>{r=n(e,s=h("FCP"),w,t.reportAllChanges),u(()=>{s.value=performance.now()-i.timeStamp,r(!0)})}))})},M=[.1,.25],P=(e,t={})=>{q(y(()=>{let i,r=h("CLS",0),s=d(t,p),o=e=>{for(let t of e)s.h(t);s.i>r.value&&(r.value=s.i,r.entries=s.o,i())},l=f("layout-shift",o);l&&(i=n(e,r,M,t.reportAllChanges),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(o(l.takeRecords()),i(!0))}),a(()=>{s.i=0,i=n(e,r=h("CLS",0),M,t.reportAllChanges),u(()=>i())}),setTimeout(i))}))},O=0,T=1/0,x=0,E=e=>{for(let t of e)t.interactionId&&(T=Math.min(T,t.interactionId),O=(x=Math.max(x,t.interactionId))?(x-T)/7+1:0)},Q=()=>r?O:performance.interactionCount??0,j=()=>{"interactionCount"in performance||r||(r=f("event",E,{type:"event",buffered:!0,durationThreshold:0}))},S=0;class D{u=[];l=new Map;m;v;p(){S=Q(),this.u.length=0,this.l.clear()}P(){let e=Math.min(this.u.length-1,Math.floor((Q()-S)/50));return this.u[e]}h(e){if(this.m?.(e),!e.interactionId&&"first-input"!==e.entryType)return;let t=this.u.at(-1),i=this.l.get(e.interactionId);if(i||this.u.length<10||e.duration>t.T){if(i?e.duration>i.T?(i.entries=[e],i.T=e.duration):e.duration===i.T&&e.startTime===i.entries[0].startTime&&i.entries.push(e):(i={id:e.interactionId,entries:[e],T:e.duration},this.l.set(i.id,i),this.u.push(i)),this.u.sort((e,t)=>t.T-e.T),this.u.length>10)for(let e of this.u.splice(10))this.l.delete(e.id);this.v?.(i)}}}let F=e=>{let t=globalThis.requestIdleCallback||setTimeout;"hidden"===document.visibilityState?e():(e=y(e),document.addEventListener("visibilitychange",e,{once:!0}),t(()=>{e(),document.removeEventListener("visibilitychange",e)}))},I=[200,500],R=(e,t={})=>{globalThis.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype&&k(()=>{j();let i,r=h("INP"),s=d(t,D),u=e=>{F(()=>{for(let t of e)s.h(t);let t=s.P();t&&t.T!==r.value&&(r.value=t.T,r.entries=t.entries,i())})},o=f("event",u,{durationThreshold:t.durationThreshold??40});i=n(e,r,I,t.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),document.addEventListener("visibilitychange",()=>{"hidden"===document.visibilityState&&(u(o.takeRecords()),i(!0))}),a(()=>{s.p(),i=n(e,r=h("INP"),I,t.reportAllChanges)}))})};class L{m;h(e){this.m?.(e)}}let G=[2500,4e3],N=(e,t={})=>{k(()=>{let i=A(),r,s=h("LCP"),o=d(t,L),c=e=>{for(let a of(t.reportAllChanges||(e=e.slice(-1)),e))o.h(a),a.startTime<i.firstHiddenTime&&(s.value=Math.max(a.startTime-l(),0),s.entries=[a],r())},p=f("largest-contentful-paint",c);if(p){r=n(e,s,G,t.reportAllChanges);let i=y(()=>{c(p.takeRecords()),p.disconnect(),r(!0)});for(let e of["keydown","click","visibilitychange"])addEventListener(e,()=>F(i),{capture:!0,once:!0});a(i=>{r=n(e,s=h("LCP"),G,t.reportAllChanges),u(()=>{s.value=performance.now()-i.timeStamp,r(!0)})})}})},_=[800,1800],H=e=>{document.prerendering?k(()=>H(e)):"complete"!==document.readyState?addEventListener("load",()=>H(e),!0):setTimeout(e)},K=(e,t={})=>{let i=h("TTFB"),r=n(e,i,_,t.reportAllChanges);H(()=>{let s=o();s&&(i.value=Math.max(s.responseStart-l(),0),i.entries=[s],r(!0),a(()=>{(r=n(e,i=h("TTFB",0),_,t.reportAllChanges))(!0)}))})}},9708:(e,t,i)=>{"use strict";i.d(t,{DX:()=>u,TL:()=>n});var r=i(2115),s=i(6101),a=i(5155);function n(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:i,...a}=e;if(r.isValidElement(i)){var n;let e,u,o=(n=i,(u=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(u=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),l=function(e,t){let i={...t};for(let r in t){let s=e[r],a=t[r];/^on[A-Z]/.test(r)?s&&a?i[r]=(...e)=>{let t=a(...e);return s(...e),t}:s&&(i[r]=s):"style"===r?i[r]={...s,...a}:"className"===r&&(i[r]=[s,a].filter(Boolean).join(" "))}return{...e,...i}}(a,i.props);return i.type!==r.Fragment&&(l.ref=t?(0,s.t)(t,o):o),r.cloneElement(i,l)}return r.Children.count(i)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),i=r.forwardRef((e,i)=>{let{children:s,...n}=e,u=r.Children.toArray(s),o=u.find(l);if(o){let e=o.props.children,s=u.map(t=>t!==o?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...n,ref:i,children:r.isValidElement(e)?r.cloneElement(e,void 0,s):null})}return(0,a.jsx)(t,{...n,ref:i,children:s})});return i.displayName=`${e}.Slot`,i}var u=n("Slot"),o=Symbol("radix.slottable");function l(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},9947:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(9946).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])}}]);