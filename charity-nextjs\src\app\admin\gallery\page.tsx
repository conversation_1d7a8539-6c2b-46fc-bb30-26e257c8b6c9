"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Plus, Edit, Trash, Loader, AlertTriangle, Image, Calendar } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { galleryApi, AlbumItem } from "@/lib/api";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { format } from "date-fns";

// Using AlbumItem from API types

export default function AdminGalleryListPage() {
  const queryClient = useQueryClient();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [albumToDelete, setAlbumToDelete] = useState<string | null>(null);

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["admin-albums"],
    queryFn: galleryApi.getAllAlbums,
  });

  const albums = data?.albums || [];

  const deleteMutation = useMutation({
    mutationFn: (id: string) => galleryApi.deleteAlbum(id),
    onSuccess: () => {
      toast.success("Album deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["admin-albums"] });
      setDeleteDialogOpen(false);
      setAlbumToDelete(null);
    },
    onError: () => {
      toast.error("Failed to delete album");
    },
  });

  const handleDeleteClick = (albumId: string) => {
    setAlbumToDelete(albumId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (albumToDelete) {
      deleteMutation.mutate(albumToDelete);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <Loader className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  if (isError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-red-600 mb-4">Failed to load gallery albums</p>
              <Button onClick={() => refetch()}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Gallery Management</h1>
          <p className="text-gray-600 mt-1">Manage photo albums and galleries</p>
        </div>
        <Button asChild>
          <Link href="/admin/gallery/create">
            <Plus className="h-4 w-4 mr-2" />
            Create Album
          </Link>
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Albums</CardTitle>
            <Image className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{albums.length}</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Images</CardTitle>
            <Image className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {albums.reduce((total: number, album: AlbumItem) => total + (album.imageCount || 0), 0)}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Albums</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {albums.filter((album: AlbumItem) => {
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                return album.createdAt && new Date(album.createdAt) > weekAgo;
              }).length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Albums Table */}
      <Card>
        <CardContent className="pt-6">
          {albums.length === 0 ? (
            <div className="text-center py-12">
              <Image className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-700 mb-2">No albums yet</h3>
              <p className="text-gray-500 mb-6">
                Create your first photo album to get started.
              </p>
              <Button asChild>
                <Link href="/admin/gallery/create">
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Album
                </Link>
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Images</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Updated</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {albums.map((album: AlbumItem) => (
                  <TableRow key={album._id}>
                    <TableCell className="font-medium">
                      <div>
                        <div className="font-semibold">{album.title}</div>
                        <div className="text-sm text-gray-500">/{album.slug}</div>
                      </div>
                    </TableCell>
                    <TableCell className="max-w-xs">
                      <div className="truncate">
                        {album.description || <span className="text-gray-400">No description</span>}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Image className="h-4 w-4 mr-1 text-gray-400" />
                        {album.imageCount || 0}
                      </div>
                    </TableCell>
                    <TableCell>
                      {album.createdAt ? format(new Date(album.createdAt), 'MMM dd, yyyy') : '-'}
                    </TableCell>
                    <TableCell>
                      {album.updatedAt ? format(new Date(album.updatedAt), 'MMM dd, yyyy') : '-'}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/gallery/${album.slug}`} target="_blank">
                            View
                          </Link>
                        </Button>
                        
                        <Button variant="ghost" size="sm" asChild>
                          <Link href={`/admin/gallery/edit/${album._id}`}>
                            <Edit className="h-4 w-4" />
                          </Link>
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteClick(album._id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Album</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this album? This will permanently delete the album and all its images. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
              disabled={deleteMutation.isPending}
            >
              {deleteMutation.isPending ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Album"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
