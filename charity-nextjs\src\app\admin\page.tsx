'use client';

import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { FileText, Image, Users, Eye, ChevronRight, BarChart3, PieChart, MessageSquare, Mail, HelpCircle } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import API_CONFIG from "@/config/api";

// Define a type for the visit data
interface VisitDay {
  date: string;
  totalVisits: number;
  uniqueVisits: number;
}

export default function AdminDashboard() {
  const { user } = useAuth();
  const router = useRouter();

  // Fetch analytics data (super-admin only)
  const { data: visitData } = useQuery({
    queryKey: ["visitStats"],
    queryFn: async () => {
      try {
        const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
        const response = await fetch(`${API_CONFIG.backendURL}/api/admin/analytics/visits?days=7`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch visit stats');
        return response.json();
      } catch (error) {
        console.error("Error fetching visit stats:", error);
        return { dates: [], counts: [], totalVisits: 0, uniqueVisits: 0, visitsByDay: [] };
      }
    },
    enabled: user?.role === "super-admin",
  });

  const { data: onlineData } = useQuery({
    queryKey: ["onlineCount"],
    queryFn: async () => {
      try {
        const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
        const response = await fetch(`${API_CONFIG.backendURL}/api/admin/analytics/online`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch online count');
        return response.json();
      } catch (error) {
        console.error("Error fetching online count:", error);
        return { count: 0 };
      }
    },
    enabled: user?.role === "super-admin",
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Fetch news count
  const { data: newsData } = useQuery({
    queryKey: ["newsCount"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_CONFIG.backendURL}/api/news?limit=1`);
        if (!response.ok) throw new Error('Failed to fetch news count');
        return response.json();
      } catch (error) {
        console.error("Error fetching news count:", error);
        return { news: [], pagination: { total: 0, page: 1, pages: 0 } };
      }
    },
  });

  // Fetch albums count
  const { data: albumsData } = useQuery({
    queryKey: ["albumsCount"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_CONFIG.backendURL}/api/gallery/albums`);
        if (!response.ok) throw new Error('Failed to fetch albums count');
        return response.json();
      } catch (error) {
        console.error("Error fetching albums count:", error);
        return { albums: [] };
      }
    },
  });

  // Fetch users count (super-admin only)
  const { data: usersData } = useQuery({
    queryKey: ["usersCount"],
    queryFn: async () => {
      try {
        const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
        const response = await fetch(`${API_CONFIG.backendURL}/api/admin/users`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch users count');
        return response.json();
      } catch (error) {
        console.error("Error fetching users count:", error);
        return { users: [] };
      }
    },
    enabled: user?.role === "super-admin",
  });

  // Fetch contact messages count (super-admin only)
  const { data: contactData } = useQuery({
    queryKey: ["contactMessages"],
    queryFn: async () => {
      try {
        const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
        const response = await fetch(`${API_CONFIG.backendURL}/api/contact?limit=1`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch contact messages');
        return response.json();
      } catch (error) {
        console.error("Error fetching contact messages:", error);
        return { contacts: [], pagination: { total: 0, page: 1, pages: 0 } };
      }
    },
    enabled: user?.role === "super-admin",
  });

  // Fetch subscribers count (super-admin only)
  const { data: subscribersData } = useQuery({
    queryKey: ["subscribersCount"],
    queryFn: async () => {
      try {
        const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
        const response = await fetch(`${API_CONFIG.backendURL}/api/subscribers?limit=1`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch subscribers');
        return response.json();
      } catch (error) {
        console.error("Error fetching subscribers:", error);
        return { subscribers: [], pagination: { total: 0, page: 1, pages: 0 } };
      }
    },
    enabled: user?.role === "super-admin",
  });

  // Fetch FAQ count (available to both editors and admins)
  const { data: faqData } = useQuery({
    queryKey: ["faqCount"],
    queryFn: async () => {
      try {
        const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;
        const response = await fetch(`${API_CONFIG.backendURL}/api/admin/faqs`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch FAQs');
        return response.json();
      } catch (error) {
        console.error("Error fetching FAQs:", error);
        return { faqs: [] };
      }
    }
  });

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold mb-2">Admin Dashboard</h1>
        <p className="text-gray-600">
          Welcome back, {user?.username}. Here&apos;s an overview of your website.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {/* Users Online - High Priority */}
        {user?.role === "super-admin" && (
          <Card className="border-2 border-green-200 shadow-md bg-gradient-to-br from-white to-green-50 md:col-span-2 lg:col-span-1">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-700 flex items-center">
                <Eye className="h-5 w-5 text-green-600 mr-2" />
                Users Online
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <div className="text-4xl font-bold text-green-600">
                  {onlineData?.count || 0}
                </div>
                <div className="ml-3 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  Live
                </div>
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <div className="text-sm text-gray-600">
                Real-time visitor count
              </div>
            </CardFooter>
          </Card>
        )}

        {/* News Articles */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
              <FileText className="h-5 w-5 text-blue-500 mr-2" />
              Total News Articles
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-3xl font-bold text-blue-500">
                {newsData?.pagination?.total || 0}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button
              variant="ghost"
              className="text-blue-500 p-0 h-auto font-medium"
              onClick={() => router.push("/admin/news")}
            >
              Manage News <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardFooter>
        </Card>

        {/* Gallery Albums */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
              <Image className="h-5 w-5 text-purple-500 mr-2" />
              Gallery Albums
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-3xl font-bold text-purple-500">
                {albumsData?.albums?.length || 0}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button
              variant="ghost"
              className="text-purple-500 p-0 h-auto font-medium"
              onClick={() => router.push("/admin/gallery")}
            >
              Manage Gallery <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardFooter>
        </Card>

        {/* System Users - High Priority */}
        {user?.role === "super-admin" && (
          <Card className="border-2 border-orange-200 shadow-md bg-gradient-to-br from-white to-orange-50">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-700 flex items-center">
                <Users className="h-5 w-5 text-orange-600 mr-2" />
                System Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <div className="text-3xl font-bold text-orange-600">
                  {usersData?.users?.length || 0}
                </div>
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <Button
                variant="ghost"
                className="text-orange-600 p-0 h-auto font-medium"
                onClick={() => router.push("/admin/users")}
              >
                Manage Users <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Contact Messages - Medium Priority */}
        {user?.role === "super-admin" && (
          <Card className="border border-purple-200 shadow-sm">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                <MessageSquare className="h-5 w-5 text-purple-500 mr-2" />
                Contact Messages
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <div className="text-3xl font-bold text-purple-500">
                  {contactData?.pagination?.total || 0}
                </div>
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <Button
                variant="ghost"
                className="text-purple-500 p-0 h-auto font-medium"
                onClick={() => router.push("/admin/contact")}
              >
                View Messages <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* Newsletter Subscribers - Medium Priority */}
        {user?.role === "super-admin" && (
          <Card className="border border-teal-200 shadow-sm">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600 flex items-center">
                <Mail className="h-5 w-5 text-teal-500 mr-2" />
                Newsletter Subscribers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <div className="text-3xl font-bold text-teal-500">
                  {subscribersData?.pagination?.total || 0}
                </div>
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <Button
                variant="ghost"
                className="text-teal-500 p-0 h-auto font-medium"
                onClick={() => router.push("/admin/subscribers")}
              >
                Manage Subscribers <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </CardFooter>
          </Card>
        )}

        {/* FAQ Management - Available to both editors and admins */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
              <HelpCircle className="h-5 w-5 text-blue-500 mr-2" />
              FAQ Management
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-3xl font-bold text-blue-500">
                {faqData?.faqs?.length || 0}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button
              variant="ghost"
              className="text-blue-500 p-0 h-auto font-medium"
              onClick={() => router.push("/admin/faqs")}
            >
              Manage FAQs <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardFooter>
        </Card>
      </div>

      {/* Analytics Section - Super Admin Only */}
      {user?.role === "super-admin" && visitData && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Visit Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <BarChart3 className="h-5 w-5 text-blue-500 mr-2" />
                Website Analytics (Last 7 Days)
              </CardTitle>
              <CardDescription>
                Total visits: {visitData.totalVisits || 0} | Unique visitors: {visitData.uniqueVisits || 0}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {visitData.visitsByDay && visitData.visitsByDay.length > 0 ? (
                  visitData.visitsByDay.map((day: VisitDay, index: number) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">
                        {new Date(day.date).toLocaleDateString('en-US', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </span>
                      <div className="flex items-center space-x-4">
                        <div className="text-sm">
                          <span className="font-medium text-blue-600">{day.totalVisits}</span>
                          <span className="text-gray-500 ml-1">total</span>
                        </div>
                        <div className="text-sm">
                          <span className="font-medium text-green-600">{day.uniqueVisits}</span>
                          <span className="text-gray-500 ml-1">unique</span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-4">No visit data available</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <PieChart className="h-5 w-5 text-green-500 mr-2" />
                Content Overview
              </CardTitle>
              <CardDescription>
                Summary of your website content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center">
                    <FileText className="h-5 w-5 text-blue-500 mr-2" />
                    <span className="font-medium">News Articles</span>
                  </div>
                  <span className="text-2xl font-bold text-blue-500">
                    {newsData?.pagination?.total || 0}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                  <div className="flex items-center">
                    <Image className="h-5 w-5 text-purple-500 mr-2" />
                    <span className="font-medium">Gallery Albums</span>
                  </div>
                  <span className="text-2xl font-bold text-purple-500">
                    {albumsData?.albums?.length || 0}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                  <div className="flex items-center">
                    <Users className="h-5 w-5 text-orange-500 mr-2" />
                    <span className="font-medium">System Users</span>
                  </div>
                  <span className="text-2xl font-bold text-orange-500">
                    {usersData?.users?.length || 0}
                  </span>
                </div>

                <div className="flex items-center justify-between p-3 bg-teal-50 rounded-lg">
                  <div className="flex items-center">
                    <Mail className="h-5 w-5 text-teal-500 mr-2" />
                    <span className="font-medium">Subscribers</span>
                  </div>
                  <span className="text-2xl font-bold text-teal-500">
                    {subscribersData?.pagination?.total || 0}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Quick Actions and Management */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quick Actions</CardTitle>
            <CardDescription>Common administrative tasks</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push("/admin/news/create")}
            >
              <FileText className="h-4 w-4 mr-2" />
              Create News Article
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push("/admin/gallery/create")}
            >
              <Image className="h-4 w-4 mr-2" />
              Create Gallery Album
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => router.push("/admin/faq/create")}
            >
              <HelpCircle className="h-4 w-4 mr-2" />
              Add FAQ Item
            </Button>
            {user?.role === "super-admin" && (
              <>
                <Button
                  variant="outline"
                  className="w-full justify-start"
                  onClick={() => router.push("/admin/users/create")}
                >
                  <Users className="h-4 w-4 mr-2" />
                  Add New User
                </Button>
              </>
            )}
          </CardContent>
        </Card>

        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">System Status</CardTitle>
            <CardDescription>Current system information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">System Status:</span>
                <div className="flex items-center">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                  <span className="text-sm font-medium text-green-600">Online</span>
                </div>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Version:</span>
                <span className="text-sm font-medium">2.0.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Environment:</span>
                <span className="text-sm font-medium capitalize">{process.env.NODE_ENV}</span>
              </div>
              {user?.role === "super-admin" && onlineData && (
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600">Active Users:</span>
                  <span className="text-sm font-medium text-blue-600">{onlineData.count}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Last Updated:</span>
                <span className="text-sm font-medium">{new Date().toLocaleDateString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity Placeholder */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Recent Activity</CardTitle>
            <CardDescription>Latest system activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="text-sm text-gray-600">
                <div className="flex items-center justify-between">
                  <span>Dashboard accessed</span>
                  <span className="text-xs text-gray-400">Now</span>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                <div className="flex items-center justify-between">
                  <span>User {user?.username} logged in</span>
                  <span className="text-xs text-gray-400">Today</span>
                </div>
              </div>
              <div className="text-sm text-gray-600">
                <div className="flex items-center justify-between">
                  <span>System status: Healthy</span>
                  <span className="text-xs text-gray-400">Today</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
