'use client';

import { useQuery } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { FileText, Image, Users, Eye, ChevronRight, MessageSquare, Mail, HelpCircle } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";
import API_CONFIG from "@/config/api";

// Define a type for the visit data
interface VisitDay {
  date: string;
  totalVisits: number;
  uniqueVisits: number;
}

export default function AdminDashboard() {
  const { user } = useAuth();
  const router = useRouter();

  // Fetch analytics data (super-admin only)
  const { data: visitData } = useQuery({
    queryKey: ["visitStats"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_CONFIG.backendURL}/api/admin/analytics/visits?days=7`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch visit stats');
        return response.json();
      } catch (error) {
        console.error("Error fetching visit stats:", error);
        return { dates: [], counts: [], totalVisits: 0, uniqueVisits: 0, visitsByDay: [] };
      }
    },
    enabled: user?.role === "super-admin",
  });

  const { data: onlineData } = useQuery({
    queryKey: ["onlineCount"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_CONFIG.backendURL}/api/admin/analytics/online`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch online count');
        return response.json();
      } catch (error) {
        console.error("Error fetching online count:", error);
        return { count: 0 };
      }
    },
    enabled: user?.role === "super-admin",
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Fetch news count
  const { data: newsData } = useQuery({
    queryKey: ["newsCount"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_CONFIG.backendURL}/api/news?limit=1`);
        if (!response.ok) throw new Error('Failed to fetch news count');
        return response.json();
      } catch (error) {
        console.error("Error fetching news count:", error);
        return { news: [], pagination: { total: 0, page: 1, pages: 0 } };
      }
    },
  });

  // Fetch albums count
  const { data: albumsData } = useQuery({
    queryKey: ["albumsCount"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_CONFIG.backendURL}/api/gallery/albums`);
        if (!response.ok) throw new Error('Failed to fetch albums count');
        return response.json();
      } catch (error) {
        console.error("Error fetching albums count:", error);
        return { albums: [] };
      }
    },
  });

  // Fetch users count (super-admin only)
  const { data: usersData } = useQuery({
    queryKey: ["usersCount"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_CONFIG.backendURL}/api/admin/users`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch users count');
        return response.json();
      } catch (error) {
        console.error("Error fetching users count:", error);
        return { users: [] };
      }
    },
    enabled: user?.role === "super-admin",
  });

  // Fetch contact messages count (super-admin only)
  const { data: contactData } = useQuery({
    queryKey: ["contactMessages"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_CONFIG.backendURL}/api/contact?limit=1`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch contact messages');
        return response.json();
      } catch (error) {
        console.error("Error fetching contact messages:", error);
        return { contacts: [], pagination: { total: 0, page: 1, pages: 0 } };
      }
    },
    enabled: user?.role === "super-admin",
  });

  // Fetch subscribers count (super-admin only)
  const { data: subscribersData } = useQuery({
    queryKey: ["subscribersCount"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_CONFIG.backendURL}/api/subscribers?limit=1`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch subscribers');
        return response.json();
      } catch (error) {
        console.error("Error fetching subscribers:", error);
        return { subscribers: [], pagination: { total: 0, page: 1, pages: 0 } };
      }
    },
    enabled: user?.role === "super-admin",
  });

  // Fetch FAQ count (available to both editors and admins)
  const { data: faqData } = useQuery({
    queryKey: ["faqCount"],
    queryFn: async () => {
      try {
        const response = await fetch(`${API_CONFIG.backendURL}/api/admin/faqs`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          },
        });
        if (!response.ok) throw new Error('Failed to fetch FAQs');
        return response.json();
      } catch (error) {
        console.error("Error fetching FAQs:", error);
        return { faqs: [] };
      }
    }
  });

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold mb-2">Admin Dashboard</h1>
        <p className="text-gray-600">
          Welcome back, {user?.username}. Here's an overview of your website.
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {/* Users Online - High Priority */}
        {user?.role === "super-admin" && (
          <Card className="border-2 border-green-200 shadow-md bg-gradient-to-br from-white to-green-50">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-700 flex items-center">
                <Eye className="h-5 w-5 text-green-600 mr-2" />
                Users Online
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center">
                <div className="text-4xl font-bold text-green-600">
                  {onlineData?.count || 0}
                </div>
                <div className="ml-3 bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                  Live
                </div>
              </div>
            </CardContent>
            <CardFooter className="pt-0">
              <div className="text-sm text-gray-600">
                Real-time visitor count
              </div>
            </CardFooter>
          </Card>
        )}

        {/* News Articles */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
              <FileText className="h-5 w-5 text-blue-500 mr-2" />
              Total News Articles
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-3xl font-bold text-blue-500">
                {newsData?.pagination?.total || 0}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button
              variant="ghost"
              className="text-blue-500 p-0 h-auto font-medium"
              onClick={() => router.push("/admin/news")}
            >
              Manage News <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardFooter>
        </Card>

        {/* Gallery Albums */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
              <Image className="h-5 w-5 text-purple-500 mr-2" />
              Gallery Albums
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-3xl font-bold text-purple-500">
                {albumsData?.albums?.length || 0}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button
              variant="ghost"
              className="text-purple-500 p-0 h-auto font-medium"
              onClick={() => router.push("/admin/gallery")}
            >
              Manage Gallery <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardFooter>
        </Card>

        {/* FAQ Count */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
              <HelpCircle className="h-5 w-5 text-orange-500 mr-2" />
              FAQ Items
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <div className="text-3xl font-bold text-orange-500">
                {faqData?.faqs?.length || 0}
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button
              variant="ghost"
              className="text-orange-500 p-0 h-auto font-medium"
              onClick={() => router.push("/admin/faq")}
            >
              Manage FAQ <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardFooter>
        </Card>

        {/* Super Admin Only Stats */}
        {user?.role === "super-admin" && (
          <>
            {/* Users */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
                  <Users className="h-5 w-5 text-indigo-500 mr-2" />
                  Total Users
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <div className="text-3xl font-bold text-indigo-500">
                    {usersData?.users?.length || 0}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button
                  variant="ghost"
                  className="text-indigo-500 p-0 h-auto font-medium"
                  onClick={() => router.push("/admin/users")}
                >
                  Manage Users <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </CardFooter>
            </Card>

            {/* Contact Messages */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
                  <MessageSquare className="h-5 w-5 text-red-500 mr-2" />
                  Contact Messages
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <div className="text-3xl font-bold text-red-500">
                    {contactData?.pagination?.total || 0}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button
                  variant="ghost"
                  className="text-red-500 p-0 h-auto font-medium"
                  onClick={() => router.push("/admin/contact")}
                >
                  View Messages <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </CardFooter>
            </Card>

            {/* Subscribers */}
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-500 flex items-center">
                  <Mail className="h-5 w-5 text-teal-500 mr-2" />
                  Newsletter Subscribers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <div className="text-3xl font-bold text-teal-500">
                    {subscribersData?.pagination?.total || 0}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button
                  variant="ghost"
                  className="text-teal-500 p-0 h-auto font-medium"
                  onClick={() => router.push("/admin/subscribers")}
                >
                  View Subscribers <ChevronRight className="h-4 w-4 ml-1" />
                </Button>
              </CardFooter>
            </Card>
          </>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Quick Actions</CardTitle>
            <CardDescription>Common administrative tasks</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/admin/news/create")}
            >
              <FileText className="h-4 w-4 mr-2" />
              Create News Article
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/admin/gallery/create")}
            >
              <Image className="h-4 w-4 mr-2" />
              Create Gallery Album
            </Button>
            <Button 
              variant="outline" 
              className="w-full justify-start"
              onClick={() => router.push("/admin/faq/create")}
            >
              <HelpCircle className="h-4 w-4 mr-2" />
              Add FAQ Item
            </Button>
          </CardContent>
        </Card>

        {/* Recent Activity would go here */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">System Status</CardTitle>
            <CardDescription>Current system information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Status:</span>
                <span className="text-sm font-medium text-green-600">Online</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Version:</span>
                <span className="text-sm font-medium">1.0.0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Environment:</span>
                <span className="text-sm font-medium">{process.env.NODE_ENV}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
