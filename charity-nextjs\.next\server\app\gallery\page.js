(()=>{var e={};e.id=235,e.ids=[235],e.modules={251:(e,a,t)=>{"use strict";t.d(a,{GalleryPageContent:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call GalleryPageContent() from the server but GalleryPageContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\GalleryPageContent.tsx","GalleryPageContent")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},13555:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=t(65239),r=t(48088),i=t(88170),l=t.n(i),n=t(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(a,o);let c={children:["",{children:["gallery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,89468)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\page.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,92441)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,68178)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,99766)),"C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive\\Desktop\\charity_info\\charity_info\\charity-nextjs\\app\\gallery\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/gallery/page",pathname:"/gallery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23280:(e,a,t)=>{Promise.resolve().then(t.bind(t,251))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44493:(e,a,t)=>{"use strict";t.d(a,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>l,aR:()=>n,wL:()=>m});var s=t(60687),r=t(43210),i=t(4780);let l=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...a}));l.displayName="Card";let n=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...a}));n.displayName="CardHeader";let o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));o.displayName="CardTitle";let c=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...a}));c.displayName="CardDescription";let d=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...a}));d.displayName="CardContent";let m=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...a}));m.displayName="CardFooter"},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62185:(e,a,t)=>{"use strict";t.d(a,{AY:()=>m,EO:()=>l,TP:()=>o,YV:()=>n,jE:()=>u,l4:()=>d,lM:()=>c});var s=t(51060),r=t(70216);let i=s.A.create({baseURL:r.A.baseURL,timeout:r.A.timeout,withCredentials:r.A.withCredentials});i.interceptors.request.use(async e=>e,e=>Promise.reject(e)),i.interceptors.response.use(e=>e,e=>(e.response?.status,Promise.reject(e)));let l={getContent:async()=>(await i.get("/about")).data,updateContent:async e=>(await i.post("/about",e)).data},n={getAll:async e=>({teamMembers:(await i.get("/team",{params:e})).data}),getById:async e=>({teamMember:(await i.get(`/team/${e}`)).data}),create:async e=>(await i.post("/team",e,{headers:{"Content-Type":"multipart/form-data"}})).data,update:async(e,a)=>(await i.put(`/team/${e}`,a,{headers:{"Content-Type":"multipart/form-data"}})).data,delete:async e=>(await i.delete(`/team/${e}`)).data},o={submit:async e=>(await i.post("/contact",e)).data,getAll:async(e=1,a=10,t)=>{let s={page:e,limit:a,...t?{status:t}:{}};return(await i.get("/contact",{params:s})).data},getById:async e=>(await i.get(`/contact/${e}`)).data,updateStatus:async(e,a)=>(await i.put(`/contact/${e}/status`,{status:a})).data,delete:async e=>(await i.delete(`/contact/${e}`)).data},c={getAll:async e=>({locations:(await i.get("/locations",{params:e})).data}),getById:async e=>({location:(await i.get(`/locations/${e}`)).data}),create:async e=>{let a={...e,isMainOffice:void 0!==e.isMainOffice?String(e.isMainOffice):void 0,active:void 0!==e.active?String(e.active):void 0};return(await i.post("/locations",a)).data},update:async(e,a)=>{let t={...a,isMainOffice:void 0!==a.isMainOffice?String(a.isMainOffice):void 0,active:void 0!==a.active?String(a.active):void 0};return(await i.put(`/locations/${e}`,t)).data},delete:async e=>(await i.delete(`/locations/${e}`)).data},d={getAll:async()=>(await i.get("/faqs")).data,getAllAdmin:async()=>(await i.get("/admin/faqs")).data,getById:async e=>(await i.get(`/admin/faqs/${e}`)).data,create:async e=>(await i.post("/admin/faqs",e)).data,update:async(e,a,t=!1)=>{let s;s=t?{isActive:void 0===a.isActive||!!a.isActive}:{question:a.question?.trim(),answer:a.answer?.trim(),category:a.category?.trim()||"General",order:"number"==typeof a.order?a.order:0,isActive:void 0===a.isActive||!!a.isActive};try{return(await i.put(`/admin/faqs/${e}`,s)).data}catch(e){throw e}},delete:async e=>(await i.delete(`/admin/faqs/${e}`)).data},m={getAll:async(e=1,a=10)=>(await i.get(`/news?page=${e}&limit=${a}&includeAttachments=true`)).data,getBySlug:async e=>(await i.get(`/news/${e}?includeAttachments=true`)).data.news,getById:async e=>(await i.get(`/admin/news/${e}`)).data.news,create:async e=>(await i.post("/admin/news",e)).data.news,update:async(e,a)=>(await i.put(`/admin/news/${e}`,a)).data.news,delete:async e=>(await i.delete(`/admin/news/${e}`)).data},u={getAllAlbums:async()=>(await i.get("/gallery/albums")).data,getAlbumBySlug:async e=>{try{return(await i.get(`/gallery/albums/${e}`)).data}catch(e){throw e}},getAlbumById:async e=>{try{return(await i.get(`/admin/gallery/albums/${e}`)).data}catch(e){throw e}},createAlbum:async e=>(await i.post("/admin/gallery/albums",e)).data.album,updateAlbum:async(e,a)=>(await i.put(`/admin/gallery/albums/${e}`,a)).data.album,deleteAlbum:async e=>{await i.delete(`/admin/gallery/albums/${e}`)},uploadImage:async(e,a)=>{let t=document.querySelector('meta[name="csrf-token"]')?.getAttribute("content");return t&&a.append("_csrf",t),(await i.post(`/admin/gallery/albums/${e}/images`,a,{headers:{"Content-Type":"multipart/form-data"}})).data},deleteImage:async e=>{await i.delete(`/admin/gallery/images/${e}`)}}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64457:(e,a,t)=>{"use strict";t.d(a,{GalleryPageContent:()=>p});var s=t(60687),r=t(43210),i=t(85814),l=t.n(i),n=t(30474),o=t(51423),c=t(9005),d=t(44493),m=t(29523),u=t(62185);function p(){let[e,a]=(0,r.useState)({}),{data:t,isLoading:i,isError:p}=(0,o.I)({queryKey:["albums"],queryFn:u.jE.getAllAlbums});return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold mb-2",children:"Gallery"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Browse our collection of images and photos"})]}),i?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((e,a)=>(0,s.jsxs)(d.Zp,{className:"animate-pulse overflow-hidden",children:[(0,s.jsx)("div",{className:"aspect-square bg-gray-200"}),(0,s.jsxs)(d.Wu,{className:"p-4",children:[(0,s.jsx)("div",{className:"h-5 bg-gray-200 rounded w-3/4 mb-2"}),(0,s.jsx)("div",{className:"h-4 bg-gray-100 rounded w-full"})]})]},a))}):p?(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("p",{className:"text-xl text-gray-600",children:"Failed to load albums."}),(0,s.jsx)(m.$,{onClick:()=>window.location.reload(),className:"mt-4",children:"Try Again"})]}):t?.albums?.length===0?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-xl text-gray-600",children:"No albums found."})}):(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:t?.albums?.map(a=>(0,s.jsxs)(d.Zp,{className:"overflow-hidden group hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"aspect-square bg-gray-100 flex items-center justify-center overflow-hidden relative",children:[e[a._id]?(0,s.jsx)(n.default,{src:e[a._id],alt:a.title,fill:!0,className:"object-cover group-hover:scale-105 transition-transform duration-300",sizes:"(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw",onError:e=>{e.currentTarget.style.display="none";let a=e.currentTarget.parentElement;if(a){let e=a.querySelector(".fallback-icon");e&&(e.style.display="block")}}}):null,(0,s.jsx)(c.A,{className:"fallback-icon h-12 w-12 text-gray-400",style:{display:e[a._id]?"none":"block"}})]}),(0,s.jsxs)(d.Wu,{className:"p-4",children:[(0,s.jsx)("h3",{className:"font-semibold text-lg mb-2 truncate",children:a.title}),(0,s.jsx)("p",{className:"text-gray-600 text-sm line-clamp-2",children:a.description||"No description available"})]}),(0,s.jsxs)(d.wL,{className:"p-4 pt-0 flex flex-col gap-2 w-full mt-auto",children:[(0,s.jsxs)("p",{className:"text-sm text-gray-500 w-full",children:[a.imageCount||0," ",1===a.imageCount?"image":"images"]}),(0,s.jsx)(m.$,{asChild:!0,className:"w-full",children:(0,s.jsx)(l(),{href:`/gallery/${a.slug}`,children:"View Album"})})]})]},a._id))})]})}t(70216)},70216:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s={baseURL:"/api",backendURL:"http://localhost:5000",timeout:15e3,withCredentials:!0,headers:{"Cache-Control":"max-age=300","Content-Type":"application/json"}}},74075:e=>{"use strict";e.exports=require("zlib")},78963:(e,a,t)=>{"use strict";t.d(a,{Wu:()=>n,Zp:()=>l,wL:()=>o});var s=t(37413),r=t(61120),i=t(10974);let l=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...a}));l.displayName="Card",r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...a})).displayName="CardHeader",r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a})).displayName="CardTitle",r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...a})).displayName="CardDescription";let n=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...a}));n.displayName="CardContent";let o=r.forwardRef(({className:e,...a},t)=>(0,s.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...a}));o.displayName="CardFooter"},79551:e=>{"use strict";e.exports=require("url")},81424:(e,a,t)=>{Promise.resolve().then(t.bind(t,64457))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89468:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>l,metadata:()=>i});var s=t(37413),r=t(251);let i={title:"Gallery - Charity Welcome Hub",description:"Browse our collection of images and photos showcasing our charitable work, events, and community impact.",keywords:["gallery","photos","images","charity events","community","albums","pictures"],openGraph:{title:"Gallery - Charity Welcome Hub",description:"Browse our collection of images and photos showcasing our charitable work, events, and community impact.",type:"website"},twitter:{card:"summary_large_image",title:"Gallery - Charity Welcome Hub",description:"Browse our collection of images and photos showcasing our charitable work, events, and community impact."}};function l(){return(0,s.jsx)(r.GalleryPageContent,{})}},92441:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>l});var s=t(37413),r=t(6972),i=t(78963);function l(){return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)(r.E,{className:"h-8 w-32 mb-2"}),(0,s.jsx)(r.E,{className:"h-4 w-64"})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:[...Array(8)].map((e,a)=>(0,s.jsxs)(i.Zp,{className:"overflow-hidden",children:[(0,s.jsx)(r.E,{className:"aspect-square w-full"}),(0,s.jsxs)(i.Wu,{className:"p-4",children:[(0,s.jsx)(r.E,{className:"h-5 w-full mb-2"}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(r.E,{className:"h-4 w-full"}),(0,s.jsx)(r.E,{className:"h-4 w-2/3"})]})]}),(0,s.jsxs)(i.wL,{className:"p-4 pt-0 flex flex-col gap-2",children:[(0,s.jsx)(r.E,{className:"h-4 w-20"}),(0,s.jsx)(r.E,{className:"h-9 w-full"})]})]},a))})]})}},94735:e=>{"use strict";e.exports=require("events")}};var a=require("../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[447,699,62,474,775],()=>t(13555));module.exports=s})();