'use client';

import { useEffect } from 'react';

/**
 * Component to preload critical resources
 * This helps improve performance by loading important assets before they're needed
 */
export function Preload() {
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return;

    // Preload critical images
    const preloadImages = [
      '/placeholder.svg',
      // Add other critical images here
    ];

    preloadImages.forEach(src => {
      const img = new Image();
      img.src = src;
    });

    // Preload critical routes using Next.js prefetch
    const preloadRoutes = [
      '/news',
      '/gallery',
      '/faq',
      '/about',
      '/contact',
    ];

    // Use requestIdleCallback to preload routes when browser is idle
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => {
        preloadRoutes.forEach(route => {
          const link = document.createElement('link');
          link.rel = 'prefetch';
          link.href = route;
          document.head.appendChild(link);
        });
      });
    } else {
      // Fallback for browsers that don't support requestIdleCallback
      setTimeout(() => {
        preloadRoutes.forEach(route => {
          const link = document.createElement('link');
          link.rel = 'prefetch';
          link.href = route;
          document.head.appendChild(link);
        });
      }, 1000);
    }
  }, []);

  // This component doesn't render anything
  return null;
}

export default Preload;
