'use client';

import { useState, useEffect } from 'react';
import API_CONFIG from '@/config/api';

export const useCsrf = () => {
  const [csrfToken, setCsrfToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') {
      setIsLoading(false);
      return;
    }

    const fetchCsrfToken = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`${API_CONFIG.baseURL}/api/csrf-token`, { 
          credentials: 'include' 
        });
        
        if (!response.ok) {
          throw new Error('Failed to fetch CSRF token');
        }
        
        const data = await response.json();
        setCsrfToken(data.csrfToken);
      } catch (err) {
        setError(err as Error);
        console.error('Failed to fetch CSRF token:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchCsrfToken();
  }, []);
  
  return { csrfToken, isLoading, error };
};
