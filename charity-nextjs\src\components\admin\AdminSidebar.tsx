'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { 
  LayoutDashboard, 
  FileText, 
  Image, 
  Users, 
  MessageSquare, 
  Mail, 
  HelpCircle, 
  MapPin, 
  Handshake, 
  Settings, 
  LogOut,
  UserCheck
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface NavItem {
  title: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  requiredRole?: 'super-admin' | 'editor';
}

const navItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
  },
  {
    title: 'News',
    href: '/admin/news',
    icon: FileText,
  },
  {
    title: 'Gallery',
    href: '/admin/gallery',
    icon: Image,
  },
  {
    title: 'FAQ',
    href: '/admin/faq',
    icon: HelpCircle,
  },
  {
    title: 'Team',
    href: '/admin/team',
    icon: UserCheck,
  },
  {
    title: 'Locations',
    href: '/admin/locations',
    icon: MapPin,
    requiredRole: 'super-admin',
  },
  {
    title: 'Partners',
    href: '/admin/partners',
    icon: Handshake,
    requiredRole: 'super-admin',
  },
  {
    title: 'Users',
    href: '/admin/users',
    icon: Users,
    requiredRole: 'super-admin',
  },
  {
    title: 'Contact Messages',
    href: '/admin/contact',
    icon: MessageSquare,
    requiredRole: 'super-admin',
  },
  {
    title: 'Subscribers',
    href: '/admin/subscribers',
    icon: Mail,
    requiredRole: 'super-admin',
  },
  {
    title: 'Settings',
    href: '/admin/settings',
    icon: Settings,
    requiredRole: 'super-admin',
  },
];

export function AdminSidebar() {
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const filteredNavItems = navItems.filter(item => {
    if (!item.requiredRole) return true;
    return user?.role === 'super-admin' || user?.role === item.requiredRole;
  });

  return (
    <div className="w-64 bg-white shadow-lg flex flex-col">
      {/* Header */}
      <div className="p-6 border-b">
        <h2 className="text-xl font-bold text-gray-800">Admin Panel</h2>
        <p className="text-sm text-gray-600 mt-1">
          {user?.username} ({user?.role})
        </p>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {filteredNavItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href || 
              (item.href !== '/admin' && pathname.startsWith(item.href));

            return (
              <li key={item.href}>
                <Link
                  href={item.href}
                  className={cn(
                    'flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors',
                    isActive
                      ? 'bg-teal-100 text-teal-700 border-r-2 border-teal-500'
                      : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                  )}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.title}
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t">
        <Button
          variant="ghost"
          className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
          onClick={logout}
        >
          <LogOut className="h-5 w-5 mr-3" />
          Logout
        </Button>
      </div>
    </div>
  );
}
