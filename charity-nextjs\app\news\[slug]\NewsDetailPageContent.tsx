'use client';

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import { Loader, Paperclip, FileText, FileImage, File, Calendar, X, ChevronLeft, ChevronRight } from "lucide-react";
import { format } from "date-fns";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { newsApi } from "@/lib/api";
import { ApiError } from "@/components/ui/api-error";
import { parseNewsContent } from "@/lib/utils";
import API_CONFIG from "@/config/api";

interface NewsDetailPageContentProps {
  slug: string;
}

// Helper function to format author
function formatAuthor(author: string | { _id: string; username: string }): string {
  if (typeof author === 'string') {
    return author;
  }
  return author.username || 'Unknown Author';
}

// Helper function to get file icon
function getFileIcon(mimetype: string) {
  if (mimetype.startsWith('image/')) return FileImage;
  if (mimetype.includes('text') || mimetype.includes('document')) return FileText;
  return File;
}

export function NewsDetailPageContent({ slug }: NewsDetailPageContentProps) {
  const [attachments, setAttachments] = useState<Array<{ _id: string; filename: string; mimeType: string; size: number }>>([]);
  const [textContents, setTextContents] = useState<Record<string, { content: string, filename: string }>>({});
  const [loadingAttachments, setLoadingAttachments] = useState<Record<string, boolean>>({});
  const [featuredImageId, setFeaturedImageId] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState<number>(-1);

  const { data: newsItem, isLoading: isNewsLoading, error, refetch } = useQuery({
    queryKey: ['news', slug],
    queryFn: () => newsApi.getBySlug(slug || ''),
    enabled: !!slug,
  });

  // Process attachments when news item loads
  useEffect(() => {
    if (newsItem?.attachments) {
      const processedAttachments = newsItem.attachments.map((att: { _id: string; filename: string; mimeType: string; size: number }) => ({
        ...att,
        url: `${API_CONFIG.baseURL}/api/news/attachments/${att._id}/content`,
        downloadUrl: `${API_CONFIG.baseURL}/api/news/attachments/${att._id}/download`
      }));
      
      setAttachments(processedAttachments);
      
      // Set featured image (first image attachment)
      const firstImage = processedAttachments.find((att: { mimeType: string }) =>
        att.mimeType && att.mimeType.startsWith('image/')
      );
      if (firstImage) {
        setFeaturedImageId(firstImage._id);
      }
    }
  }, [newsItem]);

  // Handle text file content loading
  const loadTextContent = useCallback(async (attachmentId: string, filename: string) => {
    if (textContents[attachmentId] || loadingAttachments[attachmentId]) return;

    setLoadingAttachments(prev => ({ ...prev, [attachmentId]: true }));
    
    try {
      const response = await fetch(`${API_CONFIG.baseURL}/api/news/attachments/${attachmentId}/content`);
      if (response.ok) {
        const content = await response.text();
        setTextContents(prev => ({
          ...prev,
          [attachmentId]: { content, filename }
        }));
      }
    } catch (error) {
      console.error('Failed to load text content:', error);
    } finally {
      setLoadingAttachments(prev => ({ ...prev, [attachmentId]: false }));
    }
  }, [textContents, loadingAttachments]);

  // Handle image modal
  const openImageModal = (imageUrl: string, index: number) => {
    setSelectedImage(imageUrl);
    setSelectedImageIndex(index);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
    setSelectedImageIndex(-1);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    const imageAttachments = attachments.filter(att => att.mimetype?.startsWith('image/'));
    if (imageAttachments.length === 0) return;

    let newIndex = selectedImageIndex;
    if (direction === 'prev') {
      newIndex = selectedImageIndex > 0 ? selectedImageIndex - 1 : imageAttachments.length - 1;
    } else {
      newIndex = selectedImageIndex < imageAttachments.length - 1 ? selectedImageIndex + 1 : 0;
    }

    setSelectedImageIndex(newIndex);
    setSelectedImage(imageAttachments[newIndex].url);
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!selectedImage) return;
      
      if (e.key === 'Escape') {
        closeImageModal();
      } else if (e.key === 'ArrowLeft') {
        navigateImage('prev');
      } else if (e.key === 'ArrowRight') {
        navigateImage('next');
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [selectedImage, selectedImageIndex]);

  // Group attachments by type
  const imageAttachments = attachments.filter(att => att.mimetype?.startsWith('image/'));
  const documentAttachments = attachments.filter(att => !att.mimetype?.startsWith('image/'));

  if (isNewsLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center min-h-[400px]">
          <Loader className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  if (error || !newsItem) {
    return (
      <div className="container mx-auto px-4 py-8">
        <ApiError
          title="Failed to load news article"
          error={error}
          onRetry={refetch}
        />
        <div className="mt-4">
          <Link href="/news">
            <Button variant="outline">Back to News</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Link href="/news" className="text-primary hover:underline mb-4 inline-block">
        &larr; Back to News
      </Link>

      <Card className="mt-4 overflow-hidden">
        {/* Display featured image if available */}
        {featuredImageId && (
          <div className="w-full h-[350px] md:h-[450px] bg-gray-100 relative overflow-hidden">
            <Image
              src={`${API_CONFIG.baseURL}/news/attachments/${featuredImageId}/content`}
              alt={newsItem.title}
              fill
              className="object-cover transition-transform duration-700 hover:scale-105"
              priority
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 100vw, 100vw"
              onError={(e) => {
                console.error(`Failed to load featured image`);
                e.currentTarget.src = '/placeholder.svg';
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent"></div>
            <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
              <h1 className="text-3xl md:text-4xl font-bold mb-2 drop-shadow-md">{newsItem.title}</h1>
              <div className="flex items-center text-white/80 text-sm md:text-base">
                <Calendar className="h-4 w-4 mr-1" />
                <span>
                  {newsItem.publishDate && format(new Date(newsItem.publishDate), 'MMMM dd, yyyy')}
                </span>
                {newsItem.author && (
                  <span className="ml-4">
                    by {formatAuthor(newsItem.author)}
                  </span>
                )}
              </div>
            </div>
          </div>
        )}

        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              {!featuredImageId && (
                <>
                  <CardTitle className="text-3xl">{newsItem.title}</CardTitle>
                  <CardDescription className="flex items-center gap-2 mt-2">
                    <Calendar className="h-4 w-4" />
                    {newsItem.publishDate && (
                      <span className="text-gray-500">
                        {format(new Date(newsItem.publishDate), 'MMMM dd, yyyy')}
                      </span>
                    )}
                    {newsItem.author && (
                      <span className="ml-2 text-gray-500">
                        by {formatAuthor(newsItem.author)}
                      </span>
                    )}
                  </CardDescription>
                </>
              )}
            </div>

            {!newsItem.published && (
              <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
                Draft
              </Badge>
            )}
          </div>
        </CardHeader>

        <CardContent className="prose prose-lg max-w-none">
          <div
            dangerouslySetInnerHTML={{
              __html: parseNewsContent(newsItem.body)
            }}
          />
        </CardContent>
      </Card>

      {/* Image Gallery */}
      {imageAttachments.length > 1 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileImage className="mr-2 h-5 w-5" />
              Image Gallery ({imageAttachments.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {imageAttachments.slice(1).map((attachment, index) => {
                return (
                  <div
                    key={attachment._id}
                    className="relative group cursor-pointer"
                    onClick={() => openImageModal(attachment.url, index + 1)}
                  >
                    <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden relative">
                      <Image
                        src={attachment.url}
                        alt={attachment.filename}
                        fill
                        className="object-cover transition-transform duration-300 group-hover:scale-105"
                        sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                        onError={(e) => {
                          e.currentTarget.src = '/placeholder.svg';
                        }}
                      />
                    </div>
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 rounded-lg" />
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Document Attachments */}
      {documentAttachments.length > 0 && (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Paperclip className="mr-2 h-5 w-5" />
              Attachments ({documentAttachments.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {documentAttachments.map((attachment) => {
                const IconComponent = getFileIcon(attachment.mimetype);
                const isTextFile = attachment.mimetype?.includes('text') || 
                                 attachment.filename?.endsWith('.txt') ||
                                 attachment.filename?.endsWith('.md');
                
                return (
                  <div key={attachment._id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <IconComponent className="h-6 w-6 text-gray-500" />
                        <div>
                          <p className="font-medium">{attachment.filename}</p>
                          <p className="text-sm text-gray-500">
                            {attachment.mimetype} • {(attachment.size / 1024).toFixed(1)} KB
                          </p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        {isTextFile && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => loadTextContent(attachment._id, attachment.filename)}
                            disabled={loadingAttachments[attachment._id]}
                          >
                            {loadingAttachments[attachment._id] ? (
                              <Loader className="h-4 w-4 animate-spin" />
                            ) : (
                              'Preview'
                            )}
                          </Button>
                        )}
                        <Button variant="outline" size="sm" asChild>
                          <a href={attachment.downloadUrl} download>
                            Download
                          </a>
                        </Button>
                      </div>
                    </div>
                    
                    {/* Text content preview */}
                    {textContents[attachment._id] && (
                      <div className="mt-4 p-3 bg-gray-50 rounded border">
                        <h4 className="font-medium mb-2">{textContents[attachment._id].filename}</h4>
                        <pre className="text-sm whitespace-pre-wrap text-gray-700 max-h-64 overflow-y-auto">
                          {textContents[attachment._id].content}
                        </pre>
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={closeImageModal}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
            >
              <X className="h-6 w-6" />
            </button>
            
            {imageAttachments.length > 1 && (
              <>
                <button
                  onClick={() => navigateImage('prev')}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
                >
                  <ChevronLeft className="h-8 w-8" />
                </button>
                <button
                  onClick={() => navigateImage('next')}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300 z-10"
                >
                  <ChevronRight className="h-8 w-8" />
                </button>
              </>
            )}
            
            <img
              src={selectedImage}
              alt="Full size"
              className="max-w-full max-h-full object-contain"
              crossOrigin="anonymous"
            />
          </div>
        </div>
      )}
    </div>
  );
}
