import { Metadata } from 'next';
import { GalleryPageContent } from './GalleryPageContent';

export const metadata: Metadata = {
  title: 'Gallery - Charity Welcome Hub',
  description: 'Browse our collection of images and photos showcasing our charitable work, events, and community impact.',
  keywords: ['gallery', 'photos', 'images', 'charity events', 'community', 'albums', 'pictures'],
  openGraph: {
    title: 'Gallery - Charity Welcome Hub',
    description: 'Browse our collection of images and photos showcasing our charitable work, events, and community impact.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Gallery - Charity Welcome Hub',
    description: 'Browse our collection of images and photos showcasing our charitable work, events, and community impact.',
  },
};

export default function GalleryPage() {
  return <GalleryPageContent />;
}
