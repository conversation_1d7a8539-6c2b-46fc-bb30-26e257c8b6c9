(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>o});var r=s(5155),a=s(2115),i=s(9708),l=s(2085),n=s(9434);let o=(0,l.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-sm hover:shadow-md transition-all duration-200",{variants:{variant:{default:"bg-charity-primary text-white hover:bg-charity-secondary",destructive:"bg-charity-destructive text-white hover:bg-charity-destructive/90",outline:"border border-input bg-transparent text-foreground hover:bg-accent hover:text-accent-foreground",secondary:"bg-charity-light text-charity-dark hover:bg-charity-light/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-charity-primary underline-offset-4 hover:underline",accent:"bg-charity-accent text-charity-dark hover:bg-charity-accent/80",success:"bg-charity-success text-white hover:bg-charity-success/90",warning:"bg-charity-warning text-white hover:bg-charity-warning/90",info:"bg-charity-info text-white hover:bg-charity-info/90",donate:"bg-gradient-to-r from-charity-primary to-charity-secondary text-white hover:brightness-105"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3 py-1.5 text-xs",lg:"h-11 rounded-md px-8 py-2.5",xl:"h-12 rounded-md px-10 py-3 text-base",icon:"h-10 w-10 rounded-full",wide:"h-10 px-8 py-2 w-full"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:s,variant:a,size:l,asChild:c=!1,...d}=e,h=c?i.DX:"button";return(0,r.jsx)(h,{className:(0,n.cn)(o({variant:a,size:l,className:s})),ref:t,...d})});c.displayName="Button"},1116:(e,t,s)=>{"use strict";s.d(t,{WebVitals:()=>l});var r=s(2115),a=s(9444);function i(e){}function l(){return(0,r.useEffect)(()=>{(0,a.IN)(i),(0,a.rH)(i),(0,a.zB)(i),(0,a.fK)(i),(0,a.Ck)(i)},[]),null}},2523:(e,t,s)=>{"use strict";s.d(t,{p:()=>l});var r=s(5155),a=s(2115),i=s(9434);let l=a.forwardRef((e,t)=>{let{className:s,type:a,...l}=e;return(0,r.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border-[1px] border-input bg-background px-3 py-2 text-base text-foreground ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground/60 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-charity-primary focus-visible:border-charity-primary focus-visible:ring-offset-1 disabled:cursor-not-allowed disabled:opacity-50 transition-colors duration-200 md:text-sm shadow-none",s),ref:t,...l})});l.displayName="Input"},2548:(e,t,s)=>{"use strict";s.d(t,{Footer:()=>b});var r=s(5155),a=s(2115),i=s(6874),l=s.n(i),n=s(9420),o=s(1264),c=s(4516),d=s(9074),h=s(8304),m=s(7213),x=s(1284),u=s(7924),f=s(9947),v=s(238),p=s(1976);function b(){let e=new Date().getFullYear(),t={locations:[{isMainOffice:!0,phone:"+****************",email:"<EMAIL>",address:"123 Charity Street, City, State 12345"}]},s=a.useMemo(()=>(null==t?void 0:t.locations)?t.locations.find(e=>e.isMainOffice)||t.locations[0]:null,[t]);return(0,r.jsx)("footer",{className:"bg-teal-50 border-t border-teal-100 py-10 mt-12",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsx)("h3",{className:"font-bold text-xl mb-4 text-teal-800",children:"Charity Info"}),(0,r.jsx)("p",{className:"text-teal-700 mb-4",children:"Supporting communities and making a difference"}),(0,r.jsxs)("div",{className:"space-y-2",children:[(null==s?void 0:s.phone)&&(0,r.jsxs)("div",{className:"flex items-center text-teal-700 hover:text-teal-900 transition-colors",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{children:s.phone})]}),(null==s?void 0:s.email)&&(0,r.jsxs)("div",{className:"flex items-center text-teal-700 hover:text-teal-900 transition-colors",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("a",{href:"mailto:".concat(s.email),className:"hover:text-teal-900",children:s.email})]}),(null==s?void 0:s.address)&&(0,r.jsxs)("div",{className:"flex items-center text-teal-700 hover:text-teal-900 transition-colors",children:[(0,r.jsx)(c.A,{className:"h-4 w-4 mr-2"}),(0,r.jsx)("span",{children:s.address})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-bold mb-4 text-teal-800",children:"Quick Links"}),(0,r.jsxs)("ul",{className:"space-y-3",children:[(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:"/",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Home"]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:"/news",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"News"]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:"/gallery",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Gallery"]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:"/about",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"About Us"]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:"/contact",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Contact Us"]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:"/search",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Search"]})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-bold mb-4 text-teal-800",children:"Resources"}),(0,r.jsxs)("ul",{className:"space-y-3",children:[(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:"/faq",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"FAQ"]})}),(0,r.jsx)("li",{children:(0,r.jsxs)(l(),{href:"/donate",className:"text-teal-700 hover:text-teal-900 flex items-center transition-colors",onClick:()=>window.scrollTo(0,0),children:[(0,r.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"How to Donate"]})})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 pt-6 border-t border-teal-100 text-center text-teal-700",children:[(0,r.jsxs)("p",{children:["\xa9 ",e," Charity Info. All rights reserved."]}),(0,r.jsxs)("p",{className:"mt-2 flex items-center justify-center text-sm",children:["Built with"," ",(0,r.jsx)(p.A,{className:"h-4 w-4 mx-1 text-pink-500 fill-pink-500"})," for charitable causes"]})]})]})})}},5430:(e,t,s)=>{"use strict";s.d(t,{Header:()=>u});var r=s(5155),a=s(2115),i=s(6874),l=s.n(i),n=s(5695),o=s(7924),c=s(306),d=s(4416),h=s(4783),m=s(285),x=s(2523);function u(){let[e,t]=(0,a.useState)(!1),[s,i]=(0,a.useState)(""),u=(0,n.useRouter)(),f=e=>{e.preventDefault(),s.trim()&&(u.push("/search?q=".concat(encodeURIComponent(s))),i(""),t(!1))};return(0,r.jsxs)("header",{className:"sticky top-0 z-50 bg-charity-muted border-b border-gray-200 shadow-md",children:[(0,r.jsxs)("div",{className:"container mx-auto px-4 py-4 flex items-center justify-between bg-charity-muted",children:[(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)(l(),{href:"/",className:"font-bold text-2xl text-primary mr-4",children:"Charity Info"})}),(0,r.jsxs)("nav",{className:"hidden md:flex items-center space-x-6",children:[(0,r.jsx)(l(),{href:"/news",className:"text-gray-700 hover:text-primary font-medium",children:"News"}),(0,r.jsx)(l(),{href:"/gallery",className:"text-gray-700 hover:text-primary font-medium",children:"Gallery"}),(0,r.jsx)(l(),{href:"/about",className:"text-gray-700 hover:text-primary font-medium",children:"About Us"}),(0,r.jsx)(l(),{href:"/contact",className:"text-gray-700 hover:text-primary font-medium",children:"Contact"}),(0,r.jsxs)("form",{onSubmit:f,className:"relative",children:[(0,r.jsx)(x.p,{type:"search",placeholder:"Search...",className:"w-64 pr-10",value:s,onChange:e=>i(e.target.value)}),(0,r.jsx)(m.$,{type:"submit",size:"icon",variant:"ghost",className:"absolute right-0 top-0 h-full",children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})]}),(0,r.jsx)(m.$,{asChild:!0,children:(0,r.jsxs)(l(),{href:"/login",children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"})," Login"]})})]}),(0,r.jsx)(m.$,{variant:"ghost",size:"icon",className:"md:hidden",onClick:()=>t(!e),children:e?(0,r.jsx)(d.A,{className:"h-6 w-6"}):(0,r.jsx)(h.A,{className:"h-6 w-6"})})]}),e&&(0,r.jsx)("div",{className:"md:hidden bg-charity-muted border-t border-gray-200",children:(0,r.jsxs)("nav",{className:"container mx-auto px-4 py-4 space-y-4",children:[(0,r.jsx)(l(),{href:"/news",className:"block text-gray-700 hover:text-primary font-medium",onClick:()=>t(!1),children:"News"}),(0,r.jsx)(l(),{href:"/gallery",className:"block text-gray-700 hover:text-primary font-medium",onClick:()=>t(!1),children:"Gallery"}),(0,r.jsx)(l(),{href:"/about",className:"block text-gray-700 hover:text-primary font-medium",onClick:()=>t(!1),children:"About Us"}),(0,r.jsx)(l(),{href:"/contact",className:"block text-gray-700 hover:text-primary font-medium",onClick:()=>t(!1),children:"Contact"}),(0,r.jsxs)("form",{onSubmit:f,className:"relative",children:[(0,r.jsx)(x.p,{type:"search",placeholder:"Search...",className:"w-full pr-10",value:s,onChange:e=>i(e.target.value)}),(0,r.jsx)(m.$,{type:"submit",size:"icon",variant:"ghost",className:"absolute right-0 top-0 h-full",children:(0,r.jsx)(o.A,{className:"h-4 w-4"})})]}),(0,r.jsx)(m.$,{asChild:!0,className:"w-full",children:(0,r.jsxs)(l(),{href:"/login",onClick:()=>t(!1),children:[(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4"})," Login"]})})]})})]})}},9250:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,3206,23)),Promise.resolve().then(s.t.bind(s,9324,23)),Promise.resolve().then(s.bind(s,2548)),Promise.resolve().then(s.bind(s,5430)),Promise.resolve().then(s.bind(s,1116)),Promise.resolve().then(s.bind(s,9412))},9324:()=>{},9412:(e,t,s)=>{"use strict";let r;s.d(t,{QueryProvider:()=>o});var a=s(5155),i=s(432),l=s(6715),n=s(9434);function o(e){let{children:t}=e,s=(r||(r=new i.E({defaultOptions:{queries:{staleTime:6e4,gcTime:6e5,retry:(e,t)=>(null==t||!t.status||!(t.status>=400)||!(t.status<500)||!![408,429].includes(t.status))&&e<3,retryDelay:e=>Math.min(1e3*2**e,3e4),refetchOnWindowFocus:!1,refetchOnReconnect:"always"},mutations:{retry:!1,onError:e=>{(0,n.vV)("Mutation error:",e)}}}})),r);return(0,a.jsxs)(l.Ht,{client:s,children:[t,!1]})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i,oH:()=>l,vV:()=>n});var r=s(2596),a=s(9688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}function l(e){return e.replace(/\*\*Tags:\*\*\s*([^<]+)(?=<\/p>|$)/g,(e,t)=>{let s=t.split(",").map(e=>e.trim()).filter(Boolean);if(0===s.length)return e;let r=s.map(e=>'<span class="inline-flex items-center rounded-full bg-teal-100 px-2.5 py-0.5 text-xs font-medium text-teal-800 mr-2 mb-2">'.concat(e,"</span>")).join("");return'<div class="mt-4"><span class="font-semibold">Tags:</span> <div class="flex flex-wrap mt-1">'.concat(r,"</div></div>")})}let n=(e,t)=>{}}},e=>{var t=t=>e(e.s=t);e.O(0,[455,598,967,874,57,441,684,358],()=>t(9250)),_N_E=e.O()}]);